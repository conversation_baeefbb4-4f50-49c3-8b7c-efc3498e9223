define("local_offermanager/app/app-lazy",function(){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function er(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const tt={}.NODE_ENV!=="production"?Object.freeze({}):{},Dn={}.NODE_ENV!=="production"?Object.freeze([]):[],Ot=()=>{},Fg=()=>!1,to=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ni=e=>e.startsWith("onUpdate:"),ft=Object.assign,Ka=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Bg=Object.prototype.hasOwnProperty,Ye=(e,t)=>Bg.call(e,t),me=Array.isArray,Kr=e=>so(e)==="[object Map]",xn=e=>so(e)==="[object Set]",Ic=e=>so(e)==="[object Date]",De=e=>typeof e=="function",ut=e=>typeof e=="string",Is=e=>typeof e=="symbol",Ze=e=>e!==null&&typeof e=="object",Ya=e=>(Ze(e)||De(e))&&De(e.then)&&De(e.catch),Mc=Object.prototype.toString,so=e=>Mc.call(e),Qa=e=>so(e).slice(8,-1),Pc=e=>so(e)==="[object Object]",Za=e=>ut(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ro=er(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),$g=er("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),oi=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},jg=/-(\w)/g,Kt=oi(e=>e.replace(jg,(t,s)=>s?s.toUpperCase():"")),Hg=/\B([A-Z])/g,Sr=oi(e=>e.replace(Hg,"-$1").toLowerCase()),Yr=oi(e=>e.charAt(0).toUpperCase()+e.slice(1)),Qr=oi(e=>e?`on${Yr(e)}`:""),Or=(e,t)=>!Object.is(e,t),Sn=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},ii=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},ai=e=>{const t=parseFloat(e);return isNaN(t)?e:t},qg=e=>{const t=ut(e)?Number(e):NaN;return isNaN(t)?e:t};let kc;const no=()=>kc||(kc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ls(e){if(me(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],o=ut(i)?Kg(i):ls(i);if(o)for(const a in o)t[a]=o[a]}return t}else if(ut(e)||Ze(e))return e}const zg=/;(?![^(]*\))/g,Wg=/:([^]+)/,Gg=/\/\*[^]*?\*\//g;function Kg(e){const t={};return e.replace(Gg,"").split(zg).forEach(s=>{if(s){const i=s.split(Wg);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function he(e){let t="";if(ut(e))t=e;else if(me(e))for(let s=0;s<e.length;s++){const i=he(e[s]);i&&(t+=i+" ")}else if(Ze(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Yg="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",Qg="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",Zg="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",Jg=er(Yg),Xg=er(Qg),e_=er(Zg),t_=er("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Vc(e){return!!e||e===""}function s_(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=oo(e[i],t[i]);return s}function oo(e,t){if(e===t)return!0;let s=Ic(e),i=Ic(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=Is(e),i=Is(t),s||i)return e===t;if(s=me(e),i=me(t),s||i)return s&&i?s_(e,t):!1;if(s=Ze(e),i=Ze(t),s||i){if(!s||!i)return!1;const o=Object.keys(e).length,a=Object.keys(t).length;if(o!==a)return!1;for(const u in e){const c=e.hasOwnProperty(u),h=t.hasOwnProperty(u);if(c&&!h||!c&&h||!oo(e[u],t[u]))return!1}}return String(e)===String(t)}function Ja(e,t){return e.findIndex(s=>oo(s,t))}const Rc=e=>!!(e&&e.__v_isRef===!0),Y=e=>ut(e)?e:e==null?"":me(e)||Ze(e)&&(e.toString===Mc||!De(e.toString))?Rc(e)?Y(e.value):JSON.stringify(e,Lc,2):String(e),Lc=(e,t)=>Rc(t)?Lc(e,t.value):Kr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,o],a)=>(s[Xa(i,a)+" =>"]=o,s),{})}:xn(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>Xa(s))}:Is(t)?Xa(t):Ze(t)&&!me(t)&&!Pc(t)?String(t):t,Xa=(e,t="")=>{var s;return Is(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Hs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let us;class Uc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=us,!t&&us&&(this.index=(us.scopes||(us.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=us;try{return us=this,t()}finally{us=s}}else({}).NODE_ENV!=="production"&&Hs("cannot run an inactive effect scope.")}on(){us=this}off(){us=this.parent}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function r_(e){return new Uc(e)}function n_(){return us}let st;const el=new WeakSet;class Fc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,us&&us.active&&us.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,el.has(this)&&(el.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||$c(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Wc(this),jc(this);const t=st,s=Ms;st=this,Ms=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&st!==this&&Hs("Active effect was not restored correctly - this is likely a Vue internal bug."),Hc(this),st=t,Ms=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)nl(t);this.deps=this.depsTail=void 0,Wc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?el.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){rl(this)&&this.run()}get dirty(){return rl(this)}}let Bc=0,io,ao;function $c(e,t=!1){if(e.flags|=8,t){e.next=ao,ao=e;return}e.next=io,io=e}function tl(){Bc++}function sl(){if(--Bc>0)return;if(ao){let t=ao;for(ao=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;io;){let t=io;for(io=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function jc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Hc(e){let t,s=e.depsTail,i=s;for(;i;){const o=i.prevDep;i.version===-1?(i===s&&(s=o),nl(i),o_(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=o}e.deps=t,e.depsTail=s}function rl(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(qc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function qc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===lo))return;e.globalVersion=lo;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!rl(e)){e.flags&=-3;return}const s=st,i=Ms;st=e,Ms=!0;try{jc(e);const o=e.fn(e._value);(t.version===0||Or(o,e._value))&&(e._value=o,t.version++)}catch(o){throw t.version++,o}finally{st=s,Ms=i,Hc(e),e.flags&=-3}}function nl(e,t=!1){const{dep:s,prevSub:i,nextSub:o}=e;if(i&&(i.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=o),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)nl(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function o_(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ms=!0;const zc=[];function tr(){zc.push(Ms),Ms=!1}function sr(){const e=zc.pop();Ms=e===void 0?!0:e}function Wc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=st;st=void 0;try{t()}finally{st=s}}}let lo=0;class i_{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ol{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!st||!Ms||st===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==st)s=this.activeLink=new i_(st,this),st.deps?(s.prevDep=st.depsTail,st.depsTail.nextDep=s,st.depsTail=s):st.deps=st.depsTail=s,Gc(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=st.depsTail,s.nextDep=void 0,st.depsTail.nextDep=s,st.depsTail=s,st.deps===s&&(st.deps=i)}return{}.NODE_ENV!=="production"&&st.onTrack&&st.onTrack(ft({effect:st},t)),s}trigger(t){this.version++,lo++,this.notify(t)}notify(t){tl();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(ft({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{sl()}}}function Gc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Gc(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const il=new WeakMap,Zr=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),al=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),uo=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function Tt(e,t,s){if(Ms&&st){let i=il.get(e);i||il.set(e,i=new Map);let o=i.get(s);o||(i.set(s,o=new ol),o.map=i,o.key=s),{}.NODE_ENV!=="production"?o.track({target:e,type:t,key:s}):o.track()}}function qs(e,t,s,i,o,a){const u=il.get(e);if(!u){lo++;return}const c=h=>{h&&({}.NODE_ENV!=="production"?h.trigger({target:e,type:t,key:s,newValue:i,oldValue:o,oldTarget:a}):h.trigger())};if(tl(),t==="clear")u.forEach(c);else{const h=me(e),m=h&&Za(s);if(h&&s==="length"){const p=Number(i);u.forEach((_,w)=>{(w==="length"||w===uo||!Is(w)&&w>=p)&&c(_)})}else switch((s!==void 0||u.has(void 0))&&c(u.get(s)),m&&c(u.get(uo)),t){case"add":h?m&&c(u.get("length")):(c(u.get(Zr)),Kr(e)&&c(u.get(al)));break;case"delete":h||(c(u.get(Zr)),Kr(e)&&c(u.get(al)));break;case"set":Kr(e)&&c(u.get(Zr));break}}sl()}function On(e){const t=Ie(e);return t===e?t:(Tt(t,"iterate",uo),Yt(e)?t:t.map(Ht))}function li(e){return Tt(e=Ie(e),"iterate",uo),e}const a_={__proto__:null,[Symbol.iterator](){return ll(this,Symbol.iterator,Ht)},concat(...e){return On(this).concat(...e.map(t=>me(t)?On(t):t))},entries(){return ll(this,"entries",e=>(e[1]=Ht(e[1]),e))},every(e,t){return rr(this,"every",e,t,void 0,arguments)},filter(e,t){return rr(this,"filter",e,t,s=>s.map(Ht),arguments)},find(e,t){return rr(this,"find",e,t,Ht,arguments)},findIndex(e,t){return rr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return rr(this,"findLast",e,t,Ht,arguments)},findLastIndex(e,t){return rr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return rr(this,"forEach",e,t,void 0,arguments)},includes(...e){return ul(this,"includes",e)},indexOf(...e){return ul(this,"indexOf",e)},join(e){return On(this).join(e)},lastIndexOf(...e){return ul(this,"lastIndexOf",e)},map(e,t){return rr(this,"map",e,t,void 0,arguments)},pop(){return co(this,"pop")},push(...e){return co(this,"push",e)},reduce(e,...t){return Kc(this,"reduce",e,t)},reduceRight(e,...t){return Kc(this,"reduceRight",e,t)},shift(){return co(this,"shift")},some(e,t){return rr(this,"some",e,t,void 0,arguments)},splice(...e){return co(this,"splice",e)},toReversed(){return On(this).toReversed()},toSorted(e){return On(this).toSorted(e)},toSpliced(...e){return On(this).toSpliced(...e)},unshift(...e){return co(this,"unshift",e)},values(){return ll(this,"values",Ht)}};function ll(e,t,s){const i=li(e),o=i[t]();return i!==e&&!Yt(e)&&(o._next=o.next,o.next=()=>{const a=o._next();return a.value&&(a.value=s(a.value)),a}),o}const l_=Array.prototype;function rr(e,t,s,i,o,a){const u=li(e),c=u!==e&&!Yt(e),h=u[t];if(h!==l_[t]){const _=h.apply(e,a);return c?Ht(_):_}let m=s;u!==e&&(c?m=function(_,w){return s.call(this,Ht(_),w,e)}:s.length>2&&(m=function(_,w){return s.call(this,_,w,e)}));const p=h.call(u,m,i);return c&&o?o(p):p}function Kc(e,t,s,i){const o=li(e);let a=s;return o!==e&&(Yt(e)?s.length>3&&(a=function(u,c,h){return s.call(this,u,c,h,e)}):a=function(u,c,h){return s.call(this,u,Ht(c),h,e)}),o[t](a,...i)}function ul(e,t,s){const i=Ie(e);Tt(i,"iterate",uo);const o=i[t](...s);return(o===-1||o===!1)&&pi(s[0])?(s[0]=Ie(s[0]),i[t](...s)):o}function co(e,t,s=[]){tr(),tl();const i=Ie(e)[t].apply(e,s);return sl(),sr(),i}const u_=er("__proto__,__v_isRef,__isVue"),Yc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Is));function c_(e){Is(e)||(e=String(e));const t=Ie(this);return Tt(t,"has",e),t.hasOwnProperty(e)}class Qc{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const o=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!o;if(s==="__v_isReadonly")return o;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(o?a?rd:sd:a?td:ed).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=me(t);if(!o){let h;if(u&&(h=a_[s]))return h;if(s==="hasOwnProperty")return c_}const c=Reflect.get(t,s,Dt(t)?t:i);return(Is(s)?Yc.has(s):u_(s))||(o||Tt(t,"get",s),a)?c:Dt(c)?u&&Za(s)?c:c.value:Ze(c)?o?od(c):fi(c):c}}class Zc extends Qc{constructor(t=!1){super(!1,t)}set(t,s,i,o){let a=t[s];if(!this._isShallow){const h=nr(a);if(!Yt(i)&&!nr(i)&&(a=Ie(a),i=Ie(i)),!me(t)&&Dt(a)&&!Dt(i))return h?!1:(a.value=i,!0)}const u=me(t)&&Za(s)?Number(s)<t.length:Ye(t,s),c=Reflect.set(t,s,i,Dt(t)?t:o);return t===Ie(o)&&(u?Or(i,a)&&qs(t,"set",s,i,a):qs(t,"add",s,i)),c}deleteProperty(t,s){const i=Ye(t,s),o=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&qs(t,"delete",s,void 0,o),a}has(t,s){const i=Reflect.has(t,s);return(!Is(s)||!Yc.has(s))&&Tt(t,"has",s),i}ownKeys(t){return Tt(t,"iterate",me(t)?"length":Zr),Reflect.ownKeys(t)}}class Jc extends Qc{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&Hs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&Hs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const d_=new Zc,f_=new Jc,h_=new Zc(!0),p_=new Jc(!0),cl=e=>e,ui=e=>Reflect.getPrototypeOf(e);function m_(e,t,s){return function(...i){const o=this.__v_raw,a=Ie(o),u=Kr(a),c=e==="entries"||e===Symbol.iterator&&u,h=e==="keys"&&u,m=o[e](...i),p=s?cl:t?fl:Ht;return!t&&Tt(a,"iterate",h?al:Zr),{next(){const{value:_,done:w}=m.next();return w?{value:_,done:w}:{value:c?[p(_[0]),p(_[1])]:p(_),done:w}},[Symbol.iterator](){return this}}}}function ci(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";Hs(`${Yr(e)} operation ${s}failed: target is readonly.`,Ie(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function g_(e,t){const s={get(o){const a=this.__v_raw,u=Ie(a),c=Ie(o);e||(Or(o,c)&&Tt(u,"get",o),Tt(u,"get",c));const{has:h}=ui(u),m=t?cl:e?fl:Ht;if(h.call(u,o))return m(a.get(o));if(h.call(u,c))return m(a.get(c));a!==u&&a.get(o)},get size(){const o=this.__v_raw;return!e&&Tt(Ie(o),"iterate",Zr),Reflect.get(o,"size",o)},has(o){const a=this.__v_raw,u=Ie(a),c=Ie(o);return e||(Or(o,c)&&Tt(u,"has",o),Tt(u,"has",c)),o===c?a.has(o):a.has(o)||a.has(c)},forEach(o,a){const u=this,c=u.__v_raw,h=Ie(c),m=t?cl:e?fl:Ht;return!e&&Tt(h,"iterate",Zr),c.forEach((p,_)=>o.call(a,m(p),m(_),u))}};return ft(s,e?{add:ci("add"),set:ci("set"),delete:ci("delete"),clear:ci("clear")}:{add(o){!t&&!Yt(o)&&!nr(o)&&(o=Ie(o));const a=Ie(this);return ui(a).has.call(a,o)||(a.add(o),qs(a,"add",o,o)),this},set(o,a){!t&&!Yt(a)&&!nr(a)&&(a=Ie(a));const u=Ie(this),{has:c,get:h}=ui(u);let m=c.call(u,o);m?{}.NODE_ENV!=="production"&&Xc(u,c,o):(o=Ie(o),m=c.call(u,o));const p=h.call(u,o);return u.set(o,a),m?Or(a,p)&&qs(u,"set",o,a,p):qs(u,"add",o,a),this},delete(o){const a=Ie(this),{has:u,get:c}=ui(a);let h=u.call(a,o);h?{}.NODE_ENV!=="production"&&Xc(a,u,o):(o=Ie(o),h=u.call(a,o));const m=c?c.call(a,o):void 0,p=a.delete(o);return h&&qs(a,"delete",o,void 0,m),p},clear(){const o=Ie(this),a=o.size!==0,u={}.NODE_ENV!=="production"?Kr(o)?new Map(o):new Set(o):void 0,c=o.clear();return a&&qs(o,"clear",void 0,void 0,u),c}}),["keys","values","entries",Symbol.iterator].forEach(o=>{s[o]=m_(o,e,t)}),s}function di(e,t){const s=g_(e,t);return(i,o,a)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?i:Reflect.get(Ye(s,o)&&o in i?s:i,o,a)}const __={get:di(!1,!1)},v_={get:di(!1,!0)},y_={get:di(!0,!1)},b_={get:di(!0,!0)};function Xc(e,t,s){const i=Ie(s);if(i!==s&&t.call(e,i)){const o=Qa(e);Hs(`Reactive ${o} contains both the raw and reactive versions of the same object${o==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const ed=new WeakMap,td=new WeakMap,sd=new WeakMap,rd=new WeakMap;function w_(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function E_(e){return e.__v_skip||!Object.isExtensible(e)?0:w_(Qa(e))}function fi(e){return nr(e)?e:hi(e,!1,d_,__,ed)}function nd(e){return hi(e,!1,h_,v_,td)}function od(e){return hi(e,!0,f_,y_,sd)}function zs(e){return hi(e,!0,p_,b_,rd)}function hi(e,t,s,i,o){if(!Ze(e))return{}.NODE_ENV!=="production"&&Hs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=o.get(e);if(a)return a;const u=E_(e);if(u===0)return e;const c=new Proxy(e,u===2?i:s);return o.set(e,c),c}function Jr(e){return nr(e)?Jr(e.__v_raw):!!(e&&e.__v_isReactive)}function nr(e){return!!(e&&e.__v_isReadonly)}function Yt(e){return!!(e&&e.__v_isShallow)}function pi(e){return e?!!e.__v_raw:!1}function Ie(e){const t=e&&e.__v_raw;return t?Ie(t):e}function dl(e){return!Ye(e,"__v_skip")&&Object.isExtensible(e)&&ii(e,"__v_skip",!0),e}const Ht=e=>Ze(e)?fi(e):e,fl=e=>Ze(e)?od(e):e;function Dt(e){return e?e.__v_isRef===!0:!1}function id(e){return ad(e,!1)}function C_(e){return ad(e,!0)}function ad(e,t){return Dt(e)?e:new D_(e,t)}class D_{constructor(t,s){this.dep=new ol,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Ie(t),this._value=s?t:Ht(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||Yt(t)||nr(t);t=i?t:Ie(t),Or(t,s)&&(this._rawValue=t,this._value=i?t:Ht(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function Tr(e){return Dt(e)?e.value:e}const x_={get:(e,t,s)=>t==="__v_raw"?e:Tr(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const o=e[t];return Dt(o)&&!Dt(s)?(o.value=s,!0):Reflect.set(e,t,s,i)}};function ld(e){return Jr(e)?e:new Proxy(e,x_)}class S_{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new ol(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=lo-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&st!==this)return $c(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return qc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&Hs("Write operation failed: computed value is readonly")}}function O_(e,t,s=!1){let i,o;De(e)?i=e:(i=e.get,o=e.set);const a=new S_(i,o,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const mi={},gi=new WeakMap;let Xr;function T_(e,t=!1,s=Xr){if(s){let i=gi.get(s);i||gi.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&Hs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function N_(e,t,s=tt){const{immediate:i,deep:o,once:a,scheduler:u,augmentJob:c,call:h}=s,m=Z=>{(s.onWarn||Hs)("Invalid watch source: ",Z,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},p=Z=>o?Z:Yt(Z)||o===!1||o===0?or(Z,1):or(Z);let _,w,D,k,F=!1,te=!1;if(Dt(e)?(w=()=>e.value,F=Yt(e)):Jr(e)?(w=()=>p(e),F=!0):me(e)?(te=!0,F=e.some(Z=>Jr(Z)||Yt(Z)),w=()=>e.map(Z=>{if(Dt(Z))return Z.value;if(Jr(Z))return p(Z);if(De(Z))return h?h(Z,2):Z();({}).NODE_ENV!=="production"&&m(Z)})):De(e)?t?w=h?()=>h(e,2):e:w=()=>{if(D){tr();try{D()}finally{sr()}}const Z=Xr;Xr=_;try{return h?h(e,3,[k]):e(k)}finally{Xr=Z}}:(w=Ot,{}.NODE_ENV!=="production"&&m(e)),t&&o){const Z=w,fe=o===!0?1/0:o;w=()=>or(Z(),fe)}const A=n_(),se=()=>{_.stop(),A&&A.active&&Ka(A.effects,_)};if(a&&t){const Z=t;t=(...fe)=>{Z(...fe),se()}}let G=te?new Array(e.length).fill(mi):mi;const ye=Z=>{if(!(!(_.flags&1)||!_.dirty&&!Z))if(t){const fe=_.run();if(o||F||(te?fe.some((ve,Ae)=>Or(ve,G[Ae])):Or(fe,G))){D&&D();const ve=Xr;Xr=_;try{const Ae=[fe,G===mi?void 0:te&&G[0]===mi?[]:G,k];h?h(t,3,Ae):t(...Ae),G=fe}finally{Xr=ve}}}else _.run()};return c&&c(ye),_=new Fc(w),_.scheduler=u?()=>u(ye,!1):ye,k=Z=>T_(Z,!1,_),D=_.onStop=()=>{const Z=gi.get(_);if(Z){if(h)h(Z,4);else for(const fe of Z)fe();gi.delete(_)}},{}.NODE_ENV!=="production"&&(_.onTrack=s.onTrack,_.onTrigger=s.onTrigger),t?i?ye(!0):G=_.run():u?u(ye.bind(null,!0),!0):_.run(),se.pause=_.pause.bind(_),se.resume=_.resume.bind(_),se.stop=se,se}function or(e,t=1/0,s){if(t<=0||!Ze(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Dt(e))or(e.value,t,s);else if(me(e))for(let i=0;i<e.length;i++)or(e[i],t,s);else if(xn(e)||Kr(e))e.forEach(i=>{or(i,t,s)});else if(Pc(e)){for(const i in e)or(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&or(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const en=[];function _i(e){en.push(e)}function vi(){en.pop()}let hl=!1;function Q(e,...t){if(hl)return;hl=!0,tr();const s=en.length?en[en.length-1].component:null,i=s&&s.appContext.config.warnHandler,o=A_();if(i)Tn(i,s,11,[e+t.map(a=>{var u,c;return(c=(u=a.toString)==null?void 0:u.call(a))!=null?c:JSON.stringify(a)}).join(""),s&&s.proxy,o.map(({vnode:a})=>`at <${Ui(s,a.type)}>`).join(`
`),o]);else{const a=[`[Vue warn]: ${e}`,...t];o.length&&a.push(`
`,...I_(o)),console.warn(...a)}sr(),hl=!1}function A_(){let e=en[en.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function I_(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...M_(s))}),t}function M_({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,o=` at <${Ui(e.component,e.type,i)}`,a=">"+s;return e.props?[o,...P_(e.props),a]:[o+a]}function P_(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...ud(i,e[i]))}),s.length>3&&t.push(" ..."),t}function ud(e,t,s){return ut(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:Dt(t)?(t=ud(e,Ie(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):De(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Ie(t),s?t:[`${e}=`,t])}function k_(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?Q(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&Q(`${t} is NaN - the duration expression might be incorrect.`))}const pl={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function Tn(e,t,s,i){try{return i?e(...i):e()}catch(o){fo(o,t,s)}}function Ps(e,t,s,i){if(De(e)){const o=Tn(e,t,s,i);return o&&Ya(o)&&o.catch(a=>{fo(a,t,s)}),o}if(me(e)){const o=[];for(let a=0;a<e.length;a++)o.push(Ps(e[a],t,s,i));return o}else({}).NODE_ENV!=="production"&&Q(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function fo(e,t,s,i=!0){const o=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||tt;if(t){let c=t.parent;const h=t.proxy,m={}.NODE_ENV!=="production"?pl[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const p=c.ec;if(p){for(let _=0;_<p.length;_++)if(p[_](e,h,m)===!1)return}c=c.parent}if(a){tr(),Tn(a,null,10,[e,h,m]),sr();return}}V_(e,s,o,i,u)}function V_(e,t,s,i=!0,o=!1){if({}.NODE_ENV!=="production"){const a=pl[t];if(s&&_i(s),Q(`Unhandled error${a?` during execution of ${a}`:""}`),s&&vi(),i)throw e;console.error(e)}else{if(o)throw e;console.error(e)}}const Qt=[];let Ws=-1;const Nn=[];let Nr=null,An=0;const cd=Promise.resolve();let yi=null;const R_=100;function ml(e){const t=yi||cd;return e?t.then(this?e.bind(this):e):t}function L_(e){let t=Ws+1,s=Qt.length;for(;t<s;){const i=t+s>>>1,o=Qt[i],a=ho(o);a<e||a===e&&o.flags&2?t=i+1:s=i}return t}function bi(e){if(!(e.flags&1)){const t=ho(e),s=Qt[Qt.length-1];!s||!(e.flags&2)&&t>=ho(s)?Qt.push(e):Qt.splice(L_(t),0,e),e.flags|=1,dd()}}function dd(){yi||(yi=cd.then(md))}function fd(e){me(e)?Nn.push(...e):Nr&&e.id===-1?Nr.splice(An+1,0,e):e.flags&1||(Nn.push(e),e.flags|=1),dd()}function hd(e,t,s=Ws+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<Qt.length;s++){const i=Qt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&gl(t,i))continue;Qt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function pd(e){if(Nn.length){const t=[...new Set(Nn)].sort((s,i)=>ho(s)-ho(i));if(Nn.length=0,Nr){Nr.push(...t);return}for(Nr=t,{}.NODE_ENV!=="production"&&(e=e||new Map),An=0;An<Nr.length;An++){const s=Nr[An];({}).NODE_ENV!=="production"&&gl(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}Nr=null,An=0}}const ho=e=>e.id==null?e.flags&2?-1:1/0:e.id;function md(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>gl(e,s):Ot;try{for(Ws=0;Ws<Qt.length;Ws++){const s=Qt[Ws];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),Tn(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;Ws<Qt.length;Ws++){const s=Qt[Ws];s&&(s.flags&=-2)}Ws=-1,Qt.length=0,pd(e),yi=null,(Qt.length||Nn.length)&&md(e)}}function gl(e,t){const s=e.get(t)||0;if(s>R_){const i=t.i,o=i&&jl(i.type);return fo(`Maximum recursive updates exceeded${o?` in component <${o}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let ks=!1;const wi=new Map;({}).NODE_ENV!=="production"&&(no().__VUE_HMR_RUNTIME__={createRecord:_l(gd),rerender:_l(B_),reload:_l($_)});const tn=new Map;function U_(e){const t=e.type.__hmrId;let s=tn.get(t);s||(gd(t,e.type),s=tn.get(t)),s.instances.add(e)}function F_(e){tn.get(e.type.__hmrId).instances.delete(e)}function gd(e,t){return tn.has(e)?!1:(tn.set(e,{initialDef:Ei(t),instances:new Set}),!0)}function Ei(e){return Nf(e)?e.__vccOpts:e}function B_(e,t){const s=tn.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,Ei(i.type).render=t),i.renderCache=[],ks=!0,i.update(),ks=!1}))}function $_(e,t){const s=tn.get(e);if(!s)return;t=Ei(t),_d(s.initialDef,t);const i=[...s.instances];for(let o=0;o<i.length;o++){const a=i[o],u=Ei(a.type);let c=wi.get(u);c||(u!==s.initialDef&&_d(u,t),wi.set(u,c=new Set)),c.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(c.add(a),a.ceReload(t.styles),c.delete(a)):a.parent?bi(()=>{ks=!0,a.parent.update(),ks=!1,c.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}fd(()=>{wi.clear()})}function _d(e,t){ft(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function _l(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Gs,po=[],vl=!1;function mo(e,...t){Gs?Gs.emit(e,...t):vl||po.push({event:e,args:t})}function vd(e,t){var s,i;Gs=e,Gs?(Gs.enabled=!0,po.forEach(({event:o,args:a})=>Gs.emit(o,...a)),po=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{vd(a,t)}),setTimeout(()=>{Gs||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,vl=!0,po=[])},3e3)):(vl=!0,po=[])}function j_(e,t){mo("app:init",e,t,{Fragment:Re,Text:wo,Comment:wt,Static:Eo})}function H_(e){mo("app:unmount",e)}const q_=yl("component:added"),yd=yl("component:updated"),z_=yl("component:removed"),W_=e=>{Gs&&typeof Gs.cleanupBuffer=="function"&&!Gs.cleanupBuffer(e)&&z_(e)};/*! #__NO_SIDE_EFFECTS__ */function yl(e){return t=>{mo(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const G_=bd("perf:start"),K_=bd("perf:end");function bd(e){return(t,s,i)=>{mo(e,t.appContext.app,t.uid,t,s,i)}}function Y_(e,t,s){mo("component:emit",e.appContext.app,e,t,s)}let xt=null,wd=null;function Ci(e){const t=xt;return xt=e,wd=e&&e.type.__scopeId||null,t}function Ne(e,t=xt,s){if(!t||e._n)return e;const i=(...o)=>{i._d&&yf(-1);const a=Ci(t);let u;try{u=e(...o)}finally{Ci(a),i._d&&yf(1)}return{}.NODE_ENV!=="production"&&yd(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function Ed(e){$g(e)&&Q("Do not use built-in directive ids as custom directive id: "+e)}function mt(e,t){if(xt===null)return{}.NODE_ENV!=="production"&&Q("withDirectives can only be used inside render functions."),e;const s=Li(xt),i=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[a,u,c,h=tt]=t[o];a&&(De(a)&&(a={mounted:a,updated:a}),a.deep&&or(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:c,modifiers:h}))}return e}function sn(e,t,s,i){const o=e.dirs,a=t&&t.dirs;for(let u=0;u<o.length;u++){const c=o[u];a&&(c.oldValue=a[u].value);let h=c.dir[i];h&&(tr(),Ps(h,s,8,[e.el,c,e,t]),sr())}}const Cd=Symbol("_vte"),Dd=e=>e.__isTeleport,rn=e=>e&&(e.disabled||e.disabled===""),xd=e=>e&&(e.defer||e.defer===""),Sd=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Od=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,bl=(e,t)=>{const s=e&&e.to;if(ut(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!rn(e)&&Q(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&Q("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!rn(e)&&Q(`Invalid Teleport target: ${s}`),s},Td={name:"Teleport",__isTeleport:!0,process(e,t,s,i,o,a,u,c,h,m){const{mc:p,pc:_,pbc:w,o:{insert:D,querySelector:k,createText:F,createComment:te}}=m,A=rn(t.props);let{shapeFlag:se,children:G,dynamicChildren:ye}=t;if({}.NODE_ENV!=="production"&&ks&&(h=!1,ye=null),e==null){const Z=t.el={}.NODE_ENV!=="production"?te("teleport start"):F(""),fe=t.anchor={}.NODE_ENV!=="production"?te("teleport end"):F("");D(Z,s,i),D(fe,s,i);const ve=(ie,V)=>{se&16&&(o&&o.isCE&&(o.ce._teleportTarget=ie),p(G,ie,V,o,a,u,c,h))},Ae=()=>{const ie=t.target=bl(t.props,k),V=Nd(ie,t,F,D);ie?(u!=="svg"&&Sd(ie)?u="svg":u!=="mathml"&&Od(ie)&&(u="mathml"),A||(ve(ie,V),xi(t,!1))):{}.NODE_ENV!=="production"&&!A&&Q("Invalid Teleport target on mount:",ie,`(${typeof ie})`)};A&&(ve(s,fe),xi(t,!0)),xd(t.props)?Jt(()=>{Ae(),t.el.__isMounted=!0},a):Ae()}else{if(xd(t.props)&&!e.el.__isMounted){Jt(()=>{Td.process(e,t,s,i,o,a,u,c,h,m),delete e.el.__isMounted},a);return}t.el=e.el,t.targetStart=e.targetStart;const Z=t.anchor=e.anchor,fe=t.target=e.target,ve=t.targetAnchor=e.targetAnchor,Ae=rn(e.props),ie=Ae?s:fe,V=Ae?Z:ve;if(u==="svg"||Sd(fe)?u="svg":(u==="mathml"||Od(fe))&&(u="mathml"),ye?(w(e.dynamicChildren,ye,ie,o,a,u,c),bo(e,t,!0)):h||_(e,t,ie,V,o,a,u,c,!1),A)Ae?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Di(t,s,Z,m,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const Te=t.target=bl(t.props,k);Te?Di(t,Te,null,m,0):{}.NODE_ENV!=="production"&&Q("Invalid Teleport target on update:",fe,`(${typeof fe})`)}else Ae&&Di(t,fe,ve,m,1);xi(t,A)}},remove(e,t,s,{um:i,o:{remove:o}},a){const{shapeFlag:u,children:c,anchor:h,targetStart:m,targetAnchor:p,target:_,props:w}=e;if(_&&(o(m),o(p)),a&&o(h),u&16){const D=a||!rn(w);for(let k=0;k<c.length;k++){const F=c[k];i(F,t,s,D,!!F.dynamicChildren)}}},move:Di,hydrate:Q_};function Di(e,t,s,{o:{insert:i},m:o},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:c,shapeFlag:h,children:m,props:p}=e,_=a===2;if(_&&i(u,t,s),(!_||rn(p))&&h&16)for(let w=0;w<m.length;w++)o(m[w],t,s,2);_&&i(c,t,s)}function Q_(e,t,s,i,o,a,{o:{nextSibling:u,parentNode:c,querySelector:h,insert:m,createText:p}},_){const w=t.target=bl(t.props,h);if(w){const D=rn(t.props),k=w._lpa||w.firstChild;if(t.shapeFlag&16)if(D)t.anchor=_(u(e),t,c(e),s,i,o,a),t.targetStart=k,t.targetAnchor=k&&u(k);else{t.anchor=u(e);let F=k;for(;F;){if(F&&F.nodeType===8){if(F.data==="teleport start anchor")t.targetStart=F;else if(F.data==="teleport anchor"){t.targetAnchor=F,w._lpa=t.targetAnchor&&u(t.targetAnchor);break}}F=u(F)}t.targetAnchor||Nd(w,t,p,m),_(k&&u(k),t,w,s,i,o,a)}xi(t,D)}return t.anchor&&u(t.anchor)}const Z_=Td;function xi(e,t){const s=e.ctx;if(s&&s.ut){let i,o;for(t?(i=e.el,o=e.anchor):(i=e.targetStart,o=e.targetAnchor);i&&i!==o;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function Nd(e,t,s,i){const o=t.targetStart=s(""),a=t.targetAnchor=s("");return o[Cd]=a,e&&(i(o,e),i(a,e)),a}const Ar=Symbol("_leaveCb"),Si=Symbol("_enterCb");function J_(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Fd(()=>{e.isMounted=!0}),Bd(()=>{e.isUnmounting=!0}),e}const bs=[Function,Array],Ad={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:bs,onEnter:bs,onAfterEnter:bs,onEnterCancelled:bs,onBeforeLeave:bs,onLeave:bs,onAfterLeave:bs,onLeaveCancelled:bs,onBeforeAppear:bs,onAppear:bs,onAfterAppear:bs,onAppearCancelled:bs},Id=e=>{const t=e.subTree;return t.component?Id(t.component):t},X_={name:"BaseTransition",props:Ad,setup(e,{slots:t}){const s=Vi(),i=J_();return()=>{const o=t.default&&Vd(t.default(),!0);if(!o||!o.length)return;const a=Md(o),u=Ie(e),{mode:c}=u;if({}.NODE_ENV!=="production"&&c&&c!=="in-out"&&c!=="out-in"&&c!=="default"&&Q(`invalid <transition> mode: ${c}`),i.isLeaving)return El(a);const h=kd(a);if(!h)return El(a);let m=wl(h,u,i,s,_=>m=_);h.type!==wt&&go(h,m);let p=s.subTree&&kd(s.subTree);if(p&&p.type!==wt&&!ln(h,p)&&Id(s).type!==wt){let _=wl(p,u,i,s);if(go(p,_),c==="out-in"&&h.type!==wt)return i.isLeaving=!0,_.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete _.afterLeave,p=void 0},El(a);c==="in-out"&&h.type!==wt?_.delayLeave=(w,D,k)=>{const F=Pd(i,p);F[String(p.key)]=p,w[Ar]=()=>{D(),w[Ar]=void 0,delete m.delayedLeave,p=void 0},m.delayedLeave=()=>{k(),delete m.delayedLeave,p=void 0}}:p=void 0}else p&&(p=void 0);return a}}};function Md(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==wt){if({}.NODE_ENV!=="production"&&s){Q("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const ev=X_;function Pd(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function wl(e,t,s,i,o){const{appear:a,mode:u,persisted:c=!1,onBeforeEnter:h,onEnter:m,onAfterEnter:p,onEnterCancelled:_,onBeforeLeave:w,onLeave:D,onAfterLeave:k,onLeaveCancelled:F,onBeforeAppear:te,onAppear:A,onAfterAppear:se,onAppearCancelled:G}=t,ye=String(e.key),Z=Pd(s,e),fe=(ie,V)=>{ie&&Ps(ie,i,9,V)},ve=(ie,V)=>{const Te=V[1];fe(ie,V),me(ie)?ie.every(le=>le.length<=1)&&Te():ie.length<=1&&Te()},Ae={mode:u,persisted:c,beforeEnter(ie){let V=h;if(!s.isMounted)if(a)V=te||h;else return;ie[Ar]&&ie[Ar](!0);const Te=Z[ye];Te&&ln(e,Te)&&Te.el[Ar]&&Te.el[Ar](),fe(V,[ie])},enter(ie){let V=m,Te=p,le=_;if(!s.isMounted)if(a)V=A||m,Te=se||p,le=G||_;else return;let Ge=!1;const gt=ie[Si]=ht=>{Ge||(Ge=!0,ht?fe(le,[ie]):fe(Te,[ie]),Ae.delayedLeave&&Ae.delayedLeave(),ie[Si]=void 0)};V?ve(V,[ie,gt]):gt()},leave(ie,V){const Te=String(e.key);if(ie[Si]&&ie[Si](!0),s.isUnmounting)return V();fe(w,[ie]);let le=!1;const Ge=ie[Ar]=gt=>{le||(le=!0,V(),gt?fe(F,[ie]):fe(k,[ie]),ie[Ar]=void 0,Z[Te]===e&&delete Z[Te])};Z[Te]=e,D?ve(D,[ie,Ge]):Ge()},clone(ie){const V=wl(ie,t,s,i,o);return o&&o(V),V}};return Ae}function El(e){if(_o(e))return e=Ks(e),e.children=null,e}function kd(e){if(!_o(e))return Dd(e.type)&&e.children?Md(e.children):e;if({}.NODE_ENV!=="production"&&e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&De(s.default))return s.default()}}function go(e,t){e.shapeFlag&6&&e.component?(e.transition=t,go(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Vd(e,t=!1,s){let i=[],o=0;for(let a=0;a<e.length;a++){let u=e[a];const c=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===Re?(u.patchFlag&128&&o++,i=i.concat(Vd(u.children,t,c))):(t||u.type!==wt)&&i.push(c!=null?Ks(u,{key:c}):u)}if(o>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function Rd(e,t){return De(e)?(()=>ft({name:e.name},t,{setup:e}))():e}function Ld(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const tv=new WeakSet;function Oi(e,t,s,i,o=!1){if(me(e)){e.forEach((k,F)=>Oi(k,t&&(me(t)?t[F]:t),s,i,o));return}if(In(i)&&!o){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&Oi(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Li(i.component):i.el,u=o?null:a,{i:c,r:h}=e;if({}.NODE_ENV!=="production"&&!c){Q("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const m=t&&t.r,p=c.refs===tt?c.refs={}:c.refs,_=c.setupState,w=Ie(_),D=_===tt?()=>!1:k=>({}).NODE_ENV!=="production"&&(Ye(w,k)&&!Dt(w[k])&&Q(`Template ref "${k}" used on a non-ref value. It will not work in the production build.`),tv.has(w[k]))?!1:Ye(w,k);if(m!=null&&m!==h&&(ut(m)?(p[m]=null,D(m)&&(_[m]=null)):Dt(m)&&(m.value=null)),De(h))Tn(h,c,12,[u,p]);else{const k=ut(h),F=Dt(h);if(k||F){const te=()=>{if(e.f){const A=k?D(h)?_[h]:p[h]:h.value;o?me(A)&&Ka(A,a):me(A)?A.includes(a)||A.push(a):k?(p[h]=[a],D(h)&&(_[h]=p[h])):(h.value=[a],e.k&&(p[e.k]=h.value))}else k?(p[h]=u,D(h)&&(_[h]=u)):F?(h.value=u,e.k&&(p[e.k]=u)):{}.NODE_ENV!=="production"&&Q("Invalid template ref type:",h,`(${typeof h})`)};u?(te.id=-1,Jt(te,s)):te()}else({}).NODE_ENV!=="production"&&Q("Invalid template ref type:",h,`(${typeof h})`)}}no().requestIdleCallback,no().cancelIdleCallback;const In=e=>!!e.type.__asyncLoader,_o=e=>e.type.__isKeepAlive;function sv(e,t){Ud(e,"a",t)}function rv(e,t){Ud(e,"da",t)}function Ud(e,t,s=Nt){const i=e.__wdc||(e.__wdc=()=>{let o=s;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Ti(t,i,s),s){let o=s.parent;for(;o&&o.parent;)_o(o.parent.vnode)&&nv(i,t,s,o),o=o.parent}}function nv(e,t,s,i){const o=Ti(t,e,i,!0);$d(()=>{Ka(i[t],o)},s)}function Ti(e,t,s=Nt,i=!1){if(s){const o=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{tr();const c=xo(s),h=Ps(t,s,e,u);return c(),sr(),h});return i?o.unshift(a):o.push(a),a}else if({}.NODE_ENV!=="production"){const o=Qr(pl[e].replace(/ hook$/,""));Q(`${o} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const ir=e=>(t,s=Nt)=>{(!So||e==="sp")&&Ti(e,(...i)=>t(...i),s)},ov=ir("bm"),Fd=ir("m"),iv=ir("bu"),av=ir("u"),Bd=ir("bum"),$d=ir("um"),lv=ir("sp"),uv=ir("rtg"),cv=ir("rtc");function dv(e,t=Nt){Ti("ec",e,t)}const Cl="components",fv="directives";function ee(e,t){return jd(Cl,e,!0,t)||e}const hv=Symbol.for("v-ndc");function pv(e){return jd(fv,e)}function jd(e,t,s=!0,i=!1){const o=xt||Nt;if(o){const a=o.type;if(e===Cl){const c=jl(a,!1);if(c&&(c===t||c===Kt(t)||c===Yr(Kt(t))))return a}const u=Hd(o[e]||a[e],t)||Hd(o.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const c=e===Cl?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";Q(`Failed to resolve ${e.slice(0,-1)}: ${t}${c}`)}return u}else({}).NODE_ENV!=="production"&&Q(`resolve${Yr(e.slice(0,-1))} can only be used in render() or setup().`)}function Hd(e,t){return e&&(e[t]||e[Kt(t)]||e[Yr(Kt(t))])}function vt(e,t,s,i){let o;const a=s&&s[i],u=me(e);if(u||ut(e)){const c=u&&Jr(e);let h=!1;c&&(h=!Yt(e),e=li(e)),o=new Array(e.length);for(let m=0,p=e.length;m<p;m++)o[m]=t(h?Ht(e[m]):e[m],m,void 0,a&&a[m])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&Q(`The v-for range expect an integer value but got ${e}.`),o=new Array(e);for(let c=0;c<e;c++)o[c]=t(c+1,c,void 0,a&&a[c])}else if(Ze(e))if(e[Symbol.iterator])o=Array.from(e,(c,h)=>t(c,h,void 0,a&&a[h]));else{const c=Object.keys(e);o=new Array(c.length);for(let h=0,m=c.length;h<m;h++){const p=c[h];o[h]=t(e[p],p,h,a&&a[h])}}else o=[];return s&&(s[i]=o),o}function Vt(e,t,s={},i,o){if(xt.ce||xt.parent&&In(xt.parent)&&xt.parent.ce)return t!=="default"&&(s.name=t),O(),Rt(Re,null,[P("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(Q("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),O();const u=a&&qd(a(s)),c=s.key||u&&u.key,h=Rt(Re,{key:(c&&!Is(c)?c:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!o&&h.scopeId&&(h.slotScopeIds=[h.scopeId+"-s"]),a&&a._c&&(a._d=!0),h}function qd(e){return e.some(t=>an(t)?!(t.type===wt||t.type===Re&&!qd(t.children)):!0)?e:null}const Dl=e=>e?xf(e)?Li(e):Dl(e.parent):null,nn=ft(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?zs(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?zs(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?zs(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?zs(e.refs):e.refs,$parent:e=>Dl(e.parent),$root:e=>Dl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Tl(e),$forceUpdate:e=>e.f||(e.f=()=>{bi(e.update)}),$nextTick:e=>e.n||(e.n=ml.bind(e.proxy)),$watch:e=>Gv.bind(e)}),xl=e=>e==="_"||e==="$",Sl=(e,t)=>e!==tt&&!e.__isScriptSetup&&Ye(e,t),zd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:o,props:a,accessCache:u,type:c,appContext:h}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let m;if(t[0]!=="$"){const D=u[t];if(D!==void 0)switch(D){case 1:return i[t];case 2:return o[t];case 4:return s[t];case 3:return a[t]}else{if(Sl(i,t))return u[t]=1,i[t];if(o!==tt&&Ye(o,t))return u[t]=2,o[t];if((m=e.propsOptions[0])&&Ye(m,t))return u[t]=3,a[t];if(s!==tt&&Ye(s,t))return u[t]=4,s[t];Ol&&(u[t]=0)}}const p=nn[t];let _,w;if(p)return t==="$attrs"?(Tt(e.attrs,"get",""),{}.NODE_ENV!=="production"&&Pi()):{}.NODE_ENV!=="production"&&t==="$slots"&&Tt(e,"get",t),p(e);if((_=c.__cssModules)&&(_=_[t]))return _;if(s!==tt&&Ye(s,t))return u[t]=4,s[t];if(w=h.config.globalProperties,Ye(w,t))return w[t];({}).NODE_ENV!=="production"&&xt&&(!ut(t)||t.indexOf("__v")!==0)&&(o!==tt&&xl(t[0])&&Ye(o,t)?Q(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===xt&&Q(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:o,ctx:a}=e;return Sl(o,t)?(o[t]=s,!0):{}.NODE_ENV!=="production"&&o.__isScriptSetup&&Ye(o,t)?(Q(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==tt&&Ye(i,t)?(i[t]=s,!0):Ye(e.props,t)?({}.NODE_ENV!=="production"&&Q(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&Q(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:o,propsOptions:a}},u){let c;return!!s[u]||e!==tt&&Ye(e,u)||Sl(t,u)||(c=a[0])&&Ye(c,u)||Ye(i,u)||Ye(nn,u)||Ye(o.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:Ye(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(zd.ownKeys=e=>(Q("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function mv(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(nn).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>nn[s](e),set:Ot})}),t}function gv(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:Ot})})}function _v(e){const{ctx:t,setupState:s}=e;Object.keys(Ie(s)).forEach(i=>{if(!s.__isScriptSetup){if(xl(i[0])){Q(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:Ot})}})}function Wd(e){return me(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function vv(){const e=Object.create(null);return(t,s)=>{e[s]?Q(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Ol=!0;function yv(e){const t=Tl(e),s=e.proxy,i=e.ctx;Ol=!1,t.beforeCreate&&Gd(t.beforeCreate,e,"bc");const{data:o,computed:a,methods:u,watch:c,provide:h,inject:m,created:p,beforeMount:_,mounted:w,beforeUpdate:D,updated:k,activated:F,deactivated:te,beforeDestroy:A,beforeUnmount:se,destroyed:G,unmounted:ye,render:Z,renderTracked:fe,renderTriggered:ve,errorCaptured:Ae,serverPrefetch:ie,expose:V,inheritAttrs:Te,components:le,directives:Ge,filters:gt}=t,ht={}.NODE_ENV!=="production"?vv():null;if({}.NODE_ENV!=="production"){const[xe]=e.propsOptions;if(xe)for(const be in xe)ht("Props",be)}if(m&&bv(m,i,ht),u)for(const xe in u){const be=u[xe];De(be)?({}.NODE_ENV!=="production"?Object.defineProperty(i,xe,{value:be.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[xe]=be.bind(s),{}.NODE_ENV!=="production"&&ht("Methods",xe)):{}.NODE_ENV!=="production"&&Q(`Method "${xe}" has type "${typeof be}" in the component definition. Did you reference the function correctly?`)}if(o){({}).NODE_ENV!=="production"&&!De(o)&&Q("The data option must be a function. Plain object usage is no longer supported.");const xe=o.call(s,s);if({}.NODE_ENV!=="production"&&Ya(xe)&&Q("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Ze(xe))({}).NODE_ENV!=="production"&&Q("data() should return an object.");else if(e.data=fi(xe),{}.NODE_ENV!=="production")for(const be in xe)ht("Data",be),xl(be[0])||Object.defineProperty(i,be,{configurable:!0,enumerable:!0,get:()=>xe[be],set:Ot})}if(Ol=!0,a)for(const xe in a){const be=a[xe],Ut=De(be)?be.bind(s,s):De(be.get)?be.get.bind(s,s):Ot;({}).NODE_ENV!=="production"&&Ut===Ot&&Q(`Computed property "${xe}" has no getter.`);const Xt=!De(be)&&De(be.set)?be.set.bind(s):{}.NODE_ENV!=="production"?()=>{Q(`Write operation failed: computed property "${xe}" is readonly.`)}:Ot,yt=Ls({get:Ut,set:Xt});Object.defineProperty(i,xe,{enumerable:!0,configurable:!0,get:()=>yt.value,set:ce=>yt.value=ce}),{}.NODE_ENV!=="production"&&ht("Computed",xe)}if(c)for(const xe in c)Kd(c[xe],i,s,xe);if(h){const xe=De(h)?h.call(s):h;Reflect.ownKeys(xe).forEach(be=>{Ai(be,xe[be])})}p&&Gd(p,e,"c");function ct(xe,be){me(be)?be.forEach(Ut=>xe(Ut.bind(s))):be&&xe(be.bind(s))}if(ct(ov,_),ct(Fd,w),ct(iv,D),ct(av,k),ct(sv,F),ct(rv,te),ct(dv,Ae),ct(cv,fe),ct(uv,ve),ct(Bd,se),ct($d,ye),ct(lv,ie),me(V))if(V.length){const xe=e.exposed||(e.exposed={});V.forEach(be=>{Object.defineProperty(xe,be,{get:()=>s[be],set:Ut=>s[be]=Ut})})}else e.exposed||(e.exposed={});Z&&e.render===Ot&&(e.render=Z),Te!=null&&(e.inheritAttrs=Te),le&&(e.components=le),Ge&&(e.directives=Ge),ie&&Ld(e)}function bv(e,t,s=Ot){me(e)&&(e=Nl(e));for(const i in e){const o=e[i];let a;Ze(o)?"default"in o?a=Vs(o.from||i,o.default,!0):a=Vs(o.from||i):a=Vs(o),Dt(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Gd(e,t,s){Ps(me(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Kd(e,t,s,i){let o=i.includes(".")?hf(s,i):()=>s[i];if(ut(e)){const a=t[e];De(a)?Pn(o,a):{}.NODE_ENV!=="production"&&Q(`Invalid watch handler specified by key "${e}"`,a)}else if(De(e))Pn(o,e.bind(s));else if(Ze(e))if(me(e))e.forEach(a=>Kd(a,t,s,i));else{const a=De(e.handler)?e.handler.bind(s):t[e.handler];De(a)?Pn(o,a,e):{}.NODE_ENV!=="production"&&Q(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&Q(`Invalid watch option: "${i}"`,e)}function Tl(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:o,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,c=a.get(t);let h;return c?h=c:!o.length&&!s&&!i?h=t:(h={},o.length&&o.forEach(m=>Ni(h,m,u,!0)),Ni(h,t,u)),Ze(t)&&a.set(t,h),h}function Ni(e,t,s,i=!1){const{mixins:o,extends:a}=t;a&&Ni(e,a,s,!0),o&&o.forEach(u=>Ni(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&Q('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=wv[u]||s&&s[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const wv={data:Yd,props:Qd,emits:Qd,methods:vo,computed:vo,beforeCreate:Zt,created:Zt,beforeMount:Zt,mounted:Zt,beforeUpdate:Zt,updated:Zt,beforeDestroy:Zt,beforeUnmount:Zt,destroyed:Zt,unmounted:Zt,activated:Zt,deactivated:Zt,errorCaptured:Zt,serverPrefetch:Zt,components:vo,directives:vo,watch:Cv,provide:Yd,inject:Ev};function Yd(e,t){return t?e?function(){return ft(De(e)?e.call(this,this):e,De(t)?t.call(this,this):t)}:t:e}function Ev(e,t){return vo(Nl(e),Nl(t))}function Nl(e){if(me(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Zt(e,t){return e?[...new Set([].concat(e,t))]:t}function vo(e,t){return e?ft(Object.create(null),e,t):t}function Qd(e,t){return e?me(e)&&me(t)?[...new Set([...e,...t])]:ft(Object.create(null),Wd(e),Wd(t??{})):t}function Cv(e,t){if(!e)return t;if(!t)return e;const s=ft(Object.create(null),e);for(const i in t)s[i]=Zt(e[i],t[i]);return s}function Zd(){return{app:null,config:{isNativeTag:Fg,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Dv=0;function xv(e,t){return function(i,o=null){De(i)||(i=ft({},i)),o!=null&&!Ze(o)&&({}.NODE_ENV!=="production"&&Q("root props passed to app.mount() must be an object."),o=null);const a=Zd(),u=new WeakSet,c=[];let h=!1;const m=a.app={_uid:Dv++,_component:i,_props:o,_container:null,_context:a,_instance:null,version:Af,get config(){return a.config},set config(p){({}).NODE_ENV!=="production"&&Q("app.config cannot be replaced. Modify individual options instead.")},use(p,..._){return u.has(p)?{}.NODE_ENV!=="production"&&Q("Plugin has already been applied to target app."):p&&De(p.install)?(u.add(p),p.install(m,..._)):De(p)?(u.add(p),p(m,..._)):{}.NODE_ENV!=="production"&&Q('A plugin must either be a function or an object with an "install" function.'),m},mixin(p){return a.mixins.includes(p)?{}.NODE_ENV!=="production"&&Q("Mixin has already been applied to target app"+(p.name?`: ${p.name}`:"")):a.mixins.push(p),m},component(p,_){return{}.NODE_ENV!=="production"&&Bl(p,a.config),_?({}.NODE_ENV!=="production"&&a.components[p]&&Q(`Component "${p}" has already been registered in target app.`),a.components[p]=_,m):a.components[p]},directive(p,_){return{}.NODE_ENV!=="production"&&Ed(p),_?({}.NODE_ENV!=="production"&&a.directives[p]&&Q(`Directive "${p}" has already been registered in target app.`),a.directives[p]=_,m):a.directives[p]},mount(p,_,w){if(h)({}).NODE_ENV!=="production"&&Q("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&p.__vue_app__&&Q("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const D=m._ceVNode||P(i,o);return D.appContext=a,w===!0?w="svg":w===!1&&(w=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{e(Ks(D),p,w)}),_&&t?t(D,p):e(D,p,w),h=!0,m._container=p,p.__vue_app__=m,{}.NODE_ENV!=="production"&&(m._instance=D.component,j_(m,Af)),Li(D.component)}},onUnmount(p){({}).NODE_ENV!=="production"&&typeof p!="function"&&Q(`Expected function as first argument to app.onUnmount(), but got ${typeof p}`),c.push(p)},unmount(){h?(Ps(c,m._instance,16),e(null,m._container),{}.NODE_ENV!=="production"&&(m._instance=null,H_(m)),delete m._container.__vue_app__):{}.NODE_ENV!=="production"&&Q("Cannot unmount an app that is not mounted.")},provide(p,_){return{}.NODE_ENV!=="production"&&p in a.provides&&Q(`App already provides property with key "${String(p)}". It will be overwritten with the new value.`),a.provides[p]=_,m},runWithContext(p){const _=Mn;Mn=m;try{return p()}finally{Mn=_}}};return m}}let Mn=null;function Ai(e,t){if(!Nt)({}).NODE_ENV!=="production"&&Q("provide() can only be used inside setup().");else{let s=Nt.provides;const i=Nt.parent&&Nt.parent.provides;i===s&&(s=Nt.provides=Object.create(i)),s[e]=t}}function Vs(e,t,s=!1){const i=Nt||xt;if(i||Mn){const o=Mn?Mn._context.provides:i?i.parent==null?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return s&&De(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&Q(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&Q("inject() can only be used inside setup() or functional components.")}const Jd={},Xd=()=>Object.create(Jd),ef=e=>Object.getPrototypeOf(e)===Jd;function Sv(e,t,s,i=!1){const o={},a=Xd();e.propsDefaults=Object.create(null),tf(e,t,o,a);for(const u in e.propsOptions[0])u in o||(o[u]=void 0);({}).NODE_ENV!=="production"&&nf(t||{},o,e),s?e.props=i?o:nd(o):e.type.props?e.props=o:e.props=a,e.attrs=a}function Ov(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function Tv(e,t,s,i){const{props:o,attrs:a,vnode:{patchFlag:u}}=e,c=Ie(o),[h]=e.propsOptions;let m=!1;if(!({}.NODE_ENV!=="production"&&Ov(e))&&(i||u>0)&&!(u&16)){if(u&8){const p=e.vnode.dynamicProps;for(let _=0;_<p.length;_++){let w=p[_];if(Mi(e.emitsOptions,w))continue;const D=t[w];if(h)if(Ye(a,w))D!==a[w]&&(a[w]=D,m=!0);else{const k=Kt(w);o[k]=Al(h,c,k,D,e,!1)}else D!==a[w]&&(a[w]=D,m=!0)}}}else{tf(e,t,o,a)&&(m=!0);let p;for(const _ in c)(!t||!Ye(t,_)&&((p=Sr(_))===_||!Ye(t,p)))&&(h?s&&(s[_]!==void 0||s[p]!==void 0)&&(o[_]=Al(h,c,_,void 0,e,!0)):delete o[_]);if(a!==c)for(const _ in a)(!t||!Ye(t,_))&&(delete a[_],m=!0)}m&&qs(e.attrs,"set",""),{}.NODE_ENV!=="production"&&nf(t||{},o,e)}function tf(e,t,s,i){const[o,a]=e.propsOptions;let u=!1,c;if(t)for(let h in t){if(ro(h))continue;const m=t[h];let p;o&&Ye(o,p=Kt(h))?!a||!a.includes(p)?s[p]=m:(c||(c={}))[p]=m:Mi(e.emitsOptions,h)||(!(h in i)||m!==i[h])&&(i[h]=m,u=!0)}if(a){const h=Ie(s),m=c||tt;for(let p=0;p<a.length;p++){const _=a[p];s[_]=Al(o,h,_,m[_],e,!Ye(m,_))}}return u}function Al(e,t,s,i,o,a){const u=e[s];if(u!=null){const c=Ye(u,"default");if(c&&i===void 0){const h=u.default;if(u.type!==Function&&!u.skipFactory&&De(h)){const{propsDefaults:m}=o;if(s in m)i=m[s];else{const p=xo(o);i=m[s]=h.call(null,t),p()}}else i=h;o.ce&&o.ce._setProp(s,i)}u[0]&&(a&&!c?i=!1:u[1]&&(i===""||i===Sr(s))&&(i=!0))}return i}const Nv=new WeakMap;function sf(e,t,s=!1){const i=s?Nv:t.propsCache,o=i.get(e);if(o)return o;const a=e.props,u={},c=[];let h=!1;if(!De(e)){const p=_=>{h=!0;const[w,D]=sf(_,t,!0);ft(u,w),D&&c.push(...D)};!s&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!a&&!h)return Ze(e)&&i.set(e,Dn),Dn;if(me(a))for(let p=0;p<a.length;p++){({}).NODE_ENV!=="production"&&!ut(a[p])&&Q("props must be strings when using array syntax.",a[p]);const _=Kt(a[p]);rf(_)&&(u[_]=tt)}else if(a){({}).NODE_ENV!=="production"&&!Ze(a)&&Q("invalid props options",a);for(const p in a){const _=Kt(p);if(rf(_)){const w=a[p],D=u[_]=me(w)||De(w)?{type:w}:ft({},w),k=D.type;let F=!1,te=!0;if(me(k))for(let A=0;A<k.length;++A){const se=k[A],G=De(se)&&se.name;if(G==="Boolean"){F=!0;break}else G==="String"&&(te=!1)}else F=De(k)&&k.name==="Boolean";D[0]=F,D[1]=te,(F||Ye(D,"default"))&&c.push(_)}}}const m=[u,c];return Ze(e)&&i.set(e,m),m}function rf(e){return e[0]!=="$"&&!ro(e)?!0:({}.NODE_ENV!=="production"&&Q(`Invalid prop name: "${e}" is a reserved property.`),!1)}function Av(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function nf(e,t,s){const i=Ie(t),o=s.propsOptions[0],a=Object.keys(e).map(u=>Kt(u));for(const u in o){let c=o[u];c!=null&&Iv(u,i[u],c,{}.NODE_ENV!=="production"?zs(i):i,!a.includes(u))}}function Iv(e,t,s,i,o){const{type:a,required:u,validator:c,skipCheck:h}=s;if(u&&o){Q('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!h){let m=!1;const p=me(a)?a:[a],_=[];for(let w=0;w<p.length&&!m;w++){const{valid:D,expectedType:k}=Pv(t,p[w]);_.push(k||""),m=D}if(!m){Q(kv(e,t,_));return}}c&&!c(t,i)&&Q('Invalid prop: custom validator check failed for prop "'+e+'".')}}const Mv=er("String,Number,Boolean,Function,Symbol,BigInt");function Pv(e,t){let s;const i=Av(t);if(i==="null")s=e===null;else if(Mv(i)){const o=typeof e;s=o===i.toLowerCase(),!s&&o==="object"&&(s=e instanceof t)}else i==="Object"?s=Ze(e):i==="Array"?s=me(e):s=e instanceof t;return{valid:s,expectedType:i}}function kv(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(Yr).join(" | ")}`;const o=s[0],a=Qa(t),u=of(t,o),c=of(t,a);return s.length===1&&af(o)&&!Vv(o,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,af(a)&&(i+=`with value ${c}.`),i}function of(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function af(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function Vv(...e){return e.some(t=>t.toLowerCase()==="boolean")}const lf=e=>e[0]==="_"||e==="$stable",Il=e=>me(e)?e.map(Rs):[Rs(e)],Rv=(e,t,s)=>{if(t._n)return t;const i=Ne((...o)=>({}.NODE_ENV!=="production"&&Nt&&(!s||s.root===Nt.root)&&Q(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),Il(t(...o))),s);return i._c=!1,i},uf=(e,t,s)=>{const i=e._ctx;for(const o in e){if(lf(o))continue;const a=e[o];if(De(a))t[o]=Rv(o,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&Q(`Non-function value encountered for slot "${o}". Prefer function slots for better performance.`);const u=Il(a);t[o]=()=>u}}},cf=(e,t)=>{({}).NODE_ENV!=="production"&&!_o(e.vnode)&&Q("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=Il(t);e.slots.default=()=>s},Ml=(e,t,s)=>{for(const i in t)(s||i!=="_")&&(e[i]=t[i])},Lv=(e,t,s)=>{const i=e.slots=Xd();if(e.vnode.shapeFlag&32){const o=t._;o?(Ml(i,t,s),s&&ii(i,"_",o,!0)):uf(t,i)}else t&&cf(e,t)},Uv=(e,t,s)=>{const{vnode:i,slots:o}=e;let a=!0,u=tt;if(i.shapeFlag&32){const c=t._;c?{}.NODE_ENV!=="production"&&ks?(Ml(o,t,s),qs(e,"set","$slots")):s&&c===1?a=!1:Ml(o,t,s):(a=!t.$stable,uf(t,o)),u=t}else t&&(cf(e,t),u={default:1});if(a)for(const c in o)!lf(c)&&u[c]==null&&delete o[c]};let yo,Ir;function ar(e,t){e.appContext.config.performance&&Ii()&&Ir.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&G_(e,t,Ii()?Ir.now():Date.now())}function lr(e,t){if(e.appContext.config.performance&&Ii()){const s=`vue-${t}-${e.uid}`,i=s+":end";Ir.mark(i),Ir.measure(`<${Ui(e,e.type)}> ${t}`,s,i),Ir.clearMarks(s),Ir.clearMarks(i)}({}).NODE_ENV!=="production"&&K_(e,t,Ii()?Ir.now():Date.now())}function Ii(){return yo!==void 0||(typeof window<"u"&&window.performance?(yo=!0,Ir=window.performance):yo=!1),yo}function Fv(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Jt=ey;function Bv(e){return $v(e)}function $v(e,t){Fv();const s=no();s.__VUE__=!0,{}.NODE_ENV!=="production"&&vd(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:o,patchProp:a,createElement:u,createText:c,createComment:h,setText:m,setElementText:p,parentNode:_,nextSibling:w,setScopeId:D=Ot,insertStaticContent:k}=e,F=(b,C,M,U=null,H=null,z=null,X=void 0,K=null,J={}.NODE_ENV!=="production"&&ks?!1:!!C.dynamicChildren)=>{if(b===C)return;b&&!ln(b,C)&&(U=oe(b),ze(b,H,z,!0),b=null),C.patchFlag===-2&&(J=!1,C.dynamicChildren=null);const{type:W,ref:we,shapeFlag:re}=C;switch(W){case wo:te(b,C,M,U);break;case wt:A(b,C,M,U);break;case Eo:b==null?se(C,M,U,X):{}.NODE_ENV!=="production"&&G(b,C,M,X);break;case Re:Ge(b,C,M,U,H,z,X,K,J);break;default:re&1?fe(b,C,M,U,H,z,X,K,J):re&6?gt(b,C,M,U,H,z,X,K,J):re&64||re&128?W.process(b,C,M,U,H,z,X,K,J,Pe):{}.NODE_ENV!=="production"&&Q("Invalid VNode type:",W,`(${typeof W})`)}we!=null&&H&&Oi(we,b&&b.ref,z,C||b,!C)},te=(b,C,M,U)=>{if(b==null)i(C.el=c(C.children),M,U);else{const H=C.el=b.el;C.children!==b.children&&m(H,C.children)}},A=(b,C,M,U)=>{b==null?i(C.el=h(C.children||""),M,U):C.el=b.el},se=(b,C,M,U)=>{[b.el,b.anchor]=k(b.children,C,M,U,b.el,b.anchor)},G=(b,C,M,U)=>{if(C.children!==b.children){const H=w(b.anchor);Z(b),[C.el,C.anchor]=k(C.children,M,H,U)}else C.el=b.el,C.anchor=b.anchor},ye=({el:b,anchor:C},M,U)=>{let H;for(;b&&b!==C;)H=w(b),i(b,M,U),b=H;i(C,M,U)},Z=({el:b,anchor:C})=>{let M;for(;b&&b!==C;)M=w(b),o(b),b=M;o(C)},fe=(b,C,M,U,H,z,X,K,J)=>{C.type==="svg"?X="svg":C.type==="math"&&(X="mathml"),b==null?ve(C,M,U,H,z,X,K,J):V(b,C,H,z,X,K,J)},ve=(b,C,M,U,H,z,X,K)=>{let J,W;const{props:we,shapeFlag:re,transition:_e,dirs:Ee}=b;if(J=b.el=u(b.type,z,we&&we.is,we),re&8?p(J,b.children):re&16&&ie(b.children,J,null,U,H,Pl(b,z),X,K),Ee&&sn(b,null,U,"created"),Ae(J,b,b.scopeId,X,U),we){for(const Xe in we)Xe!=="value"&&!ro(Xe)&&a(J,Xe,null,we[Xe],z,U);"value"in we&&a(J,"value",null,we.value,z),(W=we.onVnodeBeforeMount)&&Ys(W,U,b)}({}).NODE_ENV!=="production"&&(ii(J,"__vnode",b,!0),ii(J,"__vueParentComponent",U,!0)),Ee&&sn(b,null,U,"beforeMount");const Le=jv(H,_e);Le&&_e.beforeEnter(J),i(J,C,M),((W=we&&we.onVnodeMounted)||Le||Ee)&&Jt(()=>{W&&Ys(W,U,b),Le&&_e.enter(J),Ee&&sn(b,null,U,"mounted")},H)},Ae=(b,C,M,U,H)=>{if(M&&D(b,M),U)for(let z=0;z<U.length;z++)D(b,U[z]);if(H){let z=H.subTree;if({}.NODE_ENV!=="production"&&z.patchFlag>0&&z.patchFlag&2048&&(z=Ll(z.children)||z),C===z||vf(z.type)&&(z.ssContent===C||z.ssFallback===C)){const X=H.vnode;Ae(b,X,X.scopeId,X.slotScopeIds,H.parent)}}},ie=(b,C,M,U,H,z,X,K,J=0)=>{for(let W=J;W<b.length;W++){const we=b[W]=K?Mr(b[W]):Rs(b[W]);F(null,we,C,M,U,H,z,X,K)}},V=(b,C,M,U,H,z,X)=>{const K=C.el=b.el;({}).NODE_ENV!=="production"&&(K.__vnode=C);let{patchFlag:J,dynamicChildren:W,dirs:we}=C;J|=b.patchFlag&16;const re=b.props||tt,_e=C.props||tt;let Ee;if(M&&on(M,!1),(Ee=_e.onVnodeBeforeUpdate)&&Ys(Ee,M,C,b),we&&sn(C,b,M,"beforeUpdate"),M&&on(M,!0),{}.NODE_ENV!=="production"&&ks&&(J=0,X=!1,W=null),(re.innerHTML&&_e.innerHTML==null||re.textContent&&_e.textContent==null)&&p(K,""),W?(Te(b.dynamicChildren,W,K,M,U,Pl(C,H),z),{}.NODE_ENV!=="production"&&bo(b,C)):X||Ut(b,C,K,null,M,U,Pl(C,H),z,!1),J>0){if(J&16)le(K,re,_e,M,H);else if(J&2&&re.class!==_e.class&&a(K,"class",null,_e.class,H),J&4&&a(K,"style",re.style,_e.style,H),J&8){const Le=C.dynamicProps;for(let Xe=0;Xe<Le.length;Xe++){const Qe=Le[Xe],Ft=re[Qe],St=_e[Qe];(St!==Ft||Qe==="value")&&a(K,Qe,Ft,St,H,M)}}J&1&&b.children!==C.children&&p(K,C.children)}else!X&&W==null&&le(K,re,_e,M,H);((Ee=_e.onVnodeUpdated)||we)&&Jt(()=>{Ee&&Ys(Ee,M,C,b),we&&sn(C,b,M,"updated")},U)},Te=(b,C,M,U,H,z,X)=>{for(let K=0;K<C.length;K++){const J=b[K],W=C[K],we=J.el&&(J.type===Re||!ln(J,W)||J.shapeFlag&70)?_(J.el):M;F(J,W,we,null,U,H,z,X,!0)}},le=(b,C,M,U,H)=>{if(C!==M){if(C!==tt)for(const z in C)!ro(z)&&!(z in M)&&a(b,z,C[z],null,H,U);for(const z in M){if(ro(z))continue;const X=M[z],K=C[z];X!==K&&z!=="value"&&a(b,z,K,X,H,U)}"value"in M&&a(b,"value",C.value,M.value,H)}},Ge=(b,C,M,U,H,z,X,K,J)=>{const W=C.el=b?b.el:c(""),we=C.anchor=b?b.anchor:c("");let{patchFlag:re,dynamicChildren:_e,slotScopeIds:Ee}=C;({}).NODE_ENV!=="production"&&(ks||re&2048)&&(re=0,J=!1,_e=null),Ee&&(K=K?K.concat(Ee):Ee),b==null?(i(W,M,U),i(we,M,U),ie(C.children||[],M,we,H,z,X,K,J)):re>0&&re&64&&_e&&b.dynamicChildren?(Te(b.dynamicChildren,_e,M,H,z,X,K),{}.NODE_ENV!=="production"?bo(b,C):(C.key!=null||H&&C===H.subTree)&&bo(b,C,!0)):Ut(b,C,M,we,H,z,X,K,J)},gt=(b,C,M,U,H,z,X,K,J)=>{C.slotScopeIds=K,b==null?C.shapeFlag&512?H.ctx.activate(C,M,U,X,J):ht(C,M,U,H,z,X,J):ct(b,C,J)},ht=(b,C,M,U,H,z,X)=>{const K=b.component=ly(b,U,H);if({}.NODE_ENV!=="production"&&K.type.__hmrId&&U_(K),{}.NODE_ENV!=="production"&&(_i(b),ar(K,"mount")),_o(b)&&(K.ctx.renderer=Pe),{}.NODE_ENV!=="production"&&ar(K,"init"),cy(K,!1,X),{}.NODE_ENV!=="production"&&lr(K,"init"),K.asyncDep){if({}.NODE_ENV!=="production"&&ks&&(b.el=null),H&&H.registerDep(K,xe,X),!b.el){const J=K.subTree=P(wt);A(null,J,C,M)}}else xe(K,b,C,M,H,z,X);({}).NODE_ENV!=="production"&&(vi(),lr(K,"mount"))},ct=(b,C,M)=>{const U=C.component=b.component;if(Jv(b,C,M))if(U.asyncDep&&!U.asyncResolved){({}).NODE_ENV!=="production"&&_i(C),be(U,C,M),{}.NODE_ENV!=="production"&&vi();return}else U.next=C,U.update();else C.el=b.el,U.vnode=C},xe=(b,C,M,U,H,z,X)=>{const K=()=>{if(b.isMounted){let{next:re,bu:_e,u:Ee,parent:Le,vnode:Xe}=b;{const Bt=df(b);if(Bt){re&&(re.el=Xe.el,be(b,re,X)),Bt.asyncDep.then(()=>{b.isUnmounted||K()});return}}let Qe=re,Ft;({}).NODE_ENV!=="production"&&_i(re||b.vnode),on(b,!1),re?(re.el=Xe.el,be(b,re,X)):re=Xe,_e&&Sn(_e),(Ft=re.props&&re.props.onVnodeBeforeUpdate)&&Ys(Ft,Le,re,Xe),on(b,!0),{}.NODE_ENV!=="production"&&ar(b,"render");const St=Rl(b);({}).NODE_ENV!=="production"&&lr(b,"render");const es=b.subTree;b.subTree=St,{}.NODE_ENV!=="production"&&ar(b,"patch"),F(es,St,_(es.el),oe(es),b,H,z),{}.NODE_ENV!=="production"&&lr(b,"patch"),re.el=St.el,Qe===null&&Xv(b,St.el),Ee&&Jt(Ee,H),(Ft=re.props&&re.props.onVnodeUpdated)&&Jt(()=>Ys(Ft,Le,re,Xe),H),{}.NODE_ENV!=="production"&&yd(b),{}.NODE_ENV!=="production"&&vi()}else{let re;const{el:_e,props:Ee}=C,{bm:Le,m:Xe,parent:Qe,root:Ft,type:St}=b,es=In(C);if(on(b,!1),Le&&Sn(Le),!es&&(re=Ee&&Ee.onVnodeBeforeMount)&&Ys(re,Qe,C),on(b,!0),_e&&ke){const Bt=()=>{({}).NODE_ENV!=="production"&&ar(b,"render"),b.subTree=Rl(b),{}.NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"hydrate"),ke(_e,b.subTree,b,H,null),{}.NODE_ENV!=="production"&&lr(b,"hydrate")};es&&St.__asyncHydrate?St.__asyncHydrate(_e,b,Bt):Bt()}else{Ft.ce&&Ft.ce._injectChildStyle(St),{}.NODE_ENV!=="production"&&ar(b,"render");const Bt=b.subTree=Rl(b);({}).NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"patch"),F(null,Bt,M,U,b,H,z),{}.NODE_ENV!=="production"&&lr(b,"patch"),C.el=Bt.el}if(Xe&&Jt(Xe,H),!es&&(re=Ee&&Ee.onVnodeMounted)){const Bt=C;Jt(()=>Ys(re,Qe,Bt),H)}(C.shapeFlag&256||Qe&&In(Qe.vnode)&&Qe.vnode.shapeFlag&256)&&b.a&&Jt(b.a,H),b.isMounted=!0,{}.NODE_ENV!=="production"&&q_(b),C=M=U=null}};b.scope.on();const J=b.effect=new Fc(K);b.scope.off();const W=b.update=J.run.bind(J),we=b.job=J.runIfDirty.bind(J);we.i=b,we.id=b.uid,J.scheduler=()=>bi(we),on(b,!0),{}.NODE_ENV!=="production"&&(J.onTrack=b.rtc?re=>Sn(b.rtc,re):void 0,J.onTrigger=b.rtg?re=>Sn(b.rtg,re):void 0),W()},be=(b,C,M)=>{C.component=b;const U=b.vnode.props;b.vnode=C,b.next=null,Tv(b,C.props,U,M),Uv(b,C.children,M),tr(),hd(b),sr()},Ut=(b,C,M,U,H,z,X,K,J=!1)=>{const W=b&&b.children,we=b?b.shapeFlag:0,re=C.children,{patchFlag:_e,shapeFlag:Ee}=C;if(_e>0){if(_e&128){yt(W,re,M,U,H,z,X,K,J);return}else if(_e&256){Xt(W,re,M,U,H,z,X,K,J);return}}Ee&8?(we&16&&R(W,H,z),re!==W&&p(M,re)):we&16?Ee&16?yt(W,re,M,U,H,z,X,K,J):R(W,H,z,!0):(we&8&&p(M,""),Ee&16&&ie(re,M,U,H,z,X,K,J))},Xt=(b,C,M,U,H,z,X,K,J)=>{b=b||Dn,C=C||Dn;const W=b.length,we=C.length,re=Math.min(W,we);let _e;for(_e=0;_e<re;_e++){const Ee=C[_e]=J?Mr(C[_e]):Rs(C[_e]);F(b[_e],Ee,M,null,H,z,X,K,J)}W>we?R(b,H,z,!0,!1,re):ie(C,M,U,H,z,X,K,J,re)},yt=(b,C,M,U,H,z,X,K,J)=>{let W=0;const we=C.length;let re=b.length-1,_e=we-1;for(;W<=re&&W<=_e;){const Ee=b[W],Le=C[W]=J?Mr(C[W]):Rs(C[W]);if(ln(Ee,Le))F(Ee,Le,M,null,H,z,X,K,J);else break;W++}for(;W<=re&&W<=_e;){const Ee=b[re],Le=C[_e]=J?Mr(C[_e]):Rs(C[_e]);if(ln(Ee,Le))F(Ee,Le,M,null,H,z,X,K,J);else break;re--,_e--}if(W>re){if(W<=_e){const Ee=_e+1,Le=Ee<we?C[Ee].el:U;for(;W<=_e;)F(null,C[W]=J?Mr(C[W]):Rs(C[W]),M,Le,H,z,X,K,J),W++}}else if(W>_e)for(;W<=re;)ze(b[W],H,z,!0),W++;else{const Ee=W,Le=W,Xe=new Map;for(W=Le;W<=_e;W++){const At=C[W]=J?Mr(C[W]):Rs(C[W]);At.key!=null&&({}.NODE_ENV!=="production"&&Xe.has(At.key)&&Q("Duplicate keys found during update:",JSON.stringify(At.key),"Make sure keys are unique."),Xe.set(At.key,W))}let Qe,Ft=0;const St=_e-Le+1;let es=!1,Bt=0;const gr=new Array(St);for(W=0;W<St;W++)gr[W]=0;for(W=Ee;W<=re;W++){const At=b[W];if(Ft>=St){ze(At,H,z,!0);continue}let Es;if(At.key!=null)Es=Xe.get(At.key);else for(Qe=Le;Qe<=_e;Qe++)if(gr[Qe-Le]===0&&ln(At,C[Qe])){Es=Qe;break}Es===void 0?ze(At,H,z,!0):(gr[Es-Le]=W+1,Es>=Bt?Bt=Es:es=!0,F(At,C[Es],M,null,H,z,X,K,J),Ft++)}const Bn=es?Hv(gr):Dn;for(Qe=Bn.length-1,W=St-1;W>=0;W--){const At=Le+W,Es=C[At],oa=At+1<we?C[At+1].el:U;gr[W]===0?F(null,Es,M,oa,H,z,X,K,J):es&&(Qe<0||W!==Bn[Qe]?ce(Es,M,oa,2):Qe--)}}},ce=(b,C,M,U,H=null)=>{const{el:z,type:X,transition:K,children:J,shapeFlag:W}=b;if(W&6){ce(b.component.subTree,C,M,U);return}if(W&128){b.suspense.move(C,M,U);return}if(W&64){X.move(b,C,M,Pe);return}if(X===Re){i(z,C,M);for(let re=0;re<J.length;re++)ce(J[re],C,M,U);i(b.anchor,C,M);return}if(X===Eo){ye(b,C,M);return}if(U!==2&&W&1&&K)if(U===0)K.beforeEnter(z),i(z,C,M),Jt(()=>K.enter(z),H);else{const{leave:re,delayLeave:_e,afterLeave:Ee}=K,Le=()=>i(z,C,M),Xe=()=>{re(z,()=>{Le(),Ee&&Ee()})};_e?_e(z,Le,Xe):Xe()}else i(z,C,M)},ze=(b,C,M,U=!1,H=!1)=>{const{type:z,props:X,ref:K,children:J,dynamicChildren:W,shapeFlag:we,patchFlag:re,dirs:_e,cacheIndex:Ee}=b;if(re===-2&&(H=!1),K!=null&&Oi(K,null,M,b,!0),Ee!=null&&(C.renderCache[Ee]=void 0),we&256){C.ctx.deactivate(b);return}const Le=we&1&&_e,Xe=!In(b);let Qe;if(Xe&&(Qe=X&&X.onVnodeBeforeUnmount)&&Ys(Qe,C,b),we&6)hs(b.component,M,U);else{if(we&128){b.suspense.unmount(M,U);return}Le&&sn(b,null,C,"beforeUnmount"),we&64?b.type.remove(b,C,M,Pe,U):W&&!W.hasOnce&&(z!==Re||re>0&&re&64)?R(W,C,M,!1,!0):(z===Re&&re&384||!H&&we&16)&&R(J,C,M),U&&fs(b)}(Xe&&(Qe=X&&X.onVnodeUnmounted)||Le)&&Jt(()=>{Qe&&Ys(Qe,C,b),Le&&sn(b,null,C,"unmounted")},M)},fs=b=>{const{type:C,el:M,anchor:U,transition:H}=b;if(C===Re){({}).NODE_ENV!=="production"&&b.patchFlag>0&&b.patchFlag&2048&&H&&!H.persisted?b.children.forEach(X=>{X.type===wt?o(X.el):fs(X)}):zt(M,U);return}if(C===Eo){Z(b);return}const z=()=>{o(M),H&&!H.persisted&&H.afterLeave&&H.afterLeave()};if(b.shapeFlag&1&&H&&!H.persisted){const{leave:X,delayLeave:K}=H,J=()=>X(M,z);K?K(b.el,z,J):J()}else z()},zt=(b,C)=>{let M;for(;b!==C;)M=w(b),o(b),b=M;o(C)},hs=(b,C,M)=>{({}).NODE_ENV!=="production"&&b.type.__hmrId&&F_(b);const{bum:U,scope:H,job:z,subTree:X,um:K,m:J,a:W}=b;ff(J),ff(W),U&&Sn(U),H.stop(),z&&(z.flags|=8,ze(X,b,C,M)),K&&Jt(K,C),Jt(()=>{b.isUnmounted=!0},C),C&&C.pendingBranch&&!C.isUnmounted&&b.asyncDep&&!b.asyncResolved&&b.suspenseId===C.pendingId&&(C.deps--,C.deps===0&&C.resolve()),{}.NODE_ENV!=="production"&&W_(b)},R=(b,C,M,U=!1,H=!1,z=0)=>{for(let X=z;X<b.length;X++)ze(b[X],C,M,U,H)},oe=b=>{if(b.shapeFlag&6)return oe(b.component.subTree);if(b.shapeFlag&128)return b.suspense.next();const C=w(b.anchor||b.el),M=C&&C[Cd];return M?w(M):C};let ne=!1;const pe=(b,C,M)=>{b==null?C._vnode&&ze(C._vnode,null,null,!0):F(C._vnode||null,b,C,null,null,null,M),C._vnode=b,ne||(ne=!0,hd(),pd(),ne=!1)},Pe={p:F,um:ze,m:ce,r:fs,mt:ht,mc:ie,pc:Ut,pbc:Te,n:oe,o:e};let ot,ke;return t&&([ot,ke]=t(Pe)),{render:pe,hydrate:ot,createApp:xv(pe,ot)}}function Pl({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function on({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function jv(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function bo(e,t,s=!1){const i=e.children,o=t.children;if(me(i)&&me(o))for(let a=0;a<i.length;a++){const u=i[a];let c=o[a];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=o[a]=Mr(o[a]),c.el=u.el),!s&&c.patchFlag!==-2&&bo(u,c)),c.type===wo&&(c.el=u.el),{}.NODE_ENV!=="production"&&c.type===wt&&!c.el&&(c.el=u.el)}}function Hv(e){const t=e.slice(),s=[0];let i,o,a,u,c;const h=e.length;for(i=0;i<h;i++){const m=e[i];if(m!==0){if(o=s[s.length-1],e[o]<m){t[i]=o,s.push(i);continue}for(a=0,u=s.length-1;a<u;)c=a+u>>1,e[s[c]]<m?a=c+1:u=c;m<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function df(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:df(t)}function ff(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const qv=Symbol.for("v-scx"),zv=()=>{{const e=Vs(qv);return e||{}.NODE_ENV!=="production"&&Q("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function Wv(e,t){return kl(e,null,t)}function Pn(e,t,s){return{}.NODE_ENV!=="production"&&!De(t)&&Q("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),kl(e,t,s)}function kl(e,t,s=tt){const{immediate:i,deep:o,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&Q('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),o!==void 0&&Q('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&Q('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=ft({},s);({}).NODE_ENV!=="production"&&(c.onWarn=Q);const h=t&&i||!t&&a!=="post";let m;if(So){if(a==="sync"){const D=zv();m=D.__watcherHandles||(D.__watcherHandles=[])}else if(!h){const D=()=>{};return D.stop=Ot,D.resume=Ot,D.pause=Ot,D}}const p=Nt;c.call=(D,k,F)=>Ps(D,p,k,F);let _=!1;a==="post"?c.scheduler=D=>{Jt(D,p&&p.suspense)}:a!=="sync"&&(_=!0,c.scheduler=(D,k)=>{k?D():bi(D)}),c.augmentJob=D=>{t&&(D.flags|=4),_&&(D.flags|=2,p&&(D.id=p.uid,D.i=p))};const w=N_(e,t,c);return So&&(m?m.push(w):h&&w()),w}function Gv(e,t,s){const i=this.proxy,o=ut(e)?e.includes(".")?hf(i,e):()=>i[e]:e.bind(i,i);let a;De(t)?a=t:(a=t.handler,s=t);const u=xo(this),c=kl(o,a.bind(i),s);return u(),c}function hf(e,t){const s=t.split(".");return()=>{let i=e;for(let o=0;o<s.length&&i;o++)i=i[s[o]];return i}}const Kv=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Kt(t)}Modifiers`]||e[`${Sr(t)}Modifiers`];function Yv(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||tt;if({}.NODE_ENV!=="production"){const{emitsOptions:p,propsOptions:[_]}=e;if(p)if(!(t in p))(!_||!(Qr(Kt(t))in _))&&Q(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${Qr(Kt(t))}" prop.`);else{const w=p[t];De(w)&&(w(...s)||Q(`Invalid event arguments: event validation failed for event "${t}".`))}}let o=s;const a=t.startsWith("update:"),u=a&&Kv(i,t.slice(7));if(u&&(u.trim&&(o=s.map(p=>ut(p)?p.trim():p)),u.number&&(o=s.map(ai))),{}.NODE_ENV!=="production"&&Y_(e,t,o),{}.NODE_ENV!=="production"){const p=t.toLowerCase();p!==t&&i[Qr(p)]&&Q(`Event "${p}" is emitted in component ${Ui(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${Sr(t)}" instead of "${t}".`)}let c,h=i[c=Qr(t)]||i[c=Qr(Kt(t))];!h&&a&&(h=i[c=Qr(Sr(t))]),h&&Ps(h,e,6,o);const m=i[c+"Once"];if(m){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Ps(m,e,6,o)}}function pf(e,t,s=!1){const i=t.emitsCache,o=i.get(e);if(o!==void 0)return o;const a=e.emits;let u={},c=!1;if(!De(e)){const h=m=>{const p=pf(m,t,!0);p&&(c=!0,ft(u,p))};!s&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}return!a&&!c?(Ze(e)&&i.set(e,null),null):(me(a)?a.forEach(h=>u[h]=null):ft(u,a),Ze(e)&&i.set(e,u),u)}function Mi(e,t){return!e||!to(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ye(e,t[0].toLowerCase()+t.slice(1))||Ye(e,Sr(t))||Ye(e,t))}let Vl=!1;function Pi(){Vl=!0}function Rl(e){const{type:t,vnode:s,proxy:i,withProxy:o,propsOptions:[a],slots:u,attrs:c,emit:h,render:m,renderCache:p,props:_,data:w,setupState:D,ctx:k,inheritAttrs:F}=e,te=Ci(e);let A,se;({}).NODE_ENV!=="production"&&(Vl=!1);try{if(s.shapeFlag&4){const Z=o||i,fe={}.NODE_ENV!=="production"&&D.__isScriptSetup?new Proxy(Z,{get(ve,Ae,ie){return Q(`Property '${String(Ae)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(ve,Ae,ie)}}):Z;A=Rs(m.call(fe,Z,p,{}.NODE_ENV!=="production"?zs(_):_,D,w,k)),se=c}else{const Z=t;({}).NODE_ENV!=="production"&&c===_&&Pi(),A=Rs(Z.length>1?Z({}.NODE_ENV!=="production"?zs(_):_,{}.NODE_ENV!=="production"?{get attrs(){return Pi(),zs(c)},slots:u,emit:h}:{attrs:c,slots:u,emit:h}):Z({}.NODE_ENV!=="production"?zs(_):_,null)),se=t.props?c:Qv(c)}}catch(Z){Co.length=0,fo(Z,e,1),A=P(wt)}let G=A,ye;if({}.NODE_ENV!=="production"&&A.patchFlag>0&&A.patchFlag&2048&&([G,ye]=mf(A)),se&&F!==!1){const Z=Object.keys(se),{shapeFlag:fe}=G;if(Z.length){if(fe&7)a&&Z.some(ni)&&(se=Zv(se,a)),G=Ks(G,se,!1,!0);else if({}.NODE_ENV!=="production"&&!Vl&&G.type!==wt){const ve=Object.keys(c),Ae=[],ie=[];for(let V=0,Te=ve.length;V<Te;V++){const le=ve[V];to(le)?ni(le)||Ae.push(le[2].toLowerCase()+le.slice(3)):ie.push(le)}ie.length&&Q(`Extraneous non-props attributes (${ie.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),Ae.length&&Q(`Extraneous non-emits event listeners (${Ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!gf(G)&&Q("Runtime directive used on component with non-element root node. The directives will not function as intended."),G=Ks(G,null,!1,!0),G.dirs=G.dirs?G.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!gf(G)&&Q("Component inside <Transition> renders non-element root node that cannot be animated."),go(G,s.transition)),{}.NODE_ENV!=="production"&&ye?ye(G):A=G,Ci(te),A}const mf=e=>{const t=e.children,s=e.dynamicChildren,i=Ll(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return mf(i)}else return[e,void 0];const o=t.indexOf(i),a=s?s.indexOf(i):-1,u=c=>{t[o]=c,s&&(a>-1?s[a]=c:c.patchFlag>0&&(e.dynamicChildren=[...s,c]))};return[Rs(i),u]};function Ll(e,t=!0){let s;for(let i=0;i<e.length;i++){const o=e[i];if(an(o)){if(o.type!==wt||o.children==="v-if"){if(s)return;if(s=o,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return Ll(s.children)}}else return}return s}const Qv=e=>{let t;for(const s in e)(s==="class"||s==="style"||to(s))&&((t||(t={}))[s]=e[s]);return t},Zv=(e,t)=>{const s={};for(const i in e)(!ni(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},gf=e=>e.shapeFlag&7||e.type===wt;function Jv(e,t,s){const{props:i,children:o,component:a}=e,{props:u,children:c,patchFlag:h}=t,m=a.emitsOptions;if({}.NODE_ENV!=="production"&&(o||c)&&ks||t.dirs||t.transition)return!0;if(s&&h>=0){if(h&1024)return!0;if(h&16)return i?_f(i,u,m):!!u;if(h&8){const p=t.dynamicProps;for(let _=0;_<p.length;_++){const w=p[_];if(u[w]!==i[w]&&!Mi(m,w))return!0}}}else return(o||c)&&(!c||!c.$stable)?!0:i===u?!1:i?u?_f(i,u,m):!0:!!u;return!1}function _f(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let o=0;o<i.length;o++){const a=i[o];if(t[a]!==e[a]&&!Mi(s,a))return!0}return!1}function Xv({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const vf=e=>e.__isSuspense;function ey(e,t){t&&t.pendingBranch?me(e)?t.effects.push(...e):t.effects.push(e):fd(e)}const Re=Symbol.for("v-fgt"),wo=Symbol.for("v-txt"),wt=Symbol.for("v-cmt"),Eo=Symbol.for("v-stc"),Co=[];let cs=null;function O(e=!1){Co.push(cs=e?null:[])}function ty(){Co.pop(),cs=Co[Co.length-1]||null}let Do=1;function yf(e,t=!1){Do+=e,e<0&&cs&&t&&(cs.hasOnce=!0)}function bf(e){return e.dynamicChildren=Do>0?cs||Dn:null,ty(),Do>0&&cs&&cs.push(e),e}function N(e,t,s,i,o,a){return bf(f(e,t,s,i,o,a,!0))}function Rt(e,t,s,i,o){return bf(P(e,t,s,i,o,!0))}function an(e){return e?e.__v_isVNode===!0:!1}function ln(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=wi.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const sy=(...e)=>Ef(...e),wf=({key:e})=>e??null,ki=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ut(e)||Dt(e)||De(e)?{i:xt,r:e,k:t,f:!!s}:e:null);function f(e,t=null,s=null,i=0,o=null,a=e===Re?0:1,u=!1,c=!1){const h={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&wf(t),ref:t&&ki(t),scopeId:wd,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:xt};return c?(Ul(h,s),a&128&&e.normalize(h)):s&&(h.shapeFlag|=ut(s)?8:16),{}.NODE_ENV!=="production"&&h.key!==h.key&&Q("VNode created with invalid key (NaN). VNode type:",h.type),Do>0&&!u&&cs&&(h.patchFlag>0||a&6)&&h.patchFlag!==32&&cs.push(h),h}const P={}.NODE_ENV!=="production"?sy:Ef;function Ef(e,t=null,s=null,i=0,o=null,a=!1){if((!e||e===hv)&&({}.NODE_ENV!=="production"&&!e&&Q(`Invalid vnode type when creating vnode: ${e}.`),e=wt),an(e)){const c=Ks(e,t,!0);return s&&Ul(c,s),Do>0&&!a&&cs&&(c.shapeFlag&6?cs[cs.indexOf(e)]=c:cs.push(c)),c.patchFlag=-2,c}if(Nf(e)&&(e=e.__vccOpts),t){t=ry(t);let{class:c,style:h}=t;c&&!ut(c)&&(t.class=he(c)),Ze(h)&&(pi(h)&&!me(h)&&(h=ft({},h)),t.style=ls(h))}const u=ut(e)?1:vf(e)?128:Dd(e)?64:Ze(e)?4:De(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&pi(e)&&(e=Ie(e),Q("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),f(e,t,s,i,o,u,a,!0)}function ry(e){return e?pi(e)||ef(e)?ft({},e):e:null}function Ks(e,t,s=!1,i=!1){const{props:o,ref:a,patchFlag:u,children:c,transition:h}=e,m=t?oy(o||{},t):o,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:m,key:m&&wf(m),ref:t&&t.ref?s&&a?me(a)?a.concat(ki(t)):[a,ki(t)]:ki(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&me(c)?c.map(Cf):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Re?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:h,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ks(e.ssContent),ssFallback:e.ssFallback&&Ks(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return h&&i&&go(p,h.clone(p)),p}function Cf(e){const t=Ks(e);return me(e.children)&&(t.children=e.children.map(Cf)),t}function nt(e=" ",t=0){return P(wo,null,e,t)}function ny(e,t){const s=P(Eo,null,e);return s.staticCount=t,s}function ae(e="",t=!1){return t?(O(),Rt(wt,null,e)):P(wt,null,e)}function Rs(e){return e==null||typeof e=="boolean"?P(wt):me(e)?P(Re,null,e.slice()):an(e)?Mr(e):P(wo,null,String(e))}function Mr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ks(e)}function Ul(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(me(t))s=16;else if(typeof t=="object")if(i&65){const o=t.default;o&&(o._c&&(o._d=!1),Ul(e,o()),o._c&&(o._d=!0));return}else{s=32;const o=t._;!o&&!ef(t)?t._ctx=xt:o===3&&xt&&(xt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else De(t)?(t={default:t,_ctx:xt},s=32):(t=String(t),i&64?(s=16,t=[nt(t)]):s=8);e.children=t,e.shapeFlag|=s}function oy(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const o in i)if(o==="class")t.class!==i.class&&(t.class=he([t.class,i.class]));else if(o==="style")t.style=ls([t.style,i.style]);else if(to(o)){const a=t[o],u=i[o];u&&a!==u&&!(me(a)&&a.includes(u))&&(t[o]=a?[].concat(a,u):u)}else o!==""&&(t[o]=i[o])}return t}function Ys(e,t,s,i=null){Ps(e,t,7,[s,i])}const iy=Zd();let ay=0;function ly(e,t,s){const i=e.type,o=(t?t.appContext:e.appContext)||iy,a={uid:ay++,vnode:e,type:i,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Uc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:sf(i,o),emitsOptions:pf(i,o),emit:null,emitted:null,propsDefaults:tt,inheritAttrs:i.inheritAttrs,ctx:tt,data:tt,props:tt,attrs:tt,slots:tt,refs:tt,setupState:tt,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=mv(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=Yv.bind(null,a),e.ce&&e.ce(a),a}let Nt=null;const Vi=()=>Nt||xt;let Ri,Fl;{const e=no(),t=(s,i)=>{let o;return(o=e[s])||(o=e[s]=[]),o.push(i),a=>{o.length>1?o.forEach(u=>u(a)):o[0](a)}};Ri=t("__VUE_INSTANCE_SETTERS__",s=>Nt=s),Fl=t("__VUE_SSR_SETTERS__",s=>So=s)}const xo=e=>{const t=Nt;return Ri(e),e.scope.on(),()=>{e.scope.off(),Ri(t)}},Df=()=>{Nt&&Nt.scope.off(),Ri(null)},uy=er("slot,component");function Bl(e,{isNativeTag:t}){(uy(e)||t(e))&&Q("Do not use built-in or reserved HTML elements as component id: "+e)}function xf(e){return e.vnode.shapeFlag&4}let So=!1;function cy(e,t=!1,s=!1){t&&Fl(t);const{props:i,children:o}=e.vnode,a=xf(e);Sv(e,i,a,t),Lv(e,o,s);const u=a?dy(e,t):void 0;return t&&Fl(!1),u}function dy(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&Bl(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)Bl(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)Ed(a[u])}i.compilerOptions&&fy()&&Q('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,zd),{}.NODE_ENV!=="production"&&gv(e);const{setup:o}=i;if(o){tr();const a=e.setupContext=o.length>1?py(e):null,u=xo(e),c=Tn(o,e,0,[{}.NODE_ENV!=="production"?zs(e.props):e.props,a]),h=Ya(c);if(sr(),u(),(h||e.sp)&&!In(e)&&Ld(e),h){if(c.then(Df,Df),t)return c.then(m=>{Sf(e,m,t)}).catch(m=>{fo(m,e,0)});if(e.asyncDep=c,{}.NODE_ENV!=="production"&&!e.suspense){const m=(s=i.name)!=null?s:"Anonymous";Q(`Component <${m}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else Sf(e,c,t)}else Of(e,t)}function Sf(e,t,s){De(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Ze(t)?({}.NODE_ENV!=="production"&&an(t)&&Q("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=ld(t),{}.NODE_ENV!=="production"&&_v(e)):{}.NODE_ENV!=="production"&&t!==void 0&&Q(`setup() should return an object. Received: ${t===null?"null":typeof t}`),Of(e,s)}let $l;const fy=()=>!$l;function Of(e,t,s){const i=e.type;if(!e.render){if(!t&&$l&&!i.render){const o=i.template||Tl(e).template;if(o){({}).NODE_ENV!=="production"&&ar(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:c,compilerOptions:h}=i,m=ft(ft({isCustomElement:a,delimiters:c},u),h);i.render=$l(o,m),{}.NODE_ENV!=="production"&&lr(e,"compile")}}e.render=i.render||Ot}{const o=xo(e);tr();try{yv(e)}finally{sr(),o()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===Ot&&!t&&(i.template?Q('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Q("Component is missing template or render function: ",i))}const Tf={}.NODE_ENV!=="production"?{get(e,t){return Pi(),Tt(e,"get",""),e[t]},set(){return Q("setupContext.attrs is readonly."),!1},deleteProperty(){return Q("setupContext.attrs is readonly."),!1}}:{get(e,t){return Tt(e,"get",""),e[t]}};function hy(e){return new Proxy(e.slots,{get(t,s){return Tt(e,"get","$slots"),t[s]}})}function py(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&Q("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(me(s)?i="array":Dt(s)&&(i="ref")),i!=="object"&&Q(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,Tf))},get slots(){return i||(i=hy(e))},get emit(){return(o,...a)=>e.emit(o,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,Tf),slots:e.slots,emit:e.emit,expose:t}}function Li(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ld(dl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in nn)return nn[s](e)},has(t,s){return s in t||s in nn}})):e.proxy}const my=/(?:^|[-_])(\w)/g,gy=e=>e.replace(my,t=>t.toUpperCase()).replace(/[-_]/g,"");function jl(e,t=!0){return De(e)?e.displayName||e.name:e.name||t&&e.__name}function Ui(e,t,s=!1){let i=jl(t);if(!i&&t.__file){const o=t.__file.match(/([^/\\]+)\.\w+$/);o&&(i=o[1])}if(!i&&e&&e.parent){const o=a=>{for(const u in a)if(a[u]===t)return u};i=o(e.components||e.parent.type.components)||o(e.appContext.components)}return i?gy(i):s?"App":"Anonymous"}function Nf(e){return De(e)&&"__vccOpts"in e}const Ls=(e,t)=>{const s=O_(e,t,So);if({}.NODE_ENV!=="production"){const i=Vi();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function Hl(e,t,s){const i=arguments.length;return i===2?Ze(t)&&!me(t)?an(t)?P(e,null,[t]):P(e,t):P(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&an(s)&&(s=[s]),P(e,t,s))}function _y(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},o={__vue_custom_formatter:!0,header(_){return Ze(_)?_.__isVue?["div",e,"VueInstance"]:Dt(_)?["div",{},["span",e,p(_)],"<",c("_value"in _?_._value:_),">"]:Jr(_)?["div",{},["span",e,Yt(_)?"ShallowReactive":"Reactive"],"<",c(_),`>${nr(_)?" (readonly)":""}`]:nr(_)?["div",{},["span",e,Yt(_)?"ShallowReadonly":"Readonly"],"<",c(_),">"]:null:null},hasBody(_){return _&&_.__isVue},body(_){if(_&&_.__isVue)return["div",{},...a(_.$)]}};function a(_){const w=[];_.type.props&&_.props&&w.push(u("props",Ie(_.props))),_.setupState!==tt&&w.push(u("setup",_.setupState)),_.data!==tt&&w.push(u("data",Ie(_.data)));const D=h(_,"computed");D&&w.push(u("computed",D));const k=h(_,"inject");return k&&w.push(u("injected",k)),w.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:_}]]),w}function u(_,w){return w=ft({},w),Object.keys(w).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},_],["div",{style:"padding-left:1.25em"},...Object.keys(w).map(D=>["div",{},["span",i,D+": "],c(w[D],!1)])]]:["span",{}]}function c(_,w=!0){return typeof _=="number"?["span",t,_]:typeof _=="string"?["span",s,JSON.stringify(_)]:typeof _=="boolean"?["span",i,_]:Ze(_)?["object",{object:w?Ie(_):_}]:["span",s,String(_)]}function h(_,w){const D=_.type;if(De(D))return;const k={};for(const F in _.ctx)m(D,F,w)&&(k[F]=_.ctx[F]);return k}function m(_,w,D){const k=_[D];if(me(k)&&k.includes(w)||Ze(k)&&w in k||_.extends&&m(_.extends,w,D)||_.mixins&&_.mixins.some(F=>m(F,w,D)))return!0}function p(_){return Yt(_)?"ShallowRef":_.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(o):window.devtoolsFormatters=[o]}const Af="3.5.13",Qs={}.NODE_ENV!=="production"?Q:Ot;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ql;const If=typeof window<"u"&&window.trustedTypes;if(If)try{ql=If.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&Qs(`Error creating trusted types policy: ${e}`)}const Mf=ql?e=>ql.createHTML(e):e=>e,vy="http://www.w3.org/2000/svg",yy="http://www.w3.org/1998/Math/MathML",ur=typeof document<"u"?document:null,Pf=ur&&ur.createElement("template"),by={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const o=t==="svg"?ur.createElementNS(vy,e):t==="mathml"?ur.createElementNS(yy,e):s?ur.createElement(e,{is:s}):ur.createElement(e);return e==="select"&&i&&i.multiple!=null&&o.setAttribute("multiple",i.multiple),o},createText:e=>ur.createTextNode(e),createComment:e=>ur.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ur.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,o,a){const u=s?s.previousSibling:t.lastChild;if(o&&(o===a||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),s),!(o===a||!(o=o.nextSibling)););else{Pf.innerHTML=Mf(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const c=Pf.content;if(i==="svg"||i==="mathml"){const h=c.firstChild;for(;h.firstChild;)c.appendChild(h.firstChild);c.removeChild(h)}t.insertBefore(c,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Pr="transition",Oo="animation",To=Symbol("_vtc"),kf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},wy=ft({},Ad,kf),Vf=(e=>(e.displayName="Transition",e.props=wy,e))((e,{slots:t})=>Hl(ev,Ey(e),t)),un=(e,t=[])=>{me(e)?e.forEach(s=>s(...t)):e&&e(...t)},Rf=e=>e?me(e)?e.some(t=>t.length>1):e.length>1:!1;function Ey(e){const t={};for(const le in e)le in kf||(t[le]=e[le]);if(e.css===!1)return t;const{name:s="v",type:i,duration:o,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:c=`${s}-enter-to`,appearFromClass:h=a,appearActiveClass:m=u,appearToClass:p=c,leaveFromClass:_=`${s}-leave-from`,leaveActiveClass:w=`${s}-leave-active`,leaveToClass:D=`${s}-leave-to`}=e,k=Cy(o),F=k&&k[0],te=k&&k[1],{onBeforeEnter:A,onEnter:se,onEnterCancelled:G,onLeave:ye,onLeaveCancelled:Z,onBeforeAppear:fe=A,onAppear:ve=se,onAppearCancelled:Ae=G}=t,ie=(le,Ge,gt,ht)=>{le._enterCancelled=ht,cn(le,Ge?p:c),cn(le,Ge?m:u),gt&&gt()},V=(le,Ge)=>{le._isLeaving=!1,cn(le,_),cn(le,D),cn(le,w),Ge&&Ge()},Te=le=>(Ge,gt)=>{const ht=le?ve:se,ct=()=>ie(Ge,le,gt);un(ht,[Ge,ct]),Lf(()=>{cn(Ge,le?h:a),cr(Ge,le?p:c),Rf(ht)||Uf(Ge,i,F,ct)})};return ft(t,{onBeforeEnter(le){un(A,[le]),cr(le,a),cr(le,u)},onBeforeAppear(le){un(fe,[le]),cr(le,h),cr(le,m)},onEnter:Te(!1),onAppear:Te(!0),onLeave(le,Ge){le._isLeaving=!0;const gt=()=>V(le,Ge);cr(le,_),le._enterCancelled?(cr(le,w),$f()):($f(),cr(le,w)),Lf(()=>{le._isLeaving&&(cn(le,_),cr(le,D),Rf(ye)||Uf(le,i,te,gt))}),un(ye,[le,gt])},onEnterCancelled(le){ie(le,!1,void 0,!0),un(G,[le])},onAppearCancelled(le){ie(le,!0,void 0,!0),un(Ae,[le])},onLeaveCancelled(le){V(le),un(Z,[le])}})}function Cy(e){if(e==null)return null;if(Ze(e))return[zl(e.enter),zl(e.leave)];{const t=zl(e);return[t,t]}}function zl(e){const t=qg(e);return{}.NODE_ENV!=="production"&&k_(t,"<transition> explicit duration"),t}function cr(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[To]||(e[To]=new Set)).add(t)}function cn(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[To];s&&(s.delete(t),s.size||(e[To]=void 0))}function Lf(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Dy=0;function Uf(e,t,s,i){const o=e._endId=++Dy,a=()=>{o===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:c,propCount:h}=xy(e,t);if(!u)return i();const m=u+"end";let p=0;const _=()=>{e.removeEventListener(m,w),a()},w=D=>{D.target===e&&++p>=h&&_()};setTimeout(()=>{p<h&&_()},c+1),e.addEventListener(m,w)}function xy(e,t){const s=window.getComputedStyle(e),i=k=>(s[k]||"").split(", "),o=i(`${Pr}Delay`),a=i(`${Pr}Duration`),u=Ff(o,a),c=i(`${Oo}Delay`),h=i(`${Oo}Duration`),m=Ff(c,h);let p=null,_=0,w=0;t===Pr?u>0&&(p=Pr,_=u,w=a.length):t===Oo?m>0&&(p=Oo,_=m,w=h.length):(_=Math.max(u,m),p=_>0?u>m?Pr:Oo:null,w=p?p===Pr?a.length:h.length:0);const D=p===Pr&&/\b(transform|all)(,|$)/.test(i(`${Pr}Property`).toString());return{type:p,timeout:_,propCount:w,hasTransform:D}}function Ff(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>Bf(s)+Bf(e[i])))}function Bf(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function $f(){return document.body.offsetHeight}function Sy(e,t,s){const i=e[To];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Fi=Symbol("_vod"),jf=Symbol("_vsh"),Wl={beforeMount(e,{value:t},{transition:s}){e[Fi]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):No(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),No(e,!0),i.enter(e)):i.leave(e,()=>{No(e,!1)}):No(e,t))},beforeUnmount(e,{value:t}){No(e,t)}};({}).NODE_ENV!=="production"&&(Wl.name="show");function No(e,t){e.style.display=t?e[Fi]:"none",e[jf]=!t}const Oy=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),Ty=/(^|;)\s*display\s*:/;function Ny(e,t,s){const i=e.style,o=ut(s);let a=!1;if(s&&!o){if(t)if(ut(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();s[c]==null&&Bi(i,c,"")}else for(const u in t)s[u]==null&&Bi(i,u,"");for(const u in s)u==="display"&&(a=!0),Bi(i,u,s[u])}else if(o){if(t!==s){const u=i[Oy];u&&(s+=";"+u),i.cssText=s,a=Ty.test(s)}}else t&&e.removeAttribute("style");Fi in e&&(e[Fi]=a?i.display:"",e[jf]&&(i.display="none"))}const Ay=/[^\\];\s*$/,Hf=/\s*!important$/;function Bi(e,t,s){if(me(s))s.forEach(i=>Bi(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&Ay.test(s)&&Qs(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=Iy(e,t);Hf.test(s)?e.setProperty(Sr(i),s.replace(Hf,""),"important"):e[i]=s}}const qf=["Webkit","Moz","ms"],Gl={};function Iy(e,t){const s=Gl[t];if(s)return s;let i=Kt(t);if(i!=="filter"&&i in e)return Gl[t]=i;i=Yr(i);for(let o=0;o<qf.length;o++){const a=qf[o]+i;if(a in e)return Gl[t]=a}return t}const zf="http://www.w3.org/1999/xlink";function Wf(e,t,s,i,o,a=t_(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(zf,t.slice(6,t.length)):e.setAttributeNS(zf,t,s):s==null||a&&!Vc(s)?e.removeAttribute(t):e.setAttribute(t,a?"":Is(s)?String(s):s)}function Gf(e,t,s,i,o){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Mf(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const c=a==="OPTION"?e.getAttribute("value")||"":e.value,h=s==null?e.type==="checkbox"?"on":"":String(s);(c!==h||!("_value"in e))&&(e.value=h),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Vc(s):s==null&&c==="string"?(s="",u=!0):c==="number"&&(s=0,u=!0)}try{e[t]=s}catch(c){({}).NODE_ENV!=="production"&&!u&&Qs(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,c)}u&&e.removeAttribute(o||t)}function kr(e,t,s,i){e.addEventListener(t,s,i)}function My(e,t,s,i){e.removeEventListener(t,s,i)}const Kf=Symbol("_vei");function Py(e,t,s,i,o=null){const a=e[Kf]||(e[Kf]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?Qf(i,t):i;else{const[c,h]=ky(t);if(i){const m=a[t]=Ly({}.NODE_ENV!=="production"?Qf(i,t):i,o);kr(e,c,m,h)}else u&&(My(e,c,u,h),a[t]=void 0)}}const Yf=/(?:Once|Passive|Capture)$/;function ky(e){let t;if(Yf.test(e)){t={};let i;for(;i=e.match(Yf);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Sr(e.slice(2)),t]}let Kl=0;const Vy=Promise.resolve(),Ry=()=>Kl||(Vy.then(()=>Kl=0),Kl=Date.now());function Ly(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ps(Uy(i,s.value),t,5,[i])};return s.value=e,s.attached=Ry(),s}function Qf(e,t){return De(e)||me(e)?e:(Qs(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),Ot)}function Uy(e,t){if(me(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>o=>!o._stopped&&i&&i(o))}else return t}const Zf=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Fy=(e,t,s,i,o,a)=>{const u=o==="svg";t==="class"?Sy(e,i,u):t==="style"?Ny(e,s,i):to(t)?ni(t)||Py(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):By(e,t,i,u))?(Gf(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Wf(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ut(i))?Gf(e,Kt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),Wf(e,t,i,u))};function By(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&Zf(t)&&De(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return Zf(t)&&ut(s)?!1:t in e}const kn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return me(t)?s=>Sn(t,s):t};function $y(e){e.target.composing=!0}function Jf(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dr=Symbol("_assign"),ws={created(e,{modifiers:{lazy:t,trim:s,number:i}},o){e[dr]=kn(o);const a=i||o.props&&o.props.type==="number";kr(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;s&&(c=c.trim()),a&&(c=ai(c)),e[dr](c)}),s&&kr(e,"change",()=>{e.value=e.value.trim()}),t||(kr(e,"compositionstart",$y),kr(e,"compositionend",Jf),kr(e,"change",Jf))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:o,number:a}},u){if(e[dr]=kn(u),e.composing)return;const c=(a||e.type==="number")&&!/^0\d/.test(e.value)?ai(e.value):e.value,h=t??"";c!==h&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||o&&e.value.trim()===h)||(e.value=h))}},$i={deep:!0,created(e,t,s){e[dr]=kn(s),kr(e,"change",()=>{const i=e._modelValue,o=Ao(e),a=e.checked,u=e[dr];if(me(i)){const c=Ja(i,o),h=c!==-1;if(a&&!h)u(i.concat(o));else if(!a&&h){const m=[...i];m.splice(c,1),u(m)}}else if(xn(i)){const c=new Set(i);a?c.add(o):c.delete(o),u(c)}else u(th(e,a))})},mounted:Xf,beforeUpdate(e,t,s){e[dr]=kn(s),Xf(e,t,s)}};function Xf(e,{value:t,oldValue:s},i){e._modelValue=t;let o;if(me(t))o=Ja(t,i.props.value)>-1;else if(xn(t))o=t.has(i.props.value);else{if(t===s)return;o=oo(t,th(e,!0))}e.checked!==o&&(e.checked=o)}const Yl={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const o=xn(t);kr(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?ai(Ao(u)):Ao(u));e[dr](e.multiple?o?new Set(a):a:a[0]),e._assigning=!0,ml(()=>{e._assigning=!1})}),e[dr]=kn(i)},mounted(e,{value:t}){eh(e,t)},beforeUpdate(e,t,s){e[dr]=kn(s)},updated(e,{value:t}){e._assigning||eh(e,t)}};function eh(e,t){const s=e.multiple,i=me(t);if(s&&!i&&!xn(t)){({}).NODE_ENV!=="production"&&Qs(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let o=0,a=e.options.length;o<a;o++){const u=e.options[o],c=Ao(u);if(s)if(i){const h=typeof c;h==="string"||h==="number"?u.selected=t.some(m=>String(m)===String(c)):u.selected=Ja(t,c)>-1}else u.selected=t.has(c);else if(oo(Ao(u),t)){e.selectedIndex!==o&&(e.selectedIndex=o);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Ao(e){return"_value"in e?e._value:e.value}function th(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const jy=["ctrl","shift","alt","meta"],Hy={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>jy.some(s=>e[`${s}Key`]&&!t.includes(s))},Lt=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(o,...a)=>{for(let u=0;u<t.length;u++){const c=Hy[t[u]];if(c&&c(o,t))return}return e(o,...a)})},qy=ft({patchProp:Fy},by);let sh;function zy(){return sh||(sh=Bv(qy))}const Wy=(...e)=>{const t=zy().createApp(...e);({}).NODE_ENV!=="production"&&(Ky(t),Yy(t));const{mount:s}=t;return t.mount=i=>{const o=Qy(i);if(!o)return;const a=t._component;!De(a)&&!a.render&&!a.template&&(a.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const u=s(o,!1,Gy(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),u},t};function Gy(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Ky(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>Jg(t)||Xg(t)||e_(t),writable:!1})}function Yy(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){Qs("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return Qs(i),s},set(){Qs(i)}})}}function Qy(e){if(ut(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&Qs(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&Qs('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Zy(){_y()}({}).NODE_ENV!=="production"&&Zy();var Jy=!1;function Xy(){return rh().__VUE_DEVTOOLS_GLOBAL_HOOK__}function rh(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const eb=typeof Proxy=="function",tb="devtools-plugin:setup",sb="plugin:settings:set";let Vn,Ql;function rb(){var e;return Vn!==void 0||(typeof window<"u"&&window.performance?(Vn=!0,Ql=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Vn=!0,Ql=globalThis.perf_hooks.performance):Vn=!1),Vn}function nb(){return rb()?Ql.now():Date.now()}class ob{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const c=t.settings[u];i[u]=c.defaultValue}const o=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(o),c=JSON.parse(u);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(o,JSON.stringify(u))}catch{}a=u},now(){return nb()}},s&&s.on(sb,(u,c)=>{u===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(u,c)=>this.target?this.target.on[c]:(...h)=>{this.onQueue.push({method:c,args:h})}}),this.proxiedTarget=new Proxy({},{get:(u,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...h)=>(this.targetQueue.push({method:c,args:h,resolve:()=>{}}),this.fallbacks[c](...h)):(...h)=>new Promise(m=>{this.targetQueue.push({method:c,args:h,resolve:m})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function Zl(e,t){const s=e,i=rh(),o=Xy(),a=eb&&s.enableEarlyProxy;if(o&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))o.emit(tb,e,t);else{const u=a?new ob(s,o):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const ib={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var dn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(dn||(dn={}));const Jl=typeof window<"u",nh=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function ab(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function Xl(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){ah(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function oh(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function ji(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const Hi=typeof navigator=="object"?navigator:{userAgent:""},ih=(()=>/Macintosh/.test(Hi.userAgent)&&/AppleWebKit/.test(Hi.userAgent)&&!/Safari/.test(Hi.userAgent))(),ah=Jl?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!ih?lb:"msSaveOrOpenBlob"in Hi?ub:cb:()=>{};function lb(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?oh(i.href)?Xl(e,t,s):(i.target="_blank",ji(i)):ji(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){ji(i)},0))}function ub(e,t="download",s){if(typeof e=="string")if(oh(e))Xl(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){ji(i)})}else navigator.msSaveOrOpenBlob(ab(e,s),t)}function cb(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return Xl(e,t,s);const o=e.type==="application/octet-stream",a=/constructor/i.test(String(nh.HTMLElement))||"safari"in nh,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||o&&a||ih)&&typeof FileReader<"u"){const c=new FileReader;c.onloadend=function(){let h=c.result;if(typeof h!="string")throw i=null,new Error("Wrong reader.result type");h=u?h:h.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=h:location.assign(h),i=null},c.readAsDataURL(e)}else{const c=URL.createObjectURL(e);i?i.location.assign(c):location.href=c,i=null,setTimeout(function(){URL.revokeObjectURL(c)},4e4)}}function Pt(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function eu(e){return"_a"in e&&"install"in e}function lh(){if(!("clipboard"in navigator))return Pt("Your browser doesn't support the Clipboard API","error"),!0}function uh(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(Pt('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function db(e){if(!lh())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),Pt("Global state copied to clipboard.")}catch(t){if(uh(t))return;Pt("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function fb(e){if(!lh())try{ch(e,JSON.parse(await navigator.clipboard.readText())),Pt("Global state pasted from clipboard.")}catch(t){if(uh(t))return;Pt("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function hb(e){try{ah(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){Pt("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let fr;function pb(){fr||(fr=document.createElement("input"),fr.type="file",fr.accept=".json");function e(){return new Promise((t,s)=>{fr.onchange=async()=>{const i=fr.files;if(!i)return t(null);const o=i.item(0);return t(o?{text:await o.text(),file:o}:null)},fr.oncancel=()=>t(null),fr.onerror=s,fr.click()})}return e}async function mb(e){try{const s=await pb()();if(!s)return;const{text:i,file:o}=s;ch(e,JSON.parse(i)),Pt(`Global state imported from "${o.name}".`)}catch(t){Pt("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function ch(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Us(e){return{_custom:{display:e}}}const dh="🍍 Pinia (root)",qi="_root";function gb(e){return eu(e)?{id:qi,label:dh}:{id:e.$id,label:e.$id}}function _b(e){if(eu(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((c,h)=>(c[h]=u[h],c),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function vb(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Us(e.type),key:Us(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function yb(e){switch(e){case dn.direct:return"mutation";case dn.patchFunction:return"$patch";case dn.patchObject:return"$patch";default:return"unknown"}}let Rn=!0;const zi=[],fn="pinia:mutations",qt="pinia",{assign:bb}=Object,Wi=e=>"🍍 "+e;function wb(e,t){Zl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:zi,app:e},s=>{typeof s.now!="function"&&Pt("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:fn,label:"Pinia 🍍",color:15064968}),s.addInspector({id:qt,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{db(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await fb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{hb(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await mb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const o=t._s.get(i);o?typeof o.$reset!="function"?Pt(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(o.$reset(),Pt(`Store "${i}" reset.`)):Pt(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,o)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(c=>{i.instanceData.state.push({type:Wi(c.$id),key:"state",editable:!0,value:c._isOptionsAPI?{_custom:{value:Ie(c.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>c.$reset()}]}}:Object.keys(c.$state).reduce((h,m)=>(h[m]=c.$state[m],h),{})}),c._getters&&c._getters.length&&i.instanceData.state.push({type:Wi(c.$id),key:"getters",editable:!1,value:c._getters.reduce((h,m)=>{try{h[m]=c[m]}catch(p){h[m]=p}return h},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===qt){let o=[t];o=o.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?o.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):dh.toLowerCase().includes(i.filter.toLowerCase())):o).map(gb)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===qt){const o=i.nodeId===qi?t:t._s.get(i.nodeId);if(!o)return;o&&(i.nodeId!==qi&&(globalThis.$store=Ie(o)),i.state=_b(o))}}),s.on.editInspectorState((i,o)=>{if(i.app===e&&i.inspectorId===qt){const a=i.nodeId===qi?t:t._s.get(i.nodeId);if(!a)return Pt(`store "${i.nodeId}" not found`,"error");const{path:u}=i;eu(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Rn=!1,i.set(a,u,i.state.value),Rn=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const o=i.type.replace(/^🍍\s*/,""),a=t._s.get(o);if(!a)return Pt(`store "${o}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return Pt(`Invalid path for store "${o}":
${u}
Only state can be modified.`);u[0]="$state",Rn=!1,i.set(a,u,i.state.value),Rn=!0}})})}function Eb(e,t){zi.includes(Wi(t.$id))||zi.push(Wi(t.$id)),Zl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:zi,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:c,name:h,args:m})=>{const p=fh++;s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🛫 "+h,subtitle:"start",data:{store:Us(t.$id),action:Us(h),args:m},groupId:p}}),u(_=>{Vr=void 0,s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🛬 "+h,subtitle:"end",data:{store:Us(t.$id),action:Us(h),args:m,result:_},groupId:p}})}),c(_=>{Vr=void 0,s.addTimelineEvent({layerId:fn,event:{time:i(),logType:"error",title:"💥 "+h,subtitle:"end",data:{store:Us(t.$id),action:Us(h),args:m,error:_},groupId:p}})})},!0),t._customProperties.forEach(u=>{Pn(()=>Tr(t[u]),(c,h)=>{s.notifyComponentUpdate(),s.sendInspectorState(qt),Rn&&s.addTimelineEvent({layerId:fn,event:{time:i(),title:"Change",subtitle:u,data:{newValue:c,oldValue:h},groupId:Vr}})},{deep:!0})}),t.$subscribe(({events:u,type:c},h)=>{if(s.notifyComponentUpdate(),s.sendInspectorState(qt),!Rn)return;const m={time:i(),title:yb(c),data:bb({store:Us(t.$id)},vb(u)),groupId:Vr};c===dn.patchFunction?m.subtitle="⤵️":c===dn.patchObject?m.subtitle="🧩":u&&!Array.isArray(u)&&(m.subtitle=u.type),u&&(m.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:fn,event:m})},{detached:!0,flush:"sync"});const o=t._hotUpdate;t._hotUpdate=dl(u=>{o(u),s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Us(t.$id),info:Us("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&Pt(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&Pt(`"${t.$id}" store installed 🆕`)})}let fh=0,Vr;function hh(e,t,s){const i=t.reduce((o,a)=>(o[a]=Ie(e)[a],o),{});for(const o in i)e[o]=function(){const a=fh,u=s?new Proxy(e,{get(...h){return Vr=a,Reflect.get(...h)},set(...h){return Vr=a,Reflect.set(...h)}}):e;Vr=a;const c=i[o].apply(u,arguments);return Vr=void 0,c}}function Cb({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){hh(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Ie(t)._hotUpdate=function(o){i.apply(this,arguments),hh(t,Object.keys(o._hmrPayload.actions),!!t._isOptionsAPI)}}Eb(e,t)}}function Db(){const e=r_(!0),t=e.run(()=>id({}));let s=[],i=[];const o=dl({install(a){o._a=a,a.provide(ib,o),a.config.globalProperties.$pinia=o,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&Jl&&wb(a,o),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!Jy?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&Jl&&typeof Proxy<"u"&&o.use(Cb),o}const WV="",He=(e,t)=>{const s=e.__vccOpts||e;for(const[i,o]of t)s[i]=o;return s},xb={name:"App",mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}}},Sb={id:"app"};function Ob(e,t,s,i,o,a){const u=ee("router-view");return O(),N("div",Sb,[P(u)])}const Tb=He(xb,[["render",Ob]]);/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const hr=typeof document<"u";function ph(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Nb(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&ph(e.default)}const Je=Object.assign;function tu(e,t){const s={};for(const i in t){const o=t[i];s[i]=ds(o)?o.map(e):e(o)}return s}const Io=()=>{},ds=Array.isArray;function $e(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const mh=/#/g,Ab=/&/g,Ib=/\//g,Mb=/=/g,Pb=/\?/g,gh=/\+/g,kb=/%5B/g,Vb=/%5D/g,_h=/%5E/g,Rb=/%60/g,vh=/%7B/g,Lb=/%7C/g,yh=/%7D/g,Ub=/%20/g;function su(e){return encodeURI(""+e).replace(Lb,"|").replace(kb,"[").replace(Vb,"]")}function Fb(e){return su(e).replace(vh,"{").replace(yh,"}").replace(_h,"^")}function ru(e){return su(e).replace(gh,"%2B").replace(Ub,"+").replace(mh,"%23").replace(Ab,"%26").replace(Rb,"`").replace(vh,"{").replace(yh,"}").replace(_h,"^")}function Bb(e){return ru(e).replace(Mb,"%3D")}function $b(e){return su(e).replace(mh,"%23").replace(Pb,"%3F")}function jb(e){return e==null?"":$b(e).replace(Ib,"%2F")}function Ln(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&$e(`Error decoding "${e}". Using original value`)}return""+e}const Hb=/\/$/,qb=e=>e.replace(Hb,"");function nu(e,t,s="/"){let i,o={},a="",u="";const c=t.indexOf("#");let h=t.indexOf("?");return c<h&&c>=0&&(h=-1),h>-1&&(i=t.slice(0,h),a=t.slice(h+1,c>-1?c:t.length),o=e(a)),c>-1&&(i=i||t.slice(0,c),u=t.slice(c,t.length)),i=Gb(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:o,hash:Ln(u)}}function zb(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function bh(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function wh(e,t,s){const i=t.matched.length-1,o=s.matched.length-1;return i>-1&&i===o&&Rr(t.matched[i],s.matched[o])&&Eh(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Rr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Eh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Wb(e[s],t[s]))return!1;return!0}function Wb(e,t){return ds(e)?Ch(e,t):ds(t)?Ch(t,e):e===t}function Ch(e,t){return ds(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function Gb(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return $e(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),o=i[i.length-1];(o===".."||o===".")&&i.push("");let a=s.length-1,u,c;for(u=0;u<i.length;u++)if(c=i[u],c!==".")if(c==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Lr={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Mo;(function(e){e.pop="pop",e.push="push"})(Mo||(Mo={}));var Po;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Po||(Po={}));function Kb(e){if(!e)if(hr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),qb(e)}const Yb=/^[^#]+#/;function Qb(e,t){return e.replace(Yb,"#")+t}function Zb(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const Gi=()=>({left:window.scrollX,top:window.scrollY});function Jb(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){$e(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{$e(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const o=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!o){({}).NODE_ENV!=="production"&&$e(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=Zb(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Dh(e,t){return(history.state?history.state.position-t:-1)+e}const ou=new Map;function Xb(e,t){ou.set(e,t)}function e1(e){const t=ou.get(e);return ou.delete(e),t}let t1=()=>location.protocol+"//"+location.host;function xh(e,t){const{pathname:s,search:i,hash:o}=t,a=e.indexOf("#");if(a>-1){let c=o.includes(e.slice(a))?e.slice(a).length:1,h=o.slice(c);return h[0]!=="/"&&(h="/"+h),bh(h,"")}return bh(s,e)+i+o}function s1(e,t,s,i){let o=[],a=[],u=null;const c=({state:w})=>{const D=xh(e,location),k=s.value,F=t.value;let te=0;if(w){if(s.value=D,t.value=w,u&&u===k){u=null;return}te=F?w.position-F.position:0}else i(D);o.forEach(A=>{A(s.value,k,{delta:te,type:Mo.pop,direction:te?te>0?Po.forward:Po.back:Po.unknown})})};function h(){u=s.value}function m(w){o.push(w);const D=()=>{const k=o.indexOf(w);k>-1&&o.splice(k,1)};return a.push(D),D}function p(){const{history:w}=window;w.state&&w.replaceState(Je({},w.state,{scroll:Gi()}),"")}function _(){for(const w of a)w();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:h,listen:m,destroy:_}}function Sh(e,t,s,i=!1,o=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:o?Gi():null}}function r1(e){const{history:t,location:s}=window,i={value:xh(e,s)},o={value:t.state};o.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(h,m,p){const _=e.indexOf("#"),w=_>-1?(s.host&&document.querySelector("base")?e:e.slice(_))+h:t1()+e+h;try{t[p?"replaceState":"pushState"](m,"",w),o.value=m}catch(D){({}).NODE_ENV!=="production"?$e("Error with push/replace State",D):console.error(D),s[p?"replace":"assign"](w)}}function u(h,m){const p=Je({},t.state,Sh(o.value.back,h,o.value.forward,!0),m,{position:o.value.position});a(h,p,!0),i.value=h}function c(h,m){const p=Je({},o.value,t.state,{forward:h,scroll:Gi()});({}).NODE_ENV!=="production"&&!t.state&&$e(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(p.current,p,!0);const _=Je({},Sh(i.value,h,null),{position:p.position+1},m);a(h,_,!1),i.value=h}return{location:i,state:o,push:c,replace:u}}function n1(e){e=Kb(e);const t=r1(e),s=s1(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const o=Je({location:"",base:e,go:i,createHref:Qb.bind(null,e)},t,s);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Ki(e){return typeof e=="string"||e&&typeof e=="object"}function Oh(e){return typeof e=="string"||typeof e=="symbol"}const iu=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var Th;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Th||(Th={}));const o1={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${a1(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function Un(e,t){return{}.NODE_ENV!=="production"?Je(new Error(o1[e](t)),{type:e,[iu]:!0},t):Je(new Error,{type:e,[iu]:!0},t)}function pr(e,t){return e instanceof Error&&iu in e&&(t==null||!!(e.type&t))}const i1=["params","query","hash"];function a1(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of i1)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const Nh="[^/]+?",l1={sensitive:!1,strict:!1,start:!0,end:!0},u1=/[.+*?^${}()[\]/\\]/g;function c1(e,t){const s=Je({},l1,t),i=[];let o=s.start?"^":"";const a=[];for(const m of e){const p=m.length?[]:[90];s.strict&&!m.length&&(o+="/");for(let _=0;_<m.length;_++){const w=m[_];let D=40+(s.sensitive?.25:0);if(w.type===0)_||(o+="/"),o+=w.value.replace(u1,"\\$&"),D+=40;else if(w.type===1){const{value:k,repeatable:F,optional:te,regexp:A}=w;a.push({name:k,repeatable:F,optional:te});const se=A||Nh;if(se!==Nh){D+=10;try{new RegExp(`(${se})`)}catch(ye){throw new Error(`Invalid custom RegExp for param "${k}" (${se}): `+ye.message)}}let G=F?`((?:${se})(?:/(?:${se}))*)`:`(${se})`;_||(G=te&&m.length<2?`(?:/${G})`:"/"+G),te&&(G+="?"),o+=G,D+=20,te&&(D+=-8),F&&(D+=-20),se===".*"&&(D+=-50)}p.push(D)}i.push(p)}if(s.strict&&s.end){const m=i.length-1;i[m][i[m].length-1]+=.7000000000000001}s.strict||(o+="/?"),s.end?o+="$":s.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const u=new RegExp(o,s.sensitive?"":"i");function c(m){const p=m.match(u),_={};if(!p)return null;for(let w=1;w<p.length;w++){const D=p[w]||"",k=a[w-1];_[k.name]=D&&k.repeatable?D.split("/"):D}return _}function h(m){let p="",_=!1;for(const w of e){(!_||!p.endsWith("/"))&&(p+="/"),_=!1;for(const D of w)if(D.type===0)p+=D.value;else if(D.type===1){const{value:k,repeatable:F,optional:te}=D,A=k in m?m[k]:"";if(ds(A)&&!F)throw new Error(`Provided param "${k}" is an array but it is not repeatable (* or + modifiers)`);const se=ds(A)?A.join("/"):A;if(!se)if(te)w.length<2&&(p.endsWith("/")?p=p.slice(0,-1):_=!0);else throw new Error(`Missing required param "${k}"`);p+=se}}return p||"/"}return{re:u,score:i,keys:a,parse:c,stringify:h}}function d1(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Ah(e,t){let s=0;const i=e.score,o=t.score;for(;s<i.length&&s<o.length;){const a=d1(i[s],o[s]);if(a)return a;s++}if(Math.abs(o.length-i.length)===1){if(Ih(i))return 1;if(Ih(o))return-1}return o.length-i.length}function Ih(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const f1={type:0,value:""},h1=/[a-zA-Z0-9_]/;function p1(e){if(!e)return[[]];if(e==="/")return[[f1]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(D){throw new Error(`ERR (${s})/"${m}": ${D}`)}let s=0,i=s;const o=[];let a;function u(){a&&o.push(a),a=[]}let c=0,h,m="",p="";function _(){m&&(s===0?a.push({type:0,value:m}):s===1||s===2||s===3?(a.length>1&&(h==="*"||h==="+")&&t(`A repeatable param (${m}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:m,regexp:p,repeatable:h==="*"||h==="+",optional:h==="*"||h==="?"})):t("Invalid state to consume buffer"),m="")}function w(){m+=h}for(;c<e.length;){if(h=e[c++],h==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:h==="/"?(m&&_(),u()):h===":"?(_(),s=1):w();break;case 4:w(),s=i;break;case 1:h==="("?s=2:h1.test(h)?w():(_(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--);break;case 2:h===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+h:s=3:p+=h;break;case 3:_(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--,p="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${m}"`),_(),u(),o}function m1(e,t,s){const i=c1(p1(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&$e(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const o=Je(i,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function g1(e,t){const s=[],i=new Map;t=Vh({strict:!1,end:!0,sensitive:!1},t);function o(_){return i.get(_)}function a(_,w,D){const k=!D,F=Ph(_);({}).NODE_ENV!=="production"&&b1(F,w),F.aliasOf=D&&D.record;const te=Vh(t,_),A=[F];if("alias"in _){const ye=typeof _.alias=="string"?[_.alias]:_.alias;for(const Z of ye)A.push(Ph(Je({},F,{components:D?D.record.components:F.components,path:Z,aliasOf:D?D.record:F})))}let se,G;for(const ye of A){const{path:Z}=ye;if(w&&Z[0]!=="/"){const fe=w.record.path,ve=fe[fe.length-1]==="/"?"":"/";ye.path=w.record.path+(Z&&ve+Z)}if({}.NODE_ENV!=="production"&&ye.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(se=m1(ye,w,te),{}.NODE_ENV!=="production"&&w&&Z[0]==="/"&&E1(se,w),D?(D.alias.push(se),{}.NODE_ENV!=="production"&&y1(D,se)):(G=G||se,G!==se&&G.alias.push(se),k&&_.name&&!kh(se)&&({}.NODE_ENV!=="production"&&w1(_,w),u(_.name))),Rh(se)&&h(se),F.children){const fe=F.children;for(let ve=0;ve<fe.length;ve++)a(fe[ve],se,D&&D.children[ve])}D=D||se}return G?()=>{u(G)}:Io}function u(_){if(Oh(_)){const w=i.get(_);w&&(i.delete(_),s.splice(s.indexOf(w),1),w.children.forEach(u),w.alias.forEach(u))}else{const w=s.indexOf(_);w>-1&&(s.splice(w,1),_.record.name&&i.delete(_.record.name),_.children.forEach(u),_.alias.forEach(u))}}function c(){return s}function h(_){const w=C1(_,s);s.splice(w,0,_),_.record.name&&!kh(_)&&i.set(_.record.name,_)}function m(_,w){let D,k={},F,te;if("name"in _&&_.name){if(D=i.get(_.name),!D)throw Un(1,{location:_});if({}.NODE_ENV!=="production"){const G=Object.keys(_.params||{}).filter(ye=>!D.keys.find(Z=>Z.name===ye));G.length&&$e(`Discarded invalid param(s) "${G.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}te=D.record.name,k=Je(Mh(w.params,D.keys.filter(G=>!G.optional).concat(D.parent?D.parent.keys.filter(G=>G.optional):[]).map(G=>G.name)),_.params&&Mh(_.params,D.keys.map(G=>G.name))),F=D.stringify(k)}else if(_.path!=null)F=_.path,{}.NODE_ENV!=="production"&&!F.startsWith("/")&&$e(`The Matcher cannot resolve relative paths but received "${F}". Unless you directly called \`matcher.resolve("${F}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),D=s.find(G=>G.re.test(F)),D&&(k=D.parse(F),te=D.record.name);else{if(D=w.name?i.get(w.name):s.find(G=>G.re.test(w.path)),!D)throw Un(1,{location:_,currentLocation:w});te=D.record.name,k=Je({},w.params,_.params),F=D.stringify(k)}const A=[];let se=D;for(;se;)A.unshift(se.record),se=se.parent;return{name:te,path:F,params:k,matched:A,meta:v1(A)}}e.forEach(_=>a(_));function p(){s.length=0,i.clear()}return{addRoute:a,resolve:m,removeRoute:u,clearRoutes:p,getRoutes:c,getRecordMatcher:o}}function Mh(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function Ph(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:_1(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function _1(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function kh(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function v1(e){return e.reduce((t,s)=>Je(t,s.meta),{})}function Vh(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function au(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function y1(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(au.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(au.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function b1(e,t){t&&t.record.name&&!e.name&&!e.path&&$e(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function w1(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function E1(e,t){for(const s of t.keys)if(!e.keys.find(au.bind(null,s)))return $e(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function C1(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;Ah(e,t[a])<0?i=a:s=a+1}const o=D1(e);return o&&(i=t.lastIndexOf(o,i-1),{}.NODE_ENV!=="production"&&i<0&&$e(`Finding ancestor route "${o.record.path}" failed for "${e.record.path}"`)),i}function D1(e){let t=e;for(;t=t.parent;)if(Rh(t)&&Ah(e,t)===0)return t}function Rh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function x1(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<i.length;++o){const a=i[o].replace(gh," "),u=a.indexOf("="),c=Ln(u<0?a:a.slice(0,u)),h=u<0?null:Ln(a.slice(u+1));if(c in t){let m=t[c];ds(m)||(m=t[c]=[m]),m.push(h)}else t[c]=h}return t}function Lh(e){let t="";for(let s in e){const i=e[s];if(s=Bb(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(ds(i)?i.map(a=>a&&ru(a)):[i&&ru(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function S1(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=ds(i)?i.map(o=>o==null?null:""+o):i==null?i:""+i)}return t}const O1=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),Uh=Symbol({}.NODE_ENV!=="production"?"router view depth":""),Yi=Symbol({}.NODE_ENV!=="production"?"router":""),lu=Symbol({}.NODE_ENV!=="production"?"route location":""),uu=Symbol({}.NODE_ENV!=="production"?"router view location":"");function ko(){let e=[];function t(i){return e.push(i),()=>{const o=e.indexOf(i);o>-1&&e.splice(o,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Ur(e,t,s,i,o,a=u=>u()){const u=i&&(i.enterCallbacks[o]=i.enterCallbacks[o]||[]);return()=>new Promise((c,h)=>{const m=w=>{w===!1?h(Un(4,{from:s,to:t})):w instanceof Error?h(w):Ki(w)?h(Un(2,{from:t,to:w})):(u&&i.enterCallbacks[o]===u&&typeof w=="function"&&u.push(w),c())},p=a(()=>e.call(i&&i.instances[o],t,s,{}.NODE_ENV!=="production"?T1(m,t,s):m));let _=Promise.resolve(p);if(e.length<3&&(_=_.then(m)),{}.NODE_ENV!=="production"&&e.length>2){const w=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof p=="object"&&"then"in p)_=_.then(D=>m._called?D:($e(w),Promise.reject(new Error("Invalid navigation guard"))));else if(p!==void 0&&!m._called){$e(w),h(new Error("Invalid navigation guard"));return}}_.catch(w=>h(w))})}function T1(e,t,s){let i=0;return function(){i++===1&&$e(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function cu(e,t,s,i,o=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&$e(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const c in u.components){let h=u.components[c];if({}.NODE_ENV!=="production"){if(!h||typeof h!="object"&&typeof h!="function")throw $e(`Component "${c}" in record with path "${u.path}" is not a valid component. Received "${String(h)}".`),new Error("Invalid route component");if("then"in h){$e(`Component "${c}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const m=h;h=()=>m}else h.__asyncLoader&&!h.__warnedDefineAsync&&(h.__warnedDefineAsync=!0,$e(`Component "${c}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(ph(h)){const p=(h.__vccOpts||h)[t];p&&a.push(Ur(p,s,i,u,c,o))}else{let m=h();({}).NODE_ENV!=="production"&&!("catch"in m)&&($e(`Component "${c}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),m=Promise.resolve(m)),a.push(()=>m.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${c}" at "${u.path}"`);const _=Nb(p)?p.default:p;u.mods[c]=p,u.components[c]=_;const D=(_.__vccOpts||_)[t];return D&&Ur(D,s,i,u,c,o)()}))}}}return a}function Fh(e){const t=Vs(Yi),s=Vs(lu);let i=!1,o=null;const a=Ls(()=>{const p=Tr(e.to);return{}.NODE_ENV!=="production"&&(!i||p!==o)&&(Ki(p)||(i?$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- previous to:`,o,`
- props:`,e):$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- props:`,e)),o=p,i=!0),t.resolve(p)}),u=Ls(()=>{const{matched:p}=a.value,{length:_}=p,w=p[_-1],D=s.matched;if(!w||!D.length)return-1;const k=D.findIndex(Rr.bind(null,w));if(k>-1)return k;const F=Bh(p[_-2]);return _>1&&Bh(w)===F&&D[D.length-1].path!==F?D.findIndex(Rr.bind(null,p[_-2])):k}),c=Ls(()=>u.value>-1&&M1(s.params,a.value.params)),h=Ls(()=>u.value>-1&&u.value===s.matched.length-1&&Eh(s.params,a.value.params));function m(p={}){if(I1(p)){const _=t[Tr(e.replace)?"replace":"push"](Tr(e.to)).catch(Io);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>_),_}return Promise.resolve()}if({}.NODE_ENV!=="production"&&hr){const p=Vi();if(p){const _={route:a.value,isActive:c.value,isExactActive:h.value,error:null};p.__vrl_devtools=p.__vrl_devtools||[],p.__vrl_devtools.push(_),Wv(()=>{_.route=a.value,_.isActive=c.value,_.isExactActive=h.value,_.error=Ki(Tr(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:Ls(()=>a.value.href),isActive:c,isExactActive:h,navigate:m}}function N1(e){return e.length===1?e[0]:e}const A1=Rd({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Fh,setup(e,{slots:t}){const s=fi(Fh(e)),{options:i}=Vs(Yi),o=Ls(()=>({[$h(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[$h(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&N1(t.default(s));return e.custom?a:Hl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:o.value},a)}}});function I1(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function M1(e,t){for(const s in t){const i=t[s],o=e[s];if(typeof i=="string"){if(i!==o)return!1}else if(!ds(o)||o.length!==i.length||i.some((a,u)=>a!==o[u]))return!1}return!0}function Bh(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const $h=(e,t,s)=>e??t??s,P1=Rd({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&V1();const i=Vs(uu),o=Ls(()=>e.route||i.value),a=Vs(Uh,0),u=Ls(()=>{let m=Tr(a);const{matched:p}=o.value;let _;for(;(_=p[m])&&!_.components;)m++;return m}),c=Ls(()=>o.value.matched[u.value]);Ai(Uh,Ls(()=>u.value+1)),Ai(O1,c),Ai(uu,o);const h=id();return Pn(()=>[h.value,c.value,e.name],([m,p,_],[w,D,k])=>{p&&(p.instances[_]=m,D&&D!==p&&m&&m===w&&(p.leaveGuards.size||(p.leaveGuards=D.leaveGuards),p.updateGuards.size||(p.updateGuards=D.updateGuards))),m&&p&&(!D||!Rr(p,D)||!w)&&(p.enterCallbacks[_]||[]).forEach(F=>F(m))},{flush:"post"}),()=>{const m=o.value,p=e.name,_=c.value,w=_&&_.components[p];if(!w)return jh(s.default,{Component:w,route:m});const D=_.props[p],k=D?D===!0?m.params:typeof D=="function"?D(m):D:null,te=Hl(w,Je({},k,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(_.instances[p]=null)},ref:h}));if({}.NODE_ENV!=="production"&&hr&&te.ref){const A={depth:u.value,name:_.name,path:_.path,meta:_.meta};(ds(te.ref)?te.ref.map(G=>G.i):[te.ref.i]).forEach(G=>{G.__vrv_devtools=A})}return jh(s.default,{Component:te,route:m})||te}}});function jh(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const k1=P1;function V1(){const e=Vi(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";$e(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function Vo(e,t){const s=Je({},e,{matched:e.matched.map(i=>W1(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function Qi(e){return{_custom:{display:e}}}let R1=0;function L1(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=R1++;Zl({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},o=>{typeof o.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),o.on.inspectComponent((p,_)=>{p.instanceData&&p.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:Vo(t.currentRoute.value,"Current Route")})}),o.on.visitComponentTree(({treeNode:p,componentInstance:_})=>{if(_.__vrv_devtools){const w=_.__vrv_devtools;p.tags.push({label:(w.name?`${w.name.toString()}: `:"")+w.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:Hh})}ds(_.__vrl_devtools)&&(_.__devtoolsApi=o,_.__vrl_devtools.forEach(w=>{let D=w.route.path,k=Wh,F="",te=0;w.error?(D=w.error,k=j1,te=H1):w.isExactActive?(k=zh,F="This is exactly active"):w.isActive&&(k=qh,F="This link is active"),p.tags.push({label:D,textColor:te,tooltip:F,backgroundColor:k})}))}),Pn(t.currentRoute,()=>{h(),o.notifyComponentUpdate(),o.sendInspectorTree(c),o.sendInspectorState(c)});const a="router:navigations:"+i;o.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((p,_)=>{o.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:_.fullPath,logType:"error",time:o.now(),data:{error:p},groupId:_.meta.__navigationId}})});let u=0;t.beforeEach((p,_)=>{const w={guard:Qi("beforeEach"),from:Vo(_,"Current Location during this navigation"),to:Vo(p,"Target location")};Object.defineProperty(p.meta,"__navigationId",{value:u++}),o.addTimelineEvent({layerId:a,event:{time:o.now(),title:"Start of navigation",subtitle:p.fullPath,data:w,groupId:p.meta.__navigationId}})}),t.afterEach((p,_,w)=>{const D={guard:Qi("afterEach")};w?(D.failure={_custom:{type:Error,readOnly:!0,display:w?w.message:"",tooltip:"Navigation Failure",value:w}},D.status=Qi("❌")):D.status=Qi("✅"),D.from=Vo(_,"Current Location during this navigation"),D.to=Vo(p,"Target location"),o.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:p.fullPath,time:o.now(),data:D,logType:w?"warning":"default",groupId:p.meta.__navigationId}})});const c="router-inspector:"+i;o.addInspector({id:c,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function h(){if(!m)return;const p=m;let _=s.getRoutes().filter(w=>!w.parent||!w.parent.record.components);_.forEach(Yh),p.filter&&(_=_.filter(w=>du(w,p.filter.toLowerCase()))),_.forEach(w=>Kh(w,t.currentRoute.value)),p.rootNodes=_.map(Gh)}let m;o.on.getInspectorTree(p=>{m=p,p.app===e&&p.inspectorId===c&&h()}),o.on.getInspectorState(p=>{if(p.app===e&&p.inspectorId===c){const w=s.getRoutes().find(D=>D.record.__vd_id===p.nodeId);w&&(p.state={options:F1(w)})}}),o.sendInspectorTree(c),o.sendInspectorState(c)})}function U1(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function F1(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${U1(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const Hh=15485081,qh=2450411,zh=8702998,B1=2282478,Wh=16486972,$1=6710886,j1=16704226,H1=12131356;function Gh(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:B1}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Wh}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:Hh}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:zh}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:qh}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:$1});let i=s.__vd_id;return i==null&&(i=String(q1++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Gh)}}let q1=0;const z1=/^\/(.*)\/([a-z]*)$/;function Kh(e,t){const s=t.matched.length&&Rr(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>Rr(i,e.record))),e.children.forEach(i=>Kh(i,t))}function Yh(e){e.__vd_match=!1,e.children.forEach(Yh)}function du(e,t){const s=String(e.re).match(z1);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>du(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const o=e.record.path.toLowerCase(),a=Ln(o);return!t.startsWith("/")&&(a.includes(t)||o.includes(t))||a.startsWith(t)||o.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>du(u,t))}function W1(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function G1(e){const t=g1(e.routes,e),s=e.parseQuery||x1,i=e.stringifyQuery||Lh,o=e.history;if({}.NODE_ENV!=="production"&&!o)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=ko(),u=ko(),c=ko(),h=C_(Lr);let m=Lr;hr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=tu.bind(null,R=>""+R),_=tu.bind(null,jb),w=tu.bind(null,Ln);function D(R,oe){let ne,pe;return Oh(R)?(ne=t.getRecordMatcher(R),{}.NODE_ENV!=="production"&&!ne&&$e(`Parent route "${String(R)}" not found when adding child route`,oe),pe=oe):pe=R,t.addRoute(pe,ne)}function k(R){const oe=t.getRecordMatcher(R);oe?t.removeRoute(oe):{}.NODE_ENV!=="production"&&$e(`Cannot remove non-existent route "${String(R)}"`)}function F(){return t.getRoutes().map(R=>R.record)}function te(R){return!!t.getRecordMatcher(R)}function A(R,oe){if(oe=Je({},oe||h.value),typeof R=="string"){const b=nu(s,R,oe.path),C=t.resolve({path:b.path},oe),M=o.createHref(b.fullPath);return{}.NODE_ENV!=="production"&&(M.startsWith("//")?$e(`Location "${R}" resolved to "${M}". A resolved location cannot start with multiple slashes.`):C.matched.length||$e(`No match found for location with path "${R}"`)),Je(b,C,{params:w(C.params),hash:Ln(b.hash),redirectedFrom:void 0,href:M})}if({}.NODE_ENV!=="production"&&!Ki(R))return $e(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,R),A({});let ne;if(R.path!=null)({}).NODE_ENV!=="production"&&"params"in R&&!("name"in R)&&Object.keys(R.params).length&&$e(`Path "${R.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),ne=Je({},R,{path:nu(s,R.path,oe.path).path});else{const b=Je({},R.params);for(const C in b)b[C]==null&&delete b[C];ne=Je({},R,{params:_(b)}),oe.params=_(oe.params)}const pe=t.resolve(ne,oe),Pe=R.hash||"";({}).NODE_ENV!=="production"&&Pe&&!Pe.startsWith("#")&&$e(`A \`hash\` should always start with the character "#". Replace "${Pe}" with "#${Pe}".`),pe.params=p(w(pe.params));const ot=zb(i,Je({},R,{hash:Fb(Pe),path:pe.path})),ke=o.createHref(ot);return{}.NODE_ENV!=="production"&&(ke.startsWith("//")?$e(`Location "${R}" resolved to "${ke}". A resolved location cannot start with multiple slashes.`):pe.matched.length||$e(`No match found for location with path "${R.path!=null?R.path:R}"`)),Je({fullPath:ot,hash:Pe,query:i===Lh?S1(R.query):R.query||{}},pe,{redirectedFrom:void 0,href:ke})}function se(R){return typeof R=="string"?nu(s,R,h.value.path):Je({},R)}function G(R,oe){if(m!==R)return Un(8,{from:oe,to:R})}function ye(R){return ve(R)}function Z(R){return ye(Je(se(R),{replace:!0}))}function fe(R){const oe=R.matched[R.matched.length-1];if(oe&&oe.redirect){const{redirect:ne}=oe;let pe=typeof ne=="function"?ne(R):ne;if(typeof pe=="string"&&(pe=pe.includes("?")||pe.includes("#")?pe=se(pe):{path:pe},pe.params={}),{}.NODE_ENV!=="production"&&pe.path==null&&!("name"in pe))throw $e(`Invalid redirect found:
${JSON.stringify(pe,null,2)}
 when navigating to "${R.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return Je({query:R.query,hash:R.hash,params:pe.path!=null?{}:R.params},pe)}}function ve(R,oe){const ne=m=A(R),pe=h.value,Pe=R.state,ot=R.force,ke=R.replace===!0,b=fe(ne);if(b)return ve(Je(se(b),{state:typeof b=="object"?Je({},Pe,b.state):Pe,force:ot,replace:ke}),oe||ne);const C=ne;C.redirectedFrom=oe;let M;return!ot&&wh(i,pe,ne)&&(M=Un(16,{to:C,from:pe}),yt(pe,pe,!0,!1)),(M?Promise.resolve(M):V(C,pe)).catch(U=>pr(U)?pr(U,2)?U:Xt(U):be(U,C,pe)).then(U=>{if(U){if(pr(U,2))return{}.NODE_ENV!=="production"&&wh(i,A(U.to),C)&&oe&&(oe._count=oe._count?oe._count+1:1)>30?($e(`Detected a possibly infinite redirection in a navigation guard when going from "${pe.fullPath}" to "${C.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):ve(Je({replace:ke},se(U.to),{state:typeof U.to=="object"?Je({},Pe,U.to.state):Pe,force:ot}),oe||C)}else U=le(C,pe,!0,ke,Pe);return Te(C,pe,U),U})}function Ae(R,oe){const ne=G(R,oe);return ne?Promise.reject(ne):Promise.resolve()}function ie(R){const oe=fs.values().next().value;return oe&&typeof oe.runWithContext=="function"?oe.runWithContext(R):R()}function V(R,oe){let ne;const[pe,Pe,ot]=K1(R,oe);ne=cu(pe.reverse(),"beforeRouteLeave",R,oe);for(const b of pe)b.leaveGuards.forEach(C=>{ne.push(Ur(C,R,oe))});const ke=Ae.bind(null,R,oe);return ne.push(ke),hs(ne).then(()=>{ne=[];for(const b of a.list())ne.push(Ur(b,R,oe));return ne.push(ke),hs(ne)}).then(()=>{ne=cu(Pe,"beforeRouteUpdate",R,oe);for(const b of Pe)b.updateGuards.forEach(C=>{ne.push(Ur(C,R,oe))});return ne.push(ke),hs(ne)}).then(()=>{ne=[];for(const b of ot)if(b.beforeEnter)if(ds(b.beforeEnter))for(const C of b.beforeEnter)ne.push(Ur(C,R,oe));else ne.push(Ur(b.beforeEnter,R,oe));return ne.push(ke),hs(ne)}).then(()=>(R.matched.forEach(b=>b.enterCallbacks={}),ne=cu(ot,"beforeRouteEnter",R,oe,ie),ne.push(ke),hs(ne))).then(()=>{ne=[];for(const b of u.list())ne.push(Ur(b,R,oe));return ne.push(ke),hs(ne)}).catch(b=>pr(b,8)?b:Promise.reject(b))}function Te(R,oe,ne){c.list().forEach(pe=>ie(()=>pe(R,oe,ne)))}function le(R,oe,ne,pe,Pe){const ot=G(R,oe);if(ot)return ot;const ke=oe===Lr,b=hr?history.state:{};ne&&(pe||ke?o.replace(R.fullPath,Je({scroll:ke&&b&&b.scroll},Pe)):o.push(R.fullPath,Pe)),h.value=R,yt(R,oe,ne,ke),Xt()}let Ge;function gt(){Ge||(Ge=o.listen((R,oe,ne)=>{if(!zt.listening)return;const pe=A(R),Pe=fe(pe);if(Pe){ve(Je(Pe,{replace:!0,force:!0}),pe).catch(Io);return}m=pe;const ot=h.value;hr&&Xb(Dh(ot.fullPath,ne.delta),Gi()),V(pe,ot).catch(ke=>pr(ke,12)?ke:pr(ke,2)?(ve(Je(se(ke.to),{force:!0}),pe).then(b=>{pr(b,20)&&!ne.delta&&ne.type===Mo.pop&&o.go(-1,!1)}).catch(Io),Promise.reject()):(ne.delta&&o.go(-ne.delta,!1),be(ke,pe,ot))).then(ke=>{ke=ke||le(pe,ot,!1),ke&&(ne.delta&&!pr(ke,8)?o.go(-ne.delta,!1):ne.type===Mo.pop&&pr(ke,20)&&o.go(-1,!1)),Te(pe,ot,ke)}).catch(Io)}))}let ht=ko(),ct=ko(),xe;function be(R,oe,ne){Xt(R);const pe=ct.list();return pe.length?pe.forEach(Pe=>Pe(R,oe,ne)):({}.NODE_ENV!=="production"&&$e("uncaught error during route navigation:"),console.error(R)),Promise.reject(R)}function Ut(){return xe&&h.value!==Lr?Promise.resolve():new Promise((R,oe)=>{ht.add([R,oe])})}function Xt(R){return xe||(xe=!R,gt(),ht.list().forEach(([oe,ne])=>R?ne(R):oe()),ht.reset()),R}function yt(R,oe,ne,pe){const{scrollBehavior:Pe}=e;if(!hr||!Pe)return Promise.resolve();const ot=!ne&&e1(Dh(R.fullPath,0))||(pe||!ne)&&history.state&&history.state.scroll||null;return ml().then(()=>Pe(R,oe,ot)).then(ke=>ke&&Jb(ke)).catch(ke=>be(ke,R,oe))}const ce=R=>o.go(R);let ze;const fs=new Set,zt={currentRoute:h,listening:!0,addRoute:D,removeRoute:k,clearRoutes:t.clearRoutes,hasRoute:te,getRoutes:F,resolve:A,options:e,push:ye,replace:Z,go:ce,back:()=>ce(-1),forward:()=>ce(1),beforeEach:a.add,beforeResolve:u.add,afterEach:c.add,onError:ct.add,isReady:Ut,install(R){const oe=this;R.component("RouterLink",A1),R.component("RouterView",k1),R.config.globalProperties.$router=oe,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>Tr(h)}),hr&&!ze&&h.value===Lr&&(ze=!0,ye(o.location).catch(Pe=>{({}).NODE_ENV!=="production"&&$e("Unexpected error when starting the router:",Pe)}));const ne={};for(const Pe in Lr)Object.defineProperty(ne,Pe,{get:()=>h.value[Pe],enumerable:!0});R.provide(Yi,oe),R.provide(lu,nd(ne)),R.provide(uu,h);const pe=R.unmount;fs.add(R),R.unmount=function(){fs.delete(R),fs.size<1&&(m=Lr,Ge&&Ge(),Ge=null,h.value=Lr,ze=!1,xe=!1),pe()},{}.NODE_ENV!=="production"&&hr&&L1(R,oe,t)}};function hs(R){return R.reduce((oe,ne)=>oe.then(()=>ie(ne)),Promise.resolve())}return zt}function K1(e,t){const s=[],i=[],o=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const c=t.matched[u];c&&(e.matched.find(m=>Rr(m,c))?i.push(c):s.push(c));const h=e.matched[u];h&&(t.matched.find(m=>Rr(m,h))||o.push(h))}return[s,i,o]}function Zi(){return Vs(Yi)}function Qh(e){return Vs(lu)}const Y1="data:image/svg+xml;base64,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";var Ro=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Ji={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */Ji.exports,function(e,t){(function(){var s,i="4.17.21",o=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",c="Invalid `variable` option passed into `_.template`",h="__lodash_hash_undefined__",m=500,p="__lodash_placeholder__",_=1,w=2,D=4,k=1,F=2,te=1,A=2,se=4,G=8,ye=16,Z=32,fe=64,ve=128,Ae=256,ie=512,V=30,Te="...",le=800,Ge=16,gt=1,ht=2,ct=3,xe=1/0,be=9007199254740991,Ut=17976931348623157e292,Xt=0/0,yt=**********,ce=yt-1,ze=yt>>>1,fs=[["ary",ve],["bind",te],["bindKey",A],["curry",G],["curryRight",ye],["flip",ie],["partial",Z],["partialRight",fe],["rearg",Ae]],zt="[object Arguments]",hs="[object Array]",R="[object AsyncFunction]",oe="[object Boolean]",ne="[object Date]",pe="[object DOMException]",Pe="[object Error]",ot="[object Function]",ke="[object GeneratorFunction]",b="[object Map]",C="[object Number]",M="[object Null]",U="[object Object]",H="[object Promise]",z="[object Proxy]",X="[object RegExp]",K="[object Set]",J="[object String]",W="[object Symbol]",we="[object Undefined]",re="[object WeakMap]",_e="[object WeakSet]",Ee="[object ArrayBuffer]",Le="[object DataView]",Xe="[object Float32Array]",Qe="[object Float64Array]",Ft="[object Int8Array]",St="[object Int16Array]",es="[object Int32Array]",Bt="[object Uint8Array]",gr="[object Uint8ClampedArray]",Bn="[object Uint16Array]",At="[object Uint32Array]",Es=/\b__p \+= '';/g,oa=/\b(__p \+=) '' \+/g,hN=/(__e\(.*?\)|\b__t\)) \+\n'';/g,cp=/&(?:amp|lt|gt|quot|#39);/g,dp=/[&<>"']/g,pN=RegExp(cp.source),mN=RegExp(dp.source),gN=/<%-([\s\S]+?)%>/g,_N=/<%([\s\S]+?)%>/g,fp=/<%=([\s\S]+?)%>/g,vN=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,yN=/^\w*$/,bN=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,yu=/[\\^$.*+?()[\]{}|]/g,wN=RegExp(yu.source),bu=/^\s+/,EN=/\s/,CN=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,DN=/\{\n\/\* \[wrapped with (.+)\] \*/,xN=/,? & /,SN=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,ON=/[()=,{}\[\]\/\s]/,TN=/\\(\\)?/g,NN=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,hp=/\w*$/,AN=/^[-+]0x[0-9a-f]+$/i,IN=/^0b[01]+$/i,MN=/^\[object .+?Constructor\]$/,PN=/^0o[0-7]+$/i,kN=/^(?:0|[1-9]\d*)$/,VN=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ia=/($^)/,RN=/['\n\r\u2028\u2029\\]/g,aa="\\ud800-\\udfff",LN="\\u0300-\\u036f",UN="\\ufe20-\\ufe2f",FN="\\u20d0-\\u20ff",pp=LN+UN+FN,mp="\\u2700-\\u27bf",gp="a-z\\xdf-\\xf6\\xf8-\\xff",BN="\\xac\\xb1\\xd7\\xf7",$N="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",jN="\\u2000-\\u206f",HN=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",_p="A-Z\\xc0-\\xd6\\xd8-\\xde",vp="\\ufe0e\\ufe0f",yp=BN+$N+jN+HN,wu="['’]",qN="["+aa+"]",bp="["+yp+"]",la="["+pp+"]",wp="\\d+",zN="["+mp+"]",Ep="["+gp+"]",Cp="[^"+aa+yp+wp+mp+gp+_p+"]",Eu="\\ud83c[\\udffb-\\udfff]",WN="(?:"+la+"|"+Eu+")",Dp="[^"+aa+"]",Cu="(?:\\ud83c[\\udde6-\\uddff]){2}",Du="[\\ud800-\\udbff][\\udc00-\\udfff]",$n="["+_p+"]",xp="\\u200d",Sp="(?:"+Ep+"|"+Cp+")",GN="(?:"+$n+"|"+Cp+")",Op="(?:"+wu+"(?:d|ll|m|re|s|t|ve))?",Tp="(?:"+wu+"(?:D|LL|M|RE|S|T|VE))?",Np=WN+"?",Ap="["+vp+"]?",KN="(?:"+xp+"(?:"+[Dp,Cu,Du].join("|")+")"+Ap+Np+")*",YN="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",QN="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Ip=Ap+Np+KN,ZN="(?:"+[zN,Cu,Du].join("|")+")"+Ip,JN="(?:"+[Dp+la+"?",la,Cu,Du,qN].join("|")+")",XN=RegExp(wu,"g"),eA=RegExp(la,"g"),xu=RegExp(Eu+"(?="+Eu+")|"+JN+Ip,"g"),tA=RegExp([$n+"?"+Ep+"+"+Op+"(?="+[bp,$n,"$"].join("|")+")",GN+"+"+Tp+"(?="+[bp,$n+Sp,"$"].join("|")+")",$n+"?"+Sp+"+"+Op,$n+"+"+Tp,QN,YN,wp,ZN].join("|"),"g"),sA=RegExp("["+xp+aa+pp+vp+"]"),rA=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,nA=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],oA=-1,dt={};dt[Xe]=dt[Qe]=dt[Ft]=dt[St]=dt[es]=dt[Bt]=dt[gr]=dt[Bn]=dt[At]=!0,dt[zt]=dt[hs]=dt[Ee]=dt[oe]=dt[Le]=dt[ne]=dt[Pe]=dt[ot]=dt[b]=dt[C]=dt[U]=dt[X]=dt[K]=dt[J]=dt[re]=!1;var lt={};lt[zt]=lt[hs]=lt[Ee]=lt[Le]=lt[oe]=lt[ne]=lt[Xe]=lt[Qe]=lt[Ft]=lt[St]=lt[es]=lt[b]=lt[C]=lt[U]=lt[X]=lt[K]=lt[J]=lt[W]=lt[Bt]=lt[gr]=lt[Bn]=lt[At]=!0,lt[Pe]=lt[ot]=lt[re]=!1;var iA={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},aA={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},lA={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},uA={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},cA=parseFloat,dA=parseInt,Mp=typeof Ro=="object"&&Ro&&Ro.Object===Object&&Ro,fA=typeof self=="object"&&self&&self.Object===Object&&self,$t=Mp||fA||Function("return this")(),Su=t&&!t.nodeType&&t,mn=Su&&!0&&e&&!e.nodeType&&e,Pp=mn&&mn.exports===Su,Ou=Pp&&Mp.process,Cs=function(){try{var S=mn&&mn.require&&mn.require("util").types;return S||Ou&&Ou.binding&&Ou.binding("util")}catch{}}(),kp=Cs&&Cs.isArrayBuffer,Vp=Cs&&Cs.isDate,Rp=Cs&&Cs.isMap,Lp=Cs&&Cs.isRegExp,Up=Cs&&Cs.isSet,Fp=Cs&&Cs.isTypedArray;function ps(S,L,I){switch(I.length){case 0:return S.call(L);case 1:return S.call(L,I[0]);case 2:return S.call(L,I[0],I[1]);case 3:return S.call(L,I[0],I[1],I[2])}return S.apply(L,I)}function hA(S,L,I,de){for(var Me=-1,et=S==null?0:S.length;++Me<et;){var It=S[Me];L(de,It,I(It),S)}return de}function Ds(S,L){for(var I=-1,de=S==null?0:S.length;++I<de&&L(S[I],I,S)!==!1;);return S}function pA(S,L){for(var I=S==null?0:S.length;I--&&L(S[I],I,S)!==!1;);return S}function Bp(S,L){for(var I=-1,de=S==null?0:S.length;++I<de;)if(!L(S[I],I,S))return!1;return!0}function Fr(S,L){for(var I=-1,de=S==null?0:S.length,Me=0,et=[];++I<de;){var It=S[I];L(It,I,S)&&(et[Me++]=It)}return et}function ua(S,L){var I=S==null?0:S.length;return!!I&&jn(S,L,0)>-1}function Tu(S,L,I){for(var de=-1,Me=S==null?0:S.length;++de<Me;)if(I(L,S[de]))return!0;return!1}function pt(S,L){for(var I=-1,de=S==null?0:S.length,Me=Array(de);++I<de;)Me[I]=L(S[I],I,S);return Me}function Br(S,L){for(var I=-1,de=L.length,Me=S.length;++I<de;)S[Me+I]=L[I];return S}function Nu(S,L,I,de){var Me=-1,et=S==null?0:S.length;for(de&&et&&(I=S[++Me]);++Me<et;)I=L(I,S[Me],Me,S);return I}function mA(S,L,I,de){var Me=S==null?0:S.length;for(de&&Me&&(I=S[--Me]);Me--;)I=L(I,S[Me],Me,S);return I}function Au(S,L){for(var I=-1,de=S==null?0:S.length;++I<de;)if(L(S[I],I,S))return!0;return!1}var gA=Iu("length");function _A(S){return S.split("")}function vA(S){return S.match(SN)||[]}function $p(S,L,I){var de;return I(S,function(Me,et,It){if(L(Me,et,It))return de=et,!1}),de}function ca(S,L,I,de){for(var Me=S.length,et=I+(de?1:-1);de?et--:++et<Me;)if(L(S[et],et,S))return et;return-1}function jn(S,L,I){return L===L?AA(S,L,I):ca(S,jp,I)}function yA(S,L,I,de){for(var Me=I-1,et=S.length;++Me<et;)if(de(S[Me],L))return Me;return-1}function jp(S){return S!==S}function Hp(S,L){var I=S==null?0:S.length;return I?Pu(S,L)/I:Xt}function Iu(S){return function(L){return L==null?s:L[S]}}function Mu(S){return function(L){return S==null?s:S[L]}}function qp(S,L,I,de,Me){return Me(S,function(et,It,at){I=de?(de=!1,et):L(I,et,It,at)}),I}function bA(S,L){var I=S.length;for(S.sort(L);I--;)S[I]=S[I].value;return S}function Pu(S,L){for(var I,de=-1,Me=S.length;++de<Me;){var et=L(S[de]);et!==s&&(I=I===s?et:I+et)}return I}function ku(S,L){for(var I=-1,de=Array(S);++I<S;)de[I]=L(I);return de}function wA(S,L){return pt(L,function(I){return[I,S[I]]})}function zp(S){return S&&S.slice(0,Yp(S)+1).replace(bu,"")}function ms(S){return function(L){return S(L)}}function Vu(S,L){return pt(L,function(I){return S[I]})}function Ho(S,L){return S.has(L)}function Wp(S,L){for(var I=-1,de=S.length;++I<de&&jn(L,S[I],0)>-1;);return I}function Gp(S,L){for(var I=S.length;I--&&jn(L,S[I],0)>-1;);return I}function EA(S,L){for(var I=S.length,de=0;I--;)S[I]===L&&++de;return de}var CA=Mu(iA),DA=Mu(aA);function xA(S){return"\\"+uA[S]}function SA(S,L){return S==null?s:S[L]}function Hn(S){return sA.test(S)}function OA(S){return rA.test(S)}function TA(S){for(var L,I=[];!(L=S.next()).done;)I.push(L.value);return I}function Ru(S){var L=-1,I=Array(S.size);return S.forEach(function(de,Me){I[++L]=[Me,de]}),I}function Kp(S,L){return function(I){return S(L(I))}}function $r(S,L){for(var I=-1,de=S.length,Me=0,et=[];++I<de;){var It=S[I];(It===L||It===p)&&(S[I]=p,et[Me++]=I)}return et}function da(S){var L=-1,I=Array(S.size);return S.forEach(function(de){I[++L]=de}),I}function NA(S){var L=-1,I=Array(S.size);return S.forEach(function(de){I[++L]=[de,de]}),I}function AA(S,L,I){for(var de=I-1,Me=S.length;++de<Me;)if(S[de]===L)return de;return-1}function IA(S,L,I){for(var de=I+1;de--;)if(S[de]===L)return de;return de}function qn(S){return Hn(S)?PA(S):gA(S)}function Fs(S){return Hn(S)?kA(S):_A(S)}function Yp(S){for(var L=S.length;L--&&EN.test(S.charAt(L)););return L}var MA=Mu(lA);function PA(S){for(var L=xu.lastIndex=0;xu.test(S);)++L;return L}function kA(S){return S.match(xu)||[]}function VA(S){return S.match(tA)||[]}var RA=function S(L){L=L==null?$t:zn.defaults($t.Object(),L,zn.pick($t,nA));var I=L.Array,de=L.Date,Me=L.Error,et=L.Function,It=L.Math,at=L.Object,Lu=L.RegExp,LA=L.String,xs=L.TypeError,fa=I.prototype,UA=et.prototype,Wn=at.prototype,ha=L["__core-js_shared__"],pa=UA.toString,it=Wn.hasOwnProperty,FA=0,Qp=function(){var r=/[^.]+$/.exec(ha&&ha.keys&&ha.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),ma=Wn.toString,BA=pa.call(at),$A=$t._,jA=Lu("^"+pa.call(it).replace(yu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ga=Pp?L.Buffer:s,jr=L.Symbol,_a=L.Uint8Array,Zp=ga?ga.allocUnsafe:s,va=Kp(at.getPrototypeOf,at),Jp=at.create,Xp=Wn.propertyIsEnumerable,ya=fa.splice,em=jr?jr.isConcatSpreadable:s,qo=jr?jr.iterator:s,gn=jr?jr.toStringTag:s,ba=function(){try{var r=wn(at,"defineProperty");return r({},"",{}),r}catch{}}(),HA=L.clearTimeout!==$t.clearTimeout&&L.clearTimeout,qA=de&&de.now!==$t.Date.now&&de.now,zA=L.setTimeout!==$t.setTimeout&&L.setTimeout,wa=It.ceil,Ea=It.floor,Uu=at.getOwnPropertySymbols,WA=ga?ga.isBuffer:s,tm=L.isFinite,GA=fa.join,KA=Kp(at.keys,at),Mt=It.max,Wt=It.min,YA=de.now,QA=L.parseInt,sm=It.random,ZA=fa.reverse,Fu=wn(L,"DataView"),zo=wn(L,"Map"),Bu=wn(L,"Promise"),Gn=wn(L,"Set"),Wo=wn(L,"WeakMap"),Go=wn(at,"create"),Ca=Wo&&new Wo,Kn={},JA=En(Fu),XA=En(zo),eI=En(Bu),tI=En(Gn),sI=En(Wo),Da=jr?jr.prototype:s,Ko=Da?Da.valueOf:s,rm=Da?Da.toString:s;function v(r){if(bt(r)&&!Ve(r)&&!(r instanceof We)){if(r instanceof Ss)return r;if(it.call(r,"__wrapped__"))return ng(r)}return new Ss(r)}var Yn=function(){function r(){}return function(n){if(!_t(n))return{};if(Jp)return Jp(n);r.prototype=n;var l=new r;return r.prototype=s,l}}();function xa(){}function Ss(r,n){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!n,this.__index__=0,this.__values__=s}v.templateSettings={escape:gN,evaluate:_N,interpolate:fp,variable:"",imports:{_:v}},v.prototype=xa.prototype,v.prototype.constructor=v,Ss.prototype=Yn(xa.prototype),Ss.prototype.constructor=Ss;function We(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=yt,this.__views__=[]}function rI(){var r=new We(this.__wrapped__);return r.__actions__=ns(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=ns(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=ns(this.__views__),r}function nI(){if(this.__filtered__){var r=new We(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function oI(){var r=this.__wrapped__.value(),n=this.__dir__,l=Ve(r),d=n<0,g=l?r.length:0,y=_M(0,g,this.__views__),E=y.start,x=y.end,T=x-E,B=d?x:E-1,j=this.__iteratees__,q=j.length,ue=0,ge=Wt(T,this.__takeCount__);if(!l||!d&&g==T&&ge==T)return Om(r,this.__actions__);var Se=[];e:for(;T--&&ue<ge;){B+=n;for(var Be=-1,Oe=r[B];++Be<q;){var qe=j[Be],Ke=qe.iteratee,vs=qe.type,rs=Ke(Oe);if(vs==ht)Oe=rs;else if(!rs){if(vs==gt)continue e;break e}}Se[ue++]=Oe}return Se}We.prototype=Yn(xa.prototype),We.prototype.constructor=We;function _n(r){var n=-1,l=r==null?0:r.length;for(this.clear();++n<l;){var d=r[n];this.set(d[0],d[1])}}function iI(){this.__data__=Go?Go(null):{},this.size=0}function aI(r){var n=this.has(r)&&delete this.__data__[r];return this.size-=n?1:0,n}function lI(r){var n=this.__data__;if(Go){var l=n[r];return l===h?s:l}return it.call(n,r)?n[r]:s}function uI(r){var n=this.__data__;return Go?n[r]!==s:it.call(n,r)}function cI(r,n){var l=this.__data__;return this.size+=this.has(r)?0:1,l[r]=Go&&n===s?h:n,this}_n.prototype.clear=iI,_n.prototype.delete=aI,_n.prototype.get=lI,_n.prototype.has=uI,_n.prototype.set=cI;function _r(r){var n=-1,l=r==null?0:r.length;for(this.clear();++n<l;){var d=r[n];this.set(d[0],d[1])}}function dI(){this.__data__=[],this.size=0}function fI(r){var n=this.__data__,l=Sa(n,r);if(l<0)return!1;var d=n.length-1;return l==d?n.pop():ya.call(n,l,1),--this.size,!0}function hI(r){var n=this.__data__,l=Sa(n,r);return l<0?s:n[l][1]}function pI(r){return Sa(this.__data__,r)>-1}function mI(r,n){var l=this.__data__,d=Sa(l,r);return d<0?(++this.size,l.push([r,n])):l[d][1]=n,this}_r.prototype.clear=dI,_r.prototype.delete=fI,_r.prototype.get=hI,_r.prototype.has=pI,_r.prototype.set=mI;function vr(r){var n=-1,l=r==null?0:r.length;for(this.clear();++n<l;){var d=r[n];this.set(d[0],d[1])}}function gI(){this.size=0,this.__data__={hash:new _n,map:new(zo||_r),string:new _n}}function _I(r){var n=Ua(this,r).delete(r);return this.size-=n?1:0,n}function vI(r){return Ua(this,r).get(r)}function yI(r){return Ua(this,r).has(r)}function bI(r,n){var l=Ua(this,r),d=l.size;return l.set(r,n),this.size+=l.size==d?0:1,this}vr.prototype.clear=gI,vr.prototype.delete=_I,vr.prototype.get=vI,vr.prototype.has=yI,vr.prototype.set=bI;function vn(r){var n=-1,l=r==null?0:r.length;for(this.__data__=new vr;++n<l;)this.add(r[n])}function wI(r){return this.__data__.set(r,h),this}function EI(r){return this.__data__.has(r)}vn.prototype.add=vn.prototype.push=wI,vn.prototype.has=EI;function Bs(r){var n=this.__data__=new _r(r);this.size=n.size}function CI(){this.__data__=new _r,this.size=0}function DI(r){var n=this.__data__,l=n.delete(r);return this.size=n.size,l}function xI(r){return this.__data__.get(r)}function SI(r){return this.__data__.has(r)}function OI(r,n){var l=this.__data__;if(l instanceof _r){var d=l.__data__;if(!zo||d.length<o-1)return d.push([r,n]),this.size=++l.size,this;l=this.__data__=new vr(d)}return l.set(r,n),this.size=l.size,this}Bs.prototype.clear=CI,Bs.prototype.delete=DI,Bs.prototype.get=xI,Bs.prototype.has=SI,Bs.prototype.set=OI;function nm(r,n){var l=Ve(r),d=!l&&Cn(r),g=!l&&!d&&Gr(r),y=!l&&!d&&!g&&Xn(r),E=l||d||g||y,x=E?ku(r.length,LA):[],T=x.length;for(var B in r)(n||it.call(r,B))&&!(E&&(B=="length"||g&&(B=="offset"||B=="parent")||y&&(B=="buffer"||B=="byteLength"||B=="byteOffset")||Er(B,T)))&&x.push(B);return x}function om(r){var n=r.length;return n?r[Zu(0,n-1)]:s}function TI(r,n){return Fa(ns(r),yn(n,0,r.length))}function NI(r){return Fa(ns(r))}function $u(r,n,l){(l!==s&&!$s(r[n],l)||l===s&&!(n in r))&&yr(r,n,l)}function Yo(r,n,l){var d=r[n];(!(it.call(r,n)&&$s(d,l))||l===s&&!(n in r))&&yr(r,n,l)}function Sa(r,n){for(var l=r.length;l--;)if($s(r[l][0],n))return l;return-1}function AI(r,n,l,d){return Hr(r,function(g,y,E){n(d,g,l(g),E)}),d}function im(r,n){return r&&Js(n,kt(n),r)}function II(r,n){return r&&Js(n,is(n),r)}function yr(r,n,l){n=="__proto__"&&ba?ba(r,n,{configurable:!0,enumerable:!0,value:l,writable:!0}):r[n]=l}function ju(r,n){for(var l=-1,d=n.length,g=I(d),y=r==null;++l<d;)g[l]=y?s:Ec(r,n[l]);return g}function yn(r,n,l){return r===r&&(l!==s&&(r=r<=l?r:l),n!==s&&(r=r>=n?r:n)),r}function Os(r,n,l,d,g,y){var E,x=n&_,T=n&w,B=n&D;if(l&&(E=g?l(r,d,g,y):l(r)),E!==s)return E;if(!_t(r))return r;var j=Ve(r);if(j){if(E=yM(r),!x)return ns(r,E)}else{var q=Gt(r),ue=q==ot||q==ke;if(Gr(r))return Am(r,x);if(q==U||q==zt||ue&&!g){if(E=T||ue?{}:Ym(r),!x)return T?lM(r,II(E,r)):aM(r,im(E,r))}else{if(!lt[q])return g?r:{};E=bM(r,q,x)}}y||(y=new Bs);var ge=y.get(r);if(ge)return ge;y.set(r,E),Dg(r)?r.forEach(function(Oe){E.add(Os(Oe,n,l,Oe,r,y))}):Eg(r)&&r.forEach(function(Oe,qe){E.set(qe,Os(Oe,n,l,qe,r,y))});var Se=B?T?lc:ac:T?is:kt,Be=j?s:Se(r);return Ds(Be||r,function(Oe,qe){Be&&(qe=Oe,Oe=r[qe]),Yo(E,qe,Os(Oe,n,l,qe,r,y))}),E}function MI(r){var n=kt(r);return function(l){return am(l,r,n)}}function am(r,n,l){var d=l.length;if(r==null)return!d;for(r=at(r);d--;){var g=l[d],y=n[g],E=r[g];if(E===s&&!(g in r)||!y(E))return!1}return!0}function lm(r,n,l){if(typeof r!="function")throw new xs(u);return si(function(){r.apply(s,l)},n)}function Qo(r,n,l,d){var g=-1,y=ua,E=!0,x=r.length,T=[],B=n.length;if(!x)return T;l&&(n=pt(n,ms(l))),d?(y=Tu,E=!1):n.length>=o&&(y=Ho,E=!1,n=new vn(n));e:for(;++g<x;){var j=r[g],q=l==null?j:l(j);if(j=d||j!==0?j:0,E&&q===q){for(var ue=B;ue--;)if(n[ue]===q)continue e;T.push(j)}else y(n,q,d)||T.push(j)}return T}var Hr=Vm(Zs),um=Vm(qu,!0);function PI(r,n){var l=!0;return Hr(r,function(d,g,y){return l=!!n(d,g,y),l}),l}function Oa(r,n,l){for(var d=-1,g=r.length;++d<g;){var y=r[d],E=n(y);if(E!=null&&(x===s?E===E&&!_s(E):l(E,x)))var x=E,T=y}return T}function kI(r,n,l,d){var g=r.length;for(l=Ue(l),l<0&&(l=-l>g?0:g+l),d=d===s||d>g?g:Ue(d),d<0&&(d+=g),d=l>d?0:Sg(d);l<d;)r[l++]=n;return r}function cm(r,n){var l=[];return Hr(r,function(d,g,y){n(d,g,y)&&l.push(d)}),l}function jt(r,n,l,d,g){var y=-1,E=r.length;for(l||(l=EM),g||(g=[]);++y<E;){var x=r[y];n>0&&l(x)?n>1?jt(x,n-1,l,d,g):Br(g,x):d||(g[g.length]=x)}return g}var Hu=Rm(),dm=Rm(!0);function Zs(r,n){return r&&Hu(r,n,kt)}function qu(r,n){return r&&dm(r,n,kt)}function Ta(r,n){return Fr(n,function(l){return Cr(r[l])})}function bn(r,n){n=zr(n,r);for(var l=0,d=n.length;r!=null&&l<d;)r=r[Xs(n[l++])];return l&&l==d?r:s}function fm(r,n,l){var d=n(r);return Ve(r)?d:Br(d,l(r))}function ts(r){return r==null?r===s?we:M:gn&&gn in at(r)?gM(r):NM(r)}function zu(r,n){return r>n}function VI(r,n){return r!=null&&it.call(r,n)}function RI(r,n){return r!=null&&n in at(r)}function LI(r,n,l){return r>=Wt(n,l)&&r<Mt(n,l)}function Wu(r,n,l){for(var d=l?Tu:ua,g=r[0].length,y=r.length,E=y,x=I(y),T=1/0,B=[];E--;){var j=r[E];E&&n&&(j=pt(j,ms(n))),T=Wt(j.length,T),x[E]=!l&&(n||g>=120&&j.length>=120)?new vn(E&&j):s}j=r[0];var q=-1,ue=x[0];e:for(;++q<g&&B.length<T;){var ge=j[q],Se=n?n(ge):ge;if(ge=l||ge!==0?ge:0,!(ue?Ho(ue,Se):d(B,Se,l))){for(E=y;--E;){var Be=x[E];if(!(Be?Ho(Be,Se):d(r[E],Se,l)))continue e}ue&&ue.push(Se),B.push(ge)}}return B}function UI(r,n,l,d){return Zs(r,function(g,y,E){n(d,l(g),y,E)}),d}function Zo(r,n,l){n=zr(n,r),r=Xm(r,n);var d=r==null?r:r[Xs(Ns(n))];return d==null?s:ps(d,r,l)}function hm(r){return bt(r)&&ts(r)==zt}function FI(r){return bt(r)&&ts(r)==Ee}function BI(r){return bt(r)&&ts(r)==ne}function Jo(r,n,l,d,g){return r===n?!0:r==null||n==null||!bt(r)&&!bt(n)?r!==r&&n!==n:$I(r,n,l,d,Jo,g)}function $I(r,n,l,d,g,y){var E=Ve(r),x=Ve(n),T=E?hs:Gt(r),B=x?hs:Gt(n);T=T==zt?U:T,B=B==zt?U:B;var j=T==U,q=B==U,ue=T==B;if(ue&&Gr(r)){if(!Gr(n))return!1;E=!0,j=!1}if(ue&&!j)return y||(y=new Bs),E||Xn(r)?Wm(r,n,l,d,g,y):pM(r,n,T,l,d,g,y);if(!(l&k)){var ge=j&&it.call(r,"__wrapped__"),Se=q&&it.call(n,"__wrapped__");if(ge||Se){var Be=ge?r.value():r,Oe=Se?n.value():n;return y||(y=new Bs),g(Be,Oe,l,d,y)}}return ue?(y||(y=new Bs),mM(r,n,l,d,g,y)):!1}function jI(r){return bt(r)&&Gt(r)==b}function Gu(r,n,l,d){var g=l.length,y=g,E=!d;if(r==null)return!y;for(r=at(r);g--;){var x=l[g];if(E&&x[2]?x[1]!==r[x[0]]:!(x[0]in r))return!1}for(;++g<y;){x=l[g];var T=x[0],B=r[T],j=x[1];if(E&&x[2]){if(B===s&&!(T in r))return!1}else{var q=new Bs;if(d)var ue=d(B,j,T,r,n,q);if(!(ue===s?Jo(j,B,k|F,d,q):ue))return!1}}return!0}function pm(r){if(!_t(r)||DM(r))return!1;var n=Cr(r)?jA:MN;return n.test(En(r))}function HI(r){return bt(r)&&ts(r)==X}function qI(r){return bt(r)&&Gt(r)==K}function zI(r){return bt(r)&&za(r.length)&&!!dt[ts(r)]}function mm(r){return typeof r=="function"?r:r==null?as:typeof r=="object"?Ve(r)?vm(r[0],r[1]):_m(r):Lg(r)}function Ku(r){if(!ti(r))return KA(r);var n=[];for(var l in at(r))it.call(r,l)&&l!="constructor"&&n.push(l);return n}function WI(r){if(!_t(r))return TM(r);var n=ti(r),l=[];for(var d in r)d=="constructor"&&(n||!it.call(r,d))||l.push(d);return l}function Yu(r,n){return r<n}function gm(r,n){var l=-1,d=os(r)?I(r.length):[];return Hr(r,function(g,y,E){d[++l]=n(g,y,E)}),d}function _m(r){var n=cc(r);return n.length==1&&n[0][2]?Zm(n[0][0],n[0][1]):function(l){return l===r||Gu(l,r,n)}}function vm(r,n){return fc(r)&&Qm(n)?Zm(Xs(r),n):function(l){var d=Ec(l,r);return d===s&&d===n?Cc(l,r):Jo(n,d,k|F)}}function Na(r,n,l,d,g){r!==n&&Hu(n,function(y,E){if(g||(g=new Bs),_t(y))GI(r,n,E,l,Na,d,g);else{var x=d?d(pc(r,E),y,E+"",r,n,g):s;x===s&&(x=y),$u(r,E,x)}},is)}function GI(r,n,l,d,g,y,E){var x=pc(r,l),T=pc(n,l),B=E.get(T);if(B){$u(r,l,B);return}var j=y?y(x,T,l+"",r,n,E):s,q=j===s;if(q){var ue=Ve(T),ge=!ue&&Gr(T),Se=!ue&&!ge&&Xn(T);j=T,ue||ge||Se?Ve(x)?j=x:Et(x)?j=ns(x):ge?(q=!1,j=Am(T,!0)):Se?(q=!1,j=Im(T,!0)):j=[]:ri(T)||Cn(T)?(j=x,Cn(x)?j=Og(x):(!_t(x)||Cr(x))&&(j=Ym(T))):q=!1}q&&(E.set(T,j),g(j,T,d,y,E),E.delete(T)),$u(r,l,j)}function ym(r,n){var l=r.length;if(l)return n+=n<0?l:0,Er(n,l)?r[n]:s}function bm(r,n,l){n.length?n=pt(n,function(y){return Ve(y)?function(E){return bn(E,y.length===1?y[0]:y)}:y}):n=[as];var d=-1;n=pt(n,ms(Ce()));var g=gm(r,function(y,E,x){var T=pt(n,function(B){return B(y)});return{criteria:T,index:++d,value:y}});return bA(g,function(y,E){return iM(y,E,l)})}function KI(r,n){return wm(r,n,function(l,d){return Cc(r,d)})}function wm(r,n,l){for(var d=-1,g=n.length,y={};++d<g;){var E=n[d],x=bn(r,E);l(x,E)&&Xo(y,zr(E,r),x)}return y}function YI(r){return function(n){return bn(n,r)}}function Qu(r,n,l,d){var g=d?yA:jn,y=-1,E=n.length,x=r;for(r===n&&(n=ns(n)),l&&(x=pt(r,ms(l)));++y<E;)for(var T=0,B=n[y],j=l?l(B):B;(T=g(x,j,T,d))>-1;)x!==r&&ya.call(x,T,1),ya.call(r,T,1);return r}function Em(r,n){for(var l=r?n.length:0,d=l-1;l--;){var g=n[l];if(l==d||g!==y){var y=g;Er(g)?ya.call(r,g,1):ec(r,g)}}return r}function Zu(r,n){return r+Ea(sm()*(n-r+1))}function QI(r,n,l,d){for(var g=-1,y=Mt(wa((n-r)/(l||1)),0),E=I(y);y--;)E[d?y:++g]=r,r+=l;return E}function Ju(r,n){var l="";if(!r||n<1||n>be)return l;do n%2&&(l+=r),n=Ea(n/2),n&&(r+=r);while(n);return l}function je(r,n){return mc(Jm(r,n,as),r+"")}function ZI(r){return om(eo(r))}function JI(r,n){var l=eo(r);return Fa(l,yn(n,0,l.length))}function Xo(r,n,l,d){if(!_t(r))return r;n=zr(n,r);for(var g=-1,y=n.length,E=y-1,x=r;x!=null&&++g<y;){var T=Xs(n[g]),B=l;if(T==="__proto__"||T==="constructor"||T==="prototype")return r;if(g!=E){var j=x[T];B=d?d(j,T,x):s,B===s&&(B=_t(j)?j:Er(n[g+1])?[]:{})}Yo(x,T,B),x=x[T]}return r}var Cm=Ca?function(r,n){return Ca.set(r,n),r}:as,XI=ba?function(r,n){return ba(r,"toString",{configurable:!0,enumerable:!1,value:xc(n),writable:!0})}:as;function eM(r){return Fa(eo(r))}function Ts(r,n,l){var d=-1,g=r.length;n<0&&(n=-n>g?0:g+n),l=l>g?g:l,l<0&&(l+=g),g=n>l?0:l-n>>>0,n>>>=0;for(var y=I(g);++d<g;)y[d]=r[d+n];return y}function tM(r,n){var l;return Hr(r,function(d,g,y){return l=n(d,g,y),!l}),!!l}function Aa(r,n,l){var d=0,g=r==null?d:r.length;if(typeof n=="number"&&n===n&&g<=ze){for(;d<g;){var y=d+g>>>1,E=r[y];E!==null&&!_s(E)&&(l?E<=n:E<n)?d=y+1:g=y}return g}return Xu(r,n,as,l)}function Xu(r,n,l,d){var g=0,y=r==null?0:r.length;if(y===0)return 0;n=l(n);for(var E=n!==n,x=n===null,T=_s(n),B=n===s;g<y;){var j=Ea((g+y)/2),q=l(r[j]),ue=q!==s,ge=q===null,Se=q===q,Be=_s(q);if(E)var Oe=d||Se;else B?Oe=Se&&(d||ue):x?Oe=Se&&ue&&(d||!ge):T?Oe=Se&&ue&&!ge&&(d||!Be):ge||Be?Oe=!1:Oe=d?q<=n:q<n;Oe?g=j+1:y=j}return Wt(y,ce)}function Dm(r,n){for(var l=-1,d=r.length,g=0,y=[];++l<d;){var E=r[l],x=n?n(E):E;if(!l||!$s(x,T)){var T=x;y[g++]=E===0?0:E}}return y}function xm(r){return typeof r=="number"?r:_s(r)?Xt:+r}function gs(r){if(typeof r=="string")return r;if(Ve(r))return pt(r,gs)+"";if(_s(r))return rm?rm.call(r):"";var n=r+"";return n=="0"&&1/r==-xe?"-0":n}function qr(r,n,l){var d=-1,g=ua,y=r.length,E=!0,x=[],T=x;if(l)E=!1,g=Tu;else if(y>=o){var B=n?null:fM(r);if(B)return da(B);E=!1,g=Ho,T=new vn}else T=n?[]:x;e:for(;++d<y;){var j=r[d],q=n?n(j):j;if(j=l||j!==0?j:0,E&&q===q){for(var ue=T.length;ue--;)if(T[ue]===q)continue e;n&&T.push(q),x.push(j)}else g(T,q,l)||(T!==x&&T.push(q),x.push(j))}return x}function ec(r,n){return n=zr(n,r),r=Xm(r,n),r==null||delete r[Xs(Ns(n))]}function Sm(r,n,l,d){return Xo(r,n,l(bn(r,n)),d)}function Ia(r,n,l,d){for(var g=r.length,y=d?g:-1;(d?y--:++y<g)&&n(r[y],y,r););return l?Ts(r,d?0:y,d?y+1:g):Ts(r,d?y+1:0,d?g:y)}function Om(r,n){var l=r;return l instanceof We&&(l=l.value()),Nu(n,function(d,g){return g.func.apply(g.thisArg,Br([d],g.args))},l)}function tc(r,n,l){var d=r.length;if(d<2)return d?qr(r[0]):[];for(var g=-1,y=I(d);++g<d;)for(var E=r[g],x=-1;++x<d;)x!=g&&(y[g]=Qo(y[g]||E,r[x],n,l));return qr(jt(y,1),n,l)}function Tm(r,n,l){for(var d=-1,g=r.length,y=n.length,E={};++d<g;){var x=d<y?n[d]:s;l(E,r[d],x)}return E}function sc(r){return Et(r)?r:[]}function rc(r){return typeof r=="function"?r:as}function zr(r,n){return Ve(r)?r:fc(r,n)?[r]:rg(rt(r))}var sM=je;function Wr(r,n,l){var d=r.length;return l=l===s?d:l,!n&&l>=d?r:Ts(r,n,l)}var Nm=HA||function(r){return $t.clearTimeout(r)};function Am(r,n){if(n)return r.slice();var l=r.length,d=Zp?Zp(l):new r.constructor(l);return r.copy(d),d}function nc(r){var n=new r.constructor(r.byteLength);return new _a(n).set(new _a(r)),n}function rM(r,n){var l=n?nc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.byteLength)}function nM(r){var n=new r.constructor(r.source,hp.exec(r));return n.lastIndex=r.lastIndex,n}function oM(r){return Ko?at(Ko.call(r)):{}}function Im(r,n){var l=n?nc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.length)}function Mm(r,n){if(r!==n){var l=r!==s,d=r===null,g=r===r,y=_s(r),E=n!==s,x=n===null,T=n===n,B=_s(n);if(!x&&!B&&!y&&r>n||y&&E&&T&&!x&&!B||d&&E&&T||!l&&T||!g)return 1;if(!d&&!y&&!B&&r<n||B&&l&&g&&!d&&!y||x&&l&&g||!E&&g||!T)return-1}return 0}function iM(r,n,l){for(var d=-1,g=r.criteria,y=n.criteria,E=g.length,x=l.length;++d<E;){var T=Mm(g[d],y[d]);if(T){if(d>=x)return T;var B=l[d];return T*(B=="desc"?-1:1)}}return r.index-n.index}function Pm(r,n,l,d){for(var g=-1,y=r.length,E=l.length,x=-1,T=n.length,B=Mt(y-E,0),j=I(T+B),q=!d;++x<T;)j[x]=n[x];for(;++g<E;)(q||g<y)&&(j[l[g]]=r[g]);for(;B--;)j[x++]=r[g++];return j}function km(r,n,l,d){for(var g=-1,y=r.length,E=-1,x=l.length,T=-1,B=n.length,j=Mt(y-x,0),q=I(j+B),ue=!d;++g<j;)q[g]=r[g];for(var ge=g;++T<B;)q[ge+T]=n[T];for(;++E<x;)(ue||g<y)&&(q[ge+l[E]]=r[g++]);return q}function ns(r,n){var l=-1,d=r.length;for(n||(n=I(d));++l<d;)n[l]=r[l];return n}function Js(r,n,l,d){var g=!l;l||(l={});for(var y=-1,E=n.length;++y<E;){var x=n[y],T=d?d(l[x],r[x],x,l,r):s;T===s&&(T=r[x]),g?yr(l,x,T):Yo(l,x,T)}return l}function aM(r,n){return Js(r,dc(r),n)}function lM(r,n){return Js(r,Gm(r),n)}function Ma(r,n){return function(l,d){var g=Ve(l)?hA:AI,y=n?n():{};return g(l,r,Ce(d,2),y)}}function Qn(r){return je(function(n,l){var d=-1,g=l.length,y=g>1?l[g-1]:s,E=g>2?l[2]:s;for(y=r.length>3&&typeof y=="function"?(g--,y):s,E&&ss(l[0],l[1],E)&&(y=g<3?s:y,g=1),n=at(n);++d<g;){var x=l[d];x&&r(n,x,d,y)}return n})}function Vm(r,n){return function(l,d){if(l==null)return l;if(!os(l))return r(l,d);for(var g=l.length,y=n?g:-1,E=at(l);(n?y--:++y<g)&&d(E[y],y,E)!==!1;);return l}}function Rm(r){return function(n,l,d){for(var g=-1,y=at(n),E=d(n),x=E.length;x--;){var T=E[r?x:++g];if(l(y[T],T,y)===!1)break}return n}}function uM(r,n,l){var d=n&te,g=ei(r);function y(){var E=this&&this!==$t&&this instanceof y?g:r;return E.apply(d?l:this,arguments)}return y}function Lm(r){return function(n){n=rt(n);var l=Hn(n)?Fs(n):s,d=l?l[0]:n.charAt(0),g=l?Wr(l,1).join(""):n.slice(1);return d[r]()+g}}function Zn(r){return function(n){return Nu(Vg(kg(n).replace(XN,"")),r,"")}}function ei(r){return function(){var n=arguments;switch(n.length){case 0:return new r;case 1:return new r(n[0]);case 2:return new r(n[0],n[1]);case 3:return new r(n[0],n[1],n[2]);case 4:return new r(n[0],n[1],n[2],n[3]);case 5:return new r(n[0],n[1],n[2],n[3],n[4]);case 6:return new r(n[0],n[1],n[2],n[3],n[4],n[5]);case 7:return new r(n[0],n[1],n[2],n[3],n[4],n[5],n[6])}var l=Yn(r.prototype),d=r.apply(l,n);return _t(d)?d:l}}function cM(r,n,l){var d=ei(r);function g(){for(var y=arguments.length,E=I(y),x=y,T=Jn(g);x--;)E[x]=arguments[x];var B=y<3&&E[0]!==T&&E[y-1]!==T?[]:$r(E,T);if(y-=B.length,y<l)return jm(r,n,Pa,g.placeholder,s,E,B,s,s,l-y);var j=this&&this!==$t&&this instanceof g?d:r;return ps(j,this,E)}return g}function Um(r){return function(n,l,d){var g=at(n);if(!os(n)){var y=Ce(l,3);n=kt(n),l=function(x){return y(g[x],x,g)}}var E=r(n,l,d);return E>-1?g[y?n[E]:E]:s}}function Fm(r){return wr(function(n){var l=n.length,d=l,g=Ss.prototype.thru;for(r&&n.reverse();d--;){var y=n[d];if(typeof y!="function")throw new xs(u);if(g&&!E&&La(y)=="wrapper")var E=new Ss([],!0)}for(d=E?d:l;++d<l;){y=n[d];var x=La(y),T=x=="wrapper"?uc(y):s;T&&hc(T[0])&&T[1]==(ve|G|Z|Ae)&&!T[4].length&&T[9]==1?E=E[La(T[0])].apply(E,T[3]):E=y.length==1&&hc(y)?E[x]():E.thru(y)}return function(){var B=arguments,j=B[0];if(E&&B.length==1&&Ve(j))return E.plant(j).value();for(var q=0,ue=l?n[q].apply(this,B):j;++q<l;)ue=n[q].call(this,ue);return ue}})}function Pa(r,n,l,d,g,y,E,x,T,B){var j=n&ve,q=n&te,ue=n&A,ge=n&(G|ye),Se=n&ie,Be=ue?s:ei(r);function Oe(){for(var qe=arguments.length,Ke=I(qe),vs=qe;vs--;)Ke[vs]=arguments[vs];if(ge)var rs=Jn(Oe),ys=EA(Ke,rs);if(d&&(Ke=Pm(Ke,d,g,ge)),y&&(Ke=km(Ke,y,E,ge)),qe-=ys,ge&&qe<B){var Ct=$r(Ke,rs);return jm(r,n,Pa,Oe.placeholder,l,Ke,Ct,x,T,B-qe)}var js=q?l:this,xr=ue?js[r]:r;return qe=Ke.length,x?Ke=AM(Ke,x):Se&&qe>1&&Ke.reverse(),j&&T<qe&&(Ke.length=T),this&&this!==$t&&this instanceof Oe&&(xr=Be||ei(xr)),xr.apply(js,Ke)}return Oe}function Bm(r,n){return function(l,d){return UI(l,r,n(d),{})}}function ka(r,n){return function(l,d){var g;if(l===s&&d===s)return n;if(l!==s&&(g=l),d!==s){if(g===s)return d;typeof l=="string"||typeof d=="string"?(l=gs(l),d=gs(d)):(l=xm(l),d=xm(d)),g=r(l,d)}return g}}function oc(r){return wr(function(n){return n=pt(n,ms(Ce())),je(function(l){var d=this;return r(n,function(g){return ps(g,d,l)})})})}function Va(r,n){n=n===s?" ":gs(n);var l=n.length;if(l<2)return l?Ju(n,r):n;var d=Ju(n,wa(r/qn(n)));return Hn(n)?Wr(Fs(d),0,r).join(""):d.slice(0,r)}function dM(r,n,l,d){var g=n&te,y=ei(r);function E(){for(var x=-1,T=arguments.length,B=-1,j=d.length,q=I(j+T),ue=this&&this!==$t&&this instanceof E?y:r;++B<j;)q[B]=d[B];for(;T--;)q[B++]=arguments[++x];return ps(ue,g?l:this,q)}return E}function $m(r){return function(n,l,d){return d&&typeof d!="number"&&ss(n,l,d)&&(l=d=s),n=Dr(n),l===s?(l=n,n=0):l=Dr(l),d=d===s?n<l?1:-1:Dr(d),QI(n,l,d,r)}}function Ra(r){return function(n,l){return typeof n=="string"&&typeof l=="string"||(n=As(n),l=As(l)),r(n,l)}}function jm(r,n,l,d,g,y,E,x,T,B){var j=n&G,q=j?E:s,ue=j?s:E,ge=j?y:s,Se=j?s:y;n|=j?Z:fe,n&=~(j?fe:Z),n&se||(n&=~(te|A));var Be=[r,n,g,ge,q,Se,ue,x,T,B],Oe=l.apply(s,Be);return hc(r)&&eg(Oe,Be),Oe.placeholder=d,tg(Oe,r,n)}function ic(r){var n=It[r];return function(l,d){if(l=As(l),d=d==null?0:Wt(Ue(d),292),d&&tm(l)){var g=(rt(l)+"e").split("e"),y=n(g[0]+"e"+(+g[1]+d));return g=(rt(y)+"e").split("e"),+(g[0]+"e"+(+g[1]-d))}return n(l)}}var fM=Gn&&1/da(new Gn([,-0]))[1]==xe?function(r){return new Gn(r)}:Tc;function Hm(r){return function(n){var l=Gt(n);return l==b?Ru(n):l==K?NA(n):wA(n,r(n))}}function br(r,n,l,d,g,y,E,x){var T=n&A;if(!T&&typeof r!="function")throw new xs(u);var B=d?d.length:0;if(B||(n&=~(Z|fe),d=g=s),E=E===s?E:Mt(Ue(E),0),x=x===s?x:Ue(x),B-=g?g.length:0,n&fe){var j=d,q=g;d=g=s}var ue=T?s:uc(r),ge=[r,n,l,d,g,j,q,y,E,x];if(ue&&OM(ge,ue),r=ge[0],n=ge[1],l=ge[2],d=ge[3],g=ge[4],x=ge[9]=ge[9]===s?T?0:r.length:Mt(ge[9]-B,0),!x&&n&(G|ye)&&(n&=~(G|ye)),!n||n==te)var Se=uM(r,n,l);else n==G||n==ye?Se=cM(r,n,x):(n==Z||n==(te|Z))&&!g.length?Se=dM(r,n,l,d):Se=Pa.apply(s,ge);var Be=ue?Cm:eg;return tg(Be(Se,ge),r,n)}function qm(r,n,l,d){return r===s||$s(r,Wn[l])&&!it.call(d,l)?n:r}function zm(r,n,l,d,g,y){return _t(r)&&_t(n)&&(y.set(n,r),Na(r,n,s,zm,y),y.delete(n)),r}function hM(r){return ri(r)?s:r}function Wm(r,n,l,d,g,y){var E=l&k,x=r.length,T=n.length;if(x!=T&&!(E&&T>x))return!1;var B=y.get(r),j=y.get(n);if(B&&j)return B==n&&j==r;var q=-1,ue=!0,ge=l&F?new vn:s;for(y.set(r,n),y.set(n,r);++q<x;){var Se=r[q],Be=n[q];if(d)var Oe=E?d(Be,Se,q,n,r,y):d(Se,Be,q,r,n,y);if(Oe!==s){if(Oe)continue;ue=!1;break}if(ge){if(!Au(n,function(qe,Ke){if(!Ho(ge,Ke)&&(Se===qe||g(Se,qe,l,d,y)))return ge.push(Ke)})){ue=!1;break}}else if(!(Se===Be||g(Se,Be,l,d,y))){ue=!1;break}}return y.delete(r),y.delete(n),ue}function pM(r,n,l,d,g,y,E){switch(l){case Le:if(r.byteLength!=n.byteLength||r.byteOffset!=n.byteOffset)return!1;r=r.buffer,n=n.buffer;case Ee:return!(r.byteLength!=n.byteLength||!y(new _a(r),new _a(n)));case oe:case ne:case C:return $s(+r,+n);case Pe:return r.name==n.name&&r.message==n.message;case X:case J:return r==n+"";case b:var x=Ru;case K:var T=d&k;if(x||(x=da),r.size!=n.size&&!T)return!1;var B=E.get(r);if(B)return B==n;d|=F,E.set(r,n);var j=Wm(x(r),x(n),d,g,y,E);return E.delete(r),j;case W:if(Ko)return Ko.call(r)==Ko.call(n)}return!1}function mM(r,n,l,d,g,y){var E=l&k,x=ac(r),T=x.length,B=ac(n),j=B.length;if(T!=j&&!E)return!1;for(var q=T;q--;){var ue=x[q];if(!(E?ue in n:it.call(n,ue)))return!1}var ge=y.get(r),Se=y.get(n);if(ge&&Se)return ge==n&&Se==r;var Be=!0;y.set(r,n),y.set(n,r);for(var Oe=E;++q<T;){ue=x[q];var qe=r[ue],Ke=n[ue];if(d)var vs=E?d(Ke,qe,ue,n,r,y):d(qe,Ke,ue,r,n,y);if(!(vs===s?qe===Ke||g(qe,Ke,l,d,y):vs)){Be=!1;break}Oe||(Oe=ue=="constructor")}if(Be&&!Oe){var rs=r.constructor,ys=n.constructor;rs!=ys&&"constructor"in r&&"constructor"in n&&!(typeof rs=="function"&&rs instanceof rs&&typeof ys=="function"&&ys instanceof ys)&&(Be=!1)}return y.delete(r),y.delete(n),Be}function wr(r){return mc(Jm(r,s,ag),r+"")}function ac(r){return fm(r,kt,dc)}function lc(r){return fm(r,is,Gm)}var uc=Ca?function(r){return Ca.get(r)}:Tc;function La(r){for(var n=r.name+"",l=Kn[n],d=it.call(Kn,n)?l.length:0;d--;){var g=l[d],y=g.func;if(y==null||y==r)return g.name}return n}function Jn(r){var n=it.call(v,"placeholder")?v:r;return n.placeholder}function Ce(){var r=v.iteratee||Sc;return r=r===Sc?mm:r,arguments.length?r(arguments[0],arguments[1]):r}function Ua(r,n){var l=r.__data__;return CM(n)?l[typeof n=="string"?"string":"hash"]:l.map}function cc(r){for(var n=kt(r),l=n.length;l--;){var d=n[l],g=r[d];n[l]=[d,g,Qm(g)]}return n}function wn(r,n){var l=SA(r,n);return pm(l)?l:s}function gM(r){var n=it.call(r,gn),l=r[gn];try{r[gn]=s;var d=!0}catch{}var g=ma.call(r);return d&&(n?r[gn]=l:delete r[gn]),g}var dc=Uu?function(r){return r==null?[]:(r=at(r),Fr(Uu(r),function(n){return Xp.call(r,n)}))}:Nc,Gm=Uu?function(r){for(var n=[];r;)Br(n,dc(r)),r=va(r);return n}:Nc,Gt=ts;(Fu&&Gt(new Fu(new ArrayBuffer(1)))!=Le||zo&&Gt(new zo)!=b||Bu&&Gt(Bu.resolve())!=H||Gn&&Gt(new Gn)!=K||Wo&&Gt(new Wo)!=re)&&(Gt=function(r){var n=ts(r),l=n==U?r.constructor:s,d=l?En(l):"";if(d)switch(d){case JA:return Le;case XA:return b;case eI:return H;case tI:return K;case sI:return re}return n});function _M(r,n,l){for(var d=-1,g=l.length;++d<g;){var y=l[d],E=y.size;switch(y.type){case"drop":r+=E;break;case"dropRight":n-=E;break;case"take":n=Wt(n,r+E);break;case"takeRight":r=Mt(r,n-E);break}}return{start:r,end:n}}function vM(r){var n=r.match(DN);return n?n[1].split(xN):[]}function Km(r,n,l){n=zr(n,r);for(var d=-1,g=n.length,y=!1;++d<g;){var E=Xs(n[d]);if(!(y=r!=null&&l(r,E)))break;r=r[E]}return y||++d!=g?y:(g=r==null?0:r.length,!!g&&za(g)&&Er(E,g)&&(Ve(r)||Cn(r)))}function yM(r){var n=r.length,l=new r.constructor(n);return n&&typeof r[0]=="string"&&it.call(r,"index")&&(l.index=r.index,l.input=r.input),l}function Ym(r){return typeof r.constructor=="function"&&!ti(r)?Yn(va(r)):{}}function bM(r,n,l){var d=r.constructor;switch(n){case Ee:return nc(r);case oe:case ne:return new d(+r);case Le:return rM(r,l);case Xe:case Qe:case Ft:case St:case es:case Bt:case gr:case Bn:case At:return Im(r,l);case b:return new d;case C:case J:return new d(r);case X:return nM(r);case K:return new d;case W:return oM(r)}}function wM(r,n){var l=n.length;if(!l)return r;var d=l-1;return n[d]=(l>1?"& ":"")+n[d],n=n.join(l>2?", ":" "),r.replace(CN,`{
/* [wrapped with `+n+`] */
`)}function EM(r){return Ve(r)||Cn(r)||!!(em&&r&&r[em])}function Er(r,n){var l=typeof r;return n=n??be,!!n&&(l=="number"||l!="symbol"&&kN.test(r))&&r>-1&&r%1==0&&r<n}function ss(r,n,l){if(!_t(l))return!1;var d=typeof n;return(d=="number"?os(l)&&Er(n,l.length):d=="string"&&n in l)?$s(l[n],r):!1}function fc(r,n){if(Ve(r))return!1;var l=typeof r;return l=="number"||l=="symbol"||l=="boolean"||r==null||_s(r)?!0:yN.test(r)||!vN.test(r)||n!=null&&r in at(n)}function CM(r){var n=typeof r;return n=="string"||n=="number"||n=="symbol"||n=="boolean"?r!=="__proto__":r===null}function hc(r){var n=La(r),l=v[n];if(typeof l!="function"||!(n in We.prototype))return!1;if(r===l)return!0;var d=uc(l);return!!d&&r===d[0]}function DM(r){return!!Qp&&Qp in r}var xM=ha?Cr:Ac;function ti(r){var n=r&&r.constructor,l=typeof n=="function"&&n.prototype||Wn;return r===l}function Qm(r){return r===r&&!_t(r)}function Zm(r,n){return function(l){return l==null?!1:l[r]===n&&(n!==s||r in at(l))}}function SM(r){var n=Ha(r,function(d){return l.size===m&&l.clear(),d}),l=n.cache;return n}function OM(r,n){var l=r[1],d=n[1],g=l|d,y=g<(te|A|ve),E=d==ve&&l==G||d==ve&&l==Ae&&r[7].length<=n[8]||d==(ve|Ae)&&n[7].length<=n[8]&&l==G;if(!(y||E))return r;d&te&&(r[2]=n[2],g|=l&te?0:se);var x=n[3];if(x){var T=r[3];r[3]=T?Pm(T,x,n[4]):x,r[4]=T?$r(r[3],p):n[4]}return x=n[5],x&&(T=r[5],r[5]=T?km(T,x,n[6]):x,r[6]=T?$r(r[5],p):n[6]),x=n[7],x&&(r[7]=x),d&ve&&(r[8]=r[8]==null?n[8]:Wt(r[8],n[8])),r[9]==null&&(r[9]=n[9]),r[0]=n[0],r[1]=g,r}function TM(r){var n=[];if(r!=null)for(var l in at(r))n.push(l);return n}function NM(r){return ma.call(r)}function Jm(r,n,l){return n=Mt(n===s?r.length-1:n,0),function(){for(var d=arguments,g=-1,y=Mt(d.length-n,0),E=I(y);++g<y;)E[g]=d[n+g];g=-1;for(var x=I(n+1);++g<n;)x[g]=d[g];return x[n]=l(E),ps(r,this,x)}}function Xm(r,n){return n.length<2?r:bn(r,Ts(n,0,-1))}function AM(r,n){for(var l=r.length,d=Wt(n.length,l),g=ns(r);d--;){var y=n[d];r[d]=Er(y,l)?g[y]:s}return r}function pc(r,n){if(!(n==="constructor"&&typeof r[n]=="function")&&n!="__proto__")return r[n]}var eg=sg(Cm),si=zA||function(r,n){return $t.setTimeout(r,n)},mc=sg(XI);function tg(r,n,l){var d=n+"";return mc(r,wM(d,IM(vM(d),l)))}function sg(r){var n=0,l=0;return function(){var d=YA(),g=Ge-(d-l);if(l=d,g>0){if(++n>=le)return arguments[0]}else n=0;return r.apply(s,arguments)}}function Fa(r,n){var l=-1,d=r.length,g=d-1;for(n=n===s?d:n;++l<n;){var y=Zu(l,g),E=r[y];r[y]=r[l],r[l]=E}return r.length=n,r}var rg=SM(function(r){var n=[];return r.charCodeAt(0)===46&&n.push(""),r.replace(bN,function(l,d,g,y){n.push(g?y.replace(TN,"$1"):d||l)}),n});function Xs(r){if(typeof r=="string"||_s(r))return r;var n=r+"";return n=="0"&&1/r==-xe?"-0":n}function En(r){if(r!=null){try{return pa.call(r)}catch{}try{return r+""}catch{}}return""}function IM(r,n){return Ds(fs,function(l){var d="_."+l[0];n&l[1]&&!ua(r,d)&&r.push(d)}),r.sort()}function ng(r){if(r instanceof We)return r.clone();var n=new Ss(r.__wrapped__,r.__chain__);return n.__actions__=ns(r.__actions__),n.__index__=r.__index__,n.__values__=r.__values__,n}function MM(r,n,l){(l?ss(r,n,l):n===s)?n=1:n=Mt(Ue(n),0);var d=r==null?0:r.length;if(!d||n<1)return[];for(var g=0,y=0,E=I(wa(d/n));g<d;)E[y++]=Ts(r,g,g+=n);return E}function PM(r){for(var n=-1,l=r==null?0:r.length,d=0,g=[];++n<l;){var y=r[n];y&&(g[d++]=y)}return g}function kM(){var r=arguments.length;if(!r)return[];for(var n=I(r-1),l=arguments[0],d=r;d--;)n[d-1]=arguments[d];return Br(Ve(l)?ns(l):[l],jt(n,1))}var VM=je(function(r,n){return Et(r)?Qo(r,jt(n,1,Et,!0)):[]}),RM=je(function(r,n){var l=Ns(n);return Et(l)&&(l=s),Et(r)?Qo(r,jt(n,1,Et,!0),Ce(l,2)):[]}),LM=je(function(r,n){var l=Ns(n);return Et(l)&&(l=s),Et(r)?Qo(r,jt(n,1,Et,!0),s,l):[]});function UM(r,n,l){var d=r==null?0:r.length;return d?(n=l||n===s?1:Ue(n),Ts(r,n<0?0:n,d)):[]}function FM(r,n,l){var d=r==null?0:r.length;return d?(n=l||n===s?1:Ue(n),n=d-n,Ts(r,0,n<0?0:n)):[]}function BM(r,n){return r&&r.length?Ia(r,Ce(n,3),!0,!0):[]}function $M(r,n){return r&&r.length?Ia(r,Ce(n,3),!0):[]}function jM(r,n,l,d){var g=r==null?0:r.length;return g?(l&&typeof l!="number"&&ss(r,n,l)&&(l=0,d=g),kI(r,n,l,d)):[]}function og(r,n,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Ue(l);return g<0&&(g=Mt(d+g,0)),ca(r,Ce(n,3),g)}function ig(r,n,l){var d=r==null?0:r.length;if(!d)return-1;var g=d-1;return l!==s&&(g=Ue(l),g=l<0?Mt(d+g,0):Wt(g,d-1)),ca(r,Ce(n,3),g,!0)}function ag(r){var n=r==null?0:r.length;return n?jt(r,1):[]}function HM(r){var n=r==null?0:r.length;return n?jt(r,xe):[]}function qM(r,n){var l=r==null?0:r.length;return l?(n=n===s?1:Ue(n),jt(r,n)):[]}function zM(r){for(var n=-1,l=r==null?0:r.length,d={};++n<l;){var g=r[n];d[g[0]]=g[1]}return d}function lg(r){return r&&r.length?r[0]:s}function WM(r,n,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Ue(l);return g<0&&(g=Mt(d+g,0)),jn(r,n,g)}function GM(r){var n=r==null?0:r.length;return n?Ts(r,0,-1):[]}var KM=je(function(r){var n=pt(r,sc);return n.length&&n[0]===r[0]?Wu(n):[]}),YM=je(function(r){var n=Ns(r),l=pt(r,sc);return n===Ns(l)?n=s:l.pop(),l.length&&l[0]===r[0]?Wu(l,Ce(n,2)):[]}),QM=je(function(r){var n=Ns(r),l=pt(r,sc);return n=typeof n=="function"?n:s,n&&l.pop(),l.length&&l[0]===r[0]?Wu(l,s,n):[]});function ZM(r,n){return r==null?"":GA.call(r,n)}function Ns(r){var n=r==null?0:r.length;return n?r[n-1]:s}function JM(r,n,l){var d=r==null?0:r.length;if(!d)return-1;var g=d;return l!==s&&(g=Ue(l),g=g<0?Mt(d+g,0):Wt(g,d-1)),n===n?IA(r,n,g):ca(r,jp,g,!0)}function XM(r,n){return r&&r.length?ym(r,Ue(n)):s}var e2=je(ug);function ug(r,n){return r&&r.length&&n&&n.length?Qu(r,n):r}function t2(r,n,l){return r&&r.length&&n&&n.length?Qu(r,n,Ce(l,2)):r}function s2(r,n,l){return r&&r.length&&n&&n.length?Qu(r,n,s,l):r}var r2=wr(function(r,n){var l=r==null?0:r.length,d=ju(r,n);return Em(r,pt(n,function(g){return Er(g,l)?+g:g}).sort(Mm)),d});function n2(r,n){var l=[];if(!(r&&r.length))return l;var d=-1,g=[],y=r.length;for(n=Ce(n,3);++d<y;){var E=r[d];n(E,d,r)&&(l.push(E),g.push(d))}return Em(r,g),l}function gc(r){return r==null?r:ZA.call(r)}function o2(r,n,l){var d=r==null?0:r.length;return d?(l&&typeof l!="number"&&ss(r,n,l)?(n=0,l=d):(n=n==null?0:Ue(n),l=l===s?d:Ue(l)),Ts(r,n,l)):[]}function i2(r,n){return Aa(r,n)}function a2(r,n,l){return Xu(r,n,Ce(l,2))}function l2(r,n){var l=r==null?0:r.length;if(l){var d=Aa(r,n);if(d<l&&$s(r[d],n))return d}return-1}function u2(r,n){return Aa(r,n,!0)}function c2(r,n,l){return Xu(r,n,Ce(l,2),!0)}function d2(r,n){var l=r==null?0:r.length;if(l){var d=Aa(r,n,!0)-1;if($s(r[d],n))return d}return-1}function f2(r){return r&&r.length?Dm(r):[]}function h2(r,n){return r&&r.length?Dm(r,Ce(n,2)):[]}function p2(r){var n=r==null?0:r.length;return n?Ts(r,1,n):[]}function m2(r,n,l){return r&&r.length?(n=l||n===s?1:Ue(n),Ts(r,0,n<0?0:n)):[]}function g2(r,n,l){var d=r==null?0:r.length;return d?(n=l||n===s?1:Ue(n),n=d-n,Ts(r,n<0?0:n,d)):[]}function _2(r,n){return r&&r.length?Ia(r,Ce(n,3),!1,!0):[]}function v2(r,n){return r&&r.length?Ia(r,Ce(n,3)):[]}var y2=je(function(r){return qr(jt(r,1,Et,!0))}),b2=je(function(r){var n=Ns(r);return Et(n)&&(n=s),qr(jt(r,1,Et,!0),Ce(n,2))}),w2=je(function(r){var n=Ns(r);return n=typeof n=="function"?n:s,qr(jt(r,1,Et,!0),s,n)});function E2(r){return r&&r.length?qr(r):[]}function C2(r,n){return r&&r.length?qr(r,Ce(n,2)):[]}function D2(r,n){return n=typeof n=="function"?n:s,r&&r.length?qr(r,s,n):[]}function _c(r){if(!(r&&r.length))return[];var n=0;return r=Fr(r,function(l){if(Et(l))return n=Mt(l.length,n),!0}),ku(n,function(l){return pt(r,Iu(l))})}function cg(r,n){if(!(r&&r.length))return[];var l=_c(r);return n==null?l:pt(l,function(d){return ps(n,s,d)})}var x2=je(function(r,n){return Et(r)?Qo(r,n):[]}),S2=je(function(r){return tc(Fr(r,Et))}),O2=je(function(r){var n=Ns(r);return Et(n)&&(n=s),tc(Fr(r,Et),Ce(n,2))}),T2=je(function(r){var n=Ns(r);return n=typeof n=="function"?n:s,tc(Fr(r,Et),s,n)}),N2=je(_c);function A2(r,n){return Tm(r||[],n||[],Yo)}function I2(r,n){return Tm(r||[],n||[],Xo)}var M2=je(function(r){var n=r.length,l=n>1?r[n-1]:s;return l=typeof l=="function"?(r.pop(),l):s,cg(r,l)});function dg(r){var n=v(r);return n.__chain__=!0,n}function P2(r,n){return n(r),r}function Ba(r,n){return n(r)}var k2=wr(function(r){var n=r.length,l=n?r[0]:0,d=this.__wrapped__,g=function(y){return ju(y,r)};return n>1||this.__actions__.length||!(d instanceof We)||!Er(l)?this.thru(g):(d=d.slice(l,+l+(n?1:0)),d.__actions__.push({func:Ba,args:[g],thisArg:s}),new Ss(d,this.__chain__).thru(function(y){return n&&!y.length&&y.push(s),y}))});function V2(){return dg(this)}function R2(){return new Ss(this.value(),this.__chain__)}function L2(){this.__values__===s&&(this.__values__=xg(this.value()));var r=this.__index__>=this.__values__.length,n=r?s:this.__values__[this.__index__++];return{done:r,value:n}}function U2(){return this}function F2(r){for(var n,l=this;l instanceof xa;){var d=ng(l);d.__index__=0,d.__values__=s,n?g.__wrapped__=d:n=d;var g=d;l=l.__wrapped__}return g.__wrapped__=r,n}function B2(){var r=this.__wrapped__;if(r instanceof We){var n=r;return this.__actions__.length&&(n=new We(this)),n=n.reverse(),n.__actions__.push({func:Ba,args:[gc],thisArg:s}),new Ss(n,this.__chain__)}return this.thru(gc)}function $2(){return Om(this.__wrapped__,this.__actions__)}var j2=Ma(function(r,n,l){it.call(r,l)?++r[l]:yr(r,l,1)});function H2(r,n,l){var d=Ve(r)?Bp:PI;return l&&ss(r,n,l)&&(n=s),d(r,Ce(n,3))}function q2(r,n){var l=Ve(r)?Fr:cm;return l(r,Ce(n,3))}var z2=Um(og),W2=Um(ig);function G2(r,n){return jt($a(r,n),1)}function K2(r,n){return jt($a(r,n),xe)}function Y2(r,n,l){return l=l===s?1:Ue(l),jt($a(r,n),l)}function fg(r,n){var l=Ve(r)?Ds:Hr;return l(r,Ce(n,3))}function hg(r,n){var l=Ve(r)?pA:um;return l(r,Ce(n,3))}var Q2=Ma(function(r,n,l){it.call(r,l)?r[l].push(n):yr(r,l,[n])});function Z2(r,n,l,d){r=os(r)?r:eo(r),l=l&&!d?Ue(l):0;var g=r.length;return l<0&&(l=Mt(g+l,0)),Wa(r)?l<=g&&r.indexOf(n,l)>-1:!!g&&jn(r,n,l)>-1}var J2=je(function(r,n,l){var d=-1,g=typeof n=="function",y=os(r)?I(r.length):[];return Hr(r,function(E){y[++d]=g?ps(n,E,l):Zo(E,n,l)}),y}),X2=Ma(function(r,n,l){yr(r,l,n)});function $a(r,n){var l=Ve(r)?pt:gm;return l(r,Ce(n,3))}function eP(r,n,l,d){return r==null?[]:(Ve(n)||(n=n==null?[]:[n]),l=d?s:l,Ve(l)||(l=l==null?[]:[l]),bm(r,n,l))}var tP=Ma(function(r,n,l){r[l?0:1].push(n)},function(){return[[],[]]});function sP(r,n,l){var d=Ve(r)?Nu:qp,g=arguments.length<3;return d(r,Ce(n,4),l,g,Hr)}function rP(r,n,l){var d=Ve(r)?mA:qp,g=arguments.length<3;return d(r,Ce(n,4),l,g,um)}function nP(r,n){var l=Ve(r)?Fr:cm;return l(r,qa(Ce(n,3)))}function oP(r){var n=Ve(r)?om:ZI;return n(r)}function iP(r,n,l){(l?ss(r,n,l):n===s)?n=1:n=Ue(n);var d=Ve(r)?TI:JI;return d(r,n)}function aP(r){var n=Ve(r)?NI:eM;return n(r)}function lP(r){if(r==null)return 0;if(os(r))return Wa(r)?qn(r):r.length;var n=Gt(r);return n==b||n==K?r.size:Ku(r).length}function uP(r,n,l){var d=Ve(r)?Au:tM;return l&&ss(r,n,l)&&(n=s),d(r,Ce(n,3))}var cP=je(function(r,n){if(r==null)return[];var l=n.length;return l>1&&ss(r,n[0],n[1])?n=[]:l>2&&ss(n[0],n[1],n[2])&&(n=[n[0]]),bm(r,jt(n,1),[])}),ja=qA||function(){return $t.Date.now()};function dP(r,n){if(typeof n!="function")throw new xs(u);return r=Ue(r),function(){if(--r<1)return n.apply(this,arguments)}}function pg(r,n,l){return n=l?s:n,n=r&&n==null?r.length:n,br(r,ve,s,s,s,s,n)}function mg(r,n){var l;if(typeof n!="function")throw new xs(u);return r=Ue(r),function(){return--r>0&&(l=n.apply(this,arguments)),r<=1&&(n=s),l}}var vc=je(function(r,n,l){var d=te;if(l.length){var g=$r(l,Jn(vc));d|=Z}return br(r,d,n,l,g)}),gg=je(function(r,n,l){var d=te|A;if(l.length){var g=$r(l,Jn(gg));d|=Z}return br(n,d,r,l,g)});function _g(r,n,l){n=l?s:n;var d=br(r,G,s,s,s,s,s,n);return d.placeholder=_g.placeholder,d}function vg(r,n,l){n=l?s:n;var d=br(r,ye,s,s,s,s,s,n);return d.placeholder=vg.placeholder,d}function yg(r,n,l){var d,g,y,E,x,T,B=0,j=!1,q=!1,ue=!0;if(typeof r!="function")throw new xs(u);n=As(n)||0,_t(l)&&(j=!!l.leading,q="maxWait"in l,y=q?Mt(As(l.maxWait)||0,n):y,ue="trailing"in l?!!l.trailing:ue);function ge(Ct){var js=d,xr=g;return d=g=s,B=Ct,E=r.apply(xr,js),E}function Se(Ct){return B=Ct,x=si(qe,n),j?ge(Ct):E}function Be(Ct){var js=Ct-T,xr=Ct-B,Ug=n-js;return q?Wt(Ug,y-xr):Ug}function Oe(Ct){var js=Ct-T,xr=Ct-B;return T===s||js>=n||js<0||q&&xr>=y}function qe(){var Ct=ja();if(Oe(Ct))return Ke(Ct);x=si(qe,Be(Ct))}function Ke(Ct){return x=s,ue&&d?ge(Ct):(d=g=s,E)}function vs(){x!==s&&Nm(x),B=0,d=T=g=x=s}function rs(){return x===s?E:Ke(ja())}function ys(){var Ct=ja(),js=Oe(Ct);if(d=arguments,g=this,T=Ct,js){if(x===s)return Se(T);if(q)return Nm(x),x=si(qe,n),ge(T)}return x===s&&(x=si(qe,n)),E}return ys.cancel=vs,ys.flush=rs,ys}var fP=je(function(r,n){return lm(r,1,n)}),hP=je(function(r,n,l){return lm(r,As(n)||0,l)});function pP(r){return br(r,ie)}function Ha(r,n){if(typeof r!="function"||n!=null&&typeof n!="function")throw new xs(u);var l=function(){var d=arguments,g=n?n.apply(this,d):d[0],y=l.cache;if(y.has(g))return y.get(g);var E=r.apply(this,d);return l.cache=y.set(g,E)||y,E};return l.cache=new(Ha.Cache||vr),l}Ha.Cache=vr;function qa(r){if(typeof r!="function")throw new xs(u);return function(){var n=arguments;switch(n.length){case 0:return!r.call(this);case 1:return!r.call(this,n[0]);case 2:return!r.call(this,n[0],n[1]);case 3:return!r.call(this,n[0],n[1],n[2])}return!r.apply(this,n)}}function mP(r){return mg(2,r)}var gP=sM(function(r,n){n=n.length==1&&Ve(n[0])?pt(n[0],ms(Ce())):pt(jt(n,1),ms(Ce()));var l=n.length;return je(function(d){for(var g=-1,y=Wt(d.length,l);++g<y;)d[g]=n[g].call(this,d[g]);return ps(r,this,d)})}),yc=je(function(r,n){var l=$r(n,Jn(yc));return br(r,Z,s,n,l)}),bg=je(function(r,n){var l=$r(n,Jn(bg));return br(r,fe,s,n,l)}),_P=wr(function(r,n){return br(r,Ae,s,s,s,n)});function vP(r,n){if(typeof r!="function")throw new xs(u);return n=n===s?n:Ue(n),je(r,n)}function yP(r,n){if(typeof r!="function")throw new xs(u);return n=n==null?0:Mt(Ue(n),0),je(function(l){var d=l[n],g=Wr(l,0,n);return d&&Br(g,d),ps(r,this,g)})}function bP(r,n,l){var d=!0,g=!0;if(typeof r!="function")throw new xs(u);return _t(l)&&(d="leading"in l?!!l.leading:d,g="trailing"in l?!!l.trailing:g),yg(r,n,{leading:d,maxWait:n,trailing:g})}function wP(r){return pg(r,1)}function EP(r,n){return yc(rc(n),r)}function CP(){if(!arguments.length)return[];var r=arguments[0];return Ve(r)?r:[r]}function DP(r){return Os(r,D)}function xP(r,n){return n=typeof n=="function"?n:s,Os(r,D,n)}function SP(r){return Os(r,_|D)}function OP(r,n){return n=typeof n=="function"?n:s,Os(r,_|D,n)}function TP(r,n){return n==null||am(r,n,kt(n))}function $s(r,n){return r===n||r!==r&&n!==n}var NP=Ra(zu),AP=Ra(function(r,n){return r>=n}),Cn=hm(function(){return arguments}())?hm:function(r){return bt(r)&&it.call(r,"callee")&&!Xp.call(r,"callee")},Ve=I.isArray,IP=kp?ms(kp):FI;function os(r){return r!=null&&za(r.length)&&!Cr(r)}function Et(r){return bt(r)&&os(r)}function MP(r){return r===!0||r===!1||bt(r)&&ts(r)==oe}var Gr=WA||Ac,PP=Vp?ms(Vp):BI;function kP(r){return bt(r)&&r.nodeType===1&&!ri(r)}function VP(r){if(r==null)return!0;if(os(r)&&(Ve(r)||typeof r=="string"||typeof r.splice=="function"||Gr(r)||Xn(r)||Cn(r)))return!r.length;var n=Gt(r);if(n==b||n==K)return!r.size;if(ti(r))return!Ku(r).length;for(var l in r)if(it.call(r,l))return!1;return!0}function RP(r,n){return Jo(r,n)}function LP(r,n,l){l=typeof l=="function"?l:s;var d=l?l(r,n):s;return d===s?Jo(r,n,s,l):!!d}function bc(r){if(!bt(r))return!1;var n=ts(r);return n==Pe||n==pe||typeof r.message=="string"&&typeof r.name=="string"&&!ri(r)}function UP(r){return typeof r=="number"&&tm(r)}function Cr(r){if(!_t(r))return!1;var n=ts(r);return n==ot||n==ke||n==R||n==z}function wg(r){return typeof r=="number"&&r==Ue(r)}function za(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=be}function _t(r){var n=typeof r;return r!=null&&(n=="object"||n=="function")}function bt(r){return r!=null&&typeof r=="object"}var Eg=Rp?ms(Rp):jI;function FP(r,n){return r===n||Gu(r,n,cc(n))}function BP(r,n,l){return l=typeof l=="function"?l:s,Gu(r,n,cc(n),l)}function $P(r){return Cg(r)&&r!=+r}function jP(r){if(xM(r))throw new Me(a);return pm(r)}function HP(r){return r===null}function qP(r){return r==null}function Cg(r){return typeof r=="number"||bt(r)&&ts(r)==C}function ri(r){if(!bt(r)||ts(r)!=U)return!1;var n=va(r);if(n===null)return!0;var l=it.call(n,"constructor")&&n.constructor;return typeof l=="function"&&l instanceof l&&pa.call(l)==BA}var wc=Lp?ms(Lp):HI;function zP(r){return wg(r)&&r>=-be&&r<=be}var Dg=Up?ms(Up):qI;function Wa(r){return typeof r=="string"||!Ve(r)&&bt(r)&&ts(r)==J}function _s(r){return typeof r=="symbol"||bt(r)&&ts(r)==W}var Xn=Fp?ms(Fp):zI;function WP(r){return r===s}function GP(r){return bt(r)&&Gt(r)==re}function KP(r){return bt(r)&&ts(r)==_e}var YP=Ra(Yu),QP=Ra(function(r,n){return r<=n});function xg(r){if(!r)return[];if(os(r))return Wa(r)?Fs(r):ns(r);if(qo&&r[qo])return TA(r[qo]());var n=Gt(r),l=n==b?Ru:n==K?da:eo;return l(r)}function Dr(r){if(!r)return r===0?r:0;if(r=As(r),r===xe||r===-xe){var n=r<0?-1:1;return n*Ut}return r===r?r:0}function Ue(r){var n=Dr(r),l=n%1;return n===n?l?n-l:n:0}function Sg(r){return r?yn(Ue(r),0,yt):0}function As(r){if(typeof r=="number")return r;if(_s(r))return Xt;if(_t(r)){var n=typeof r.valueOf=="function"?r.valueOf():r;r=_t(n)?n+"":n}if(typeof r!="string")return r===0?r:+r;r=zp(r);var l=IN.test(r);return l||PN.test(r)?dA(r.slice(2),l?2:8):AN.test(r)?Xt:+r}function Og(r){return Js(r,is(r))}function ZP(r){return r?yn(Ue(r),-be,be):r===0?r:0}function rt(r){return r==null?"":gs(r)}var JP=Qn(function(r,n){if(ti(n)||os(n)){Js(n,kt(n),r);return}for(var l in n)it.call(n,l)&&Yo(r,l,n[l])}),Tg=Qn(function(r,n){Js(n,is(n),r)}),Ga=Qn(function(r,n,l,d){Js(n,is(n),r,d)}),XP=Qn(function(r,n,l,d){Js(n,kt(n),r,d)}),ek=wr(ju);function tk(r,n){var l=Yn(r);return n==null?l:im(l,n)}var sk=je(function(r,n){r=at(r);var l=-1,d=n.length,g=d>2?n[2]:s;for(g&&ss(n[0],n[1],g)&&(d=1);++l<d;)for(var y=n[l],E=is(y),x=-1,T=E.length;++x<T;){var B=E[x],j=r[B];(j===s||$s(j,Wn[B])&&!it.call(r,B))&&(r[B]=y[B])}return r}),rk=je(function(r){return r.push(s,zm),ps(Ng,s,r)});function nk(r,n){return $p(r,Ce(n,3),Zs)}function ok(r,n){return $p(r,Ce(n,3),qu)}function ik(r,n){return r==null?r:Hu(r,Ce(n,3),is)}function ak(r,n){return r==null?r:dm(r,Ce(n,3),is)}function lk(r,n){return r&&Zs(r,Ce(n,3))}function uk(r,n){return r&&qu(r,Ce(n,3))}function ck(r){return r==null?[]:Ta(r,kt(r))}function dk(r){return r==null?[]:Ta(r,is(r))}function Ec(r,n,l){var d=r==null?s:bn(r,n);return d===s?l:d}function fk(r,n){return r!=null&&Km(r,n,VI)}function Cc(r,n){return r!=null&&Km(r,n,RI)}var hk=Bm(function(r,n,l){n!=null&&typeof n.toString!="function"&&(n=ma.call(n)),r[n]=l},xc(as)),pk=Bm(function(r,n,l){n!=null&&typeof n.toString!="function"&&(n=ma.call(n)),it.call(r,n)?r[n].push(l):r[n]=[l]},Ce),mk=je(Zo);function kt(r){return os(r)?nm(r):Ku(r)}function is(r){return os(r)?nm(r,!0):WI(r)}function gk(r,n){var l={};return n=Ce(n,3),Zs(r,function(d,g,y){yr(l,n(d,g,y),d)}),l}function _k(r,n){var l={};return n=Ce(n,3),Zs(r,function(d,g,y){yr(l,g,n(d,g,y))}),l}var vk=Qn(function(r,n,l){Na(r,n,l)}),Ng=Qn(function(r,n,l,d){Na(r,n,l,d)}),yk=wr(function(r,n){var l={};if(r==null)return l;var d=!1;n=pt(n,function(y){return y=zr(y,r),d||(d=y.length>1),y}),Js(r,lc(r),l),d&&(l=Os(l,_|w|D,hM));for(var g=n.length;g--;)ec(l,n[g]);return l});function bk(r,n){return Ag(r,qa(Ce(n)))}var wk=wr(function(r,n){return r==null?{}:KI(r,n)});function Ag(r,n){if(r==null)return{};var l=pt(lc(r),function(d){return[d]});return n=Ce(n),wm(r,l,function(d,g){return n(d,g[0])})}function Ek(r,n,l){n=zr(n,r);var d=-1,g=n.length;for(g||(g=1,r=s);++d<g;){var y=r==null?s:r[Xs(n[d])];y===s&&(d=g,y=l),r=Cr(y)?y.call(r):y}return r}function Ck(r,n,l){return r==null?r:Xo(r,n,l)}function Dk(r,n,l,d){return d=typeof d=="function"?d:s,r==null?r:Xo(r,n,l,d)}var Ig=Hm(kt),Mg=Hm(is);function xk(r,n,l){var d=Ve(r),g=d||Gr(r)||Xn(r);if(n=Ce(n,4),l==null){var y=r&&r.constructor;g?l=d?new y:[]:_t(r)?l=Cr(y)?Yn(va(r)):{}:l={}}return(g?Ds:Zs)(r,function(E,x,T){return n(l,E,x,T)}),l}function Sk(r,n){return r==null?!0:ec(r,n)}function Ok(r,n,l){return r==null?r:Sm(r,n,rc(l))}function Tk(r,n,l,d){return d=typeof d=="function"?d:s,r==null?r:Sm(r,n,rc(l),d)}function eo(r){return r==null?[]:Vu(r,kt(r))}function Nk(r){return r==null?[]:Vu(r,is(r))}function Ak(r,n,l){return l===s&&(l=n,n=s),l!==s&&(l=As(l),l=l===l?l:0),n!==s&&(n=As(n),n=n===n?n:0),yn(As(r),n,l)}function Ik(r,n,l){return n=Dr(n),l===s?(l=n,n=0):l=Dr(l),r=As(r),LI(r,n,l)}function Mk(r,n,l){if(l&&typeof l!="boolean"&&ss(r,n,l)&&(n=l=s),l===s&&(typeof n=="boolean"?(l=n,n=s):typeof r=="boolean"&&(l=r,r=s)),r===s&&n===s?(r=0,n=1):(r=Dr(r),n===s?(n=r,r=0):n=Dr(n)),r>n){var d=r;r=n,n=d}if(l||r%1||n%1){var g=sm();return Wt(r+g*(n-r+cA("1e-"+((g+"").length-1))),n)}return Zu(r,n)}var Pk=Zn(function(r,n,l){return n=n.toLowerCase(),r+(l?Pg(n):n)});function Pg(r){return Dc(rt(r).toLowerCase())}function kg(r){return r=rt(r),r&&r.replace(VN,CA).replace(eA,"")}function kk(r,n,l){r=rt(r),n=gs(n);var d=r.length;l=l===s?d:yn(Ue(l),0,d);var g=l;return l-=n.length,l>=0&&r.slice(l,g)==n}function Vk(r){return r=rt(r),r&&mN.test(r)?r.replace(dp,DA):r}function Rk(r){return r=rt(r),r&&wN.test(r)?r.replace(yu,"\\$&"):r}var Lk=Zn(function(r,n,l){return r+(l?"-":"")+n.toLowerCase()}),Uk=Zn(function(r,n,l){return r+(l?" ":"")+n.toLowerCase()}),Fk=Lm("toLowerCase");function Bk(r,n,l){r=rt(r),n=Ue(n);var d=n?qn(r):0;if(!n||d>=n)return r;var g=(n-d)/2;return Va(Ea(g),l)+r+Va(wa(g),l)}function $k(r,n,l){r=rt(r),n=Ue(n);var d=n?qn(r):0;return n&&d<n?r+Va(n-d,l):r}function jk(r,n,l){r=rt(r),n=Ue(n);var d=n?qn(r):0;return n&&d<n?Va(n-d,l)+r:r}function Hk(r,n,l){return l||n==null?n=0:n&&(n=+n),QA(rt(r).replace(bu,""),n||0)}function qk(r,n,l){return(l?ss(r,n,l):n===s)?n=1:n=Ue(n),Ju(rt(r),n)}function zk(){var r=arguments,n=rt(r[0]);return r.length<3?n:n.replace(r[1],r[2])}var Wk=Zn(function(r,n,l){return r+(l?"_":"")+n.toLowerCase()});function Gk(r,n,l){return l&&typeof l!="number"&&ss(r,n,l)&&(n=l=s),l=l===s?yt:l>>>0,l?(r=rt(r),r&&(typeof n=="string"||n!=null&&!wc(n))&&(n=gs(n),!n&&Hn(r))?Wr(Fs(r),0,l):r.split(n,l)):[]}var Kk=Zn(function(r,n,l){return r+(l?" ":"")+Dc(n)});function Yk(r,n,l){return r=rt(r),l=l==null?0:yn(Ue(l),0,r.length),n=gs(n),r.slice(l,l+n.length)==n}function Qk(r,n,l){var d=v.templateSettings;l&&ss(r,n,l)&&(n=s),r=rt(r),n=Ga({},n,d,qm);var g=Ga({},n.imports,d.imports,qm),y=kt(g),E=Vu(g,y),x,T,B=0,j=n.interpolate||ia,q="__p += '",ue=Lu((n.escape||ia).source+"|"+j.source+"|"+(j===fp?NN:ia).source+"|"+(n.evaluate||ia).source+"|$","g"),ge="//# sourceURL="+(it.call(n,"sourceURL")?(n.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++oA+"]")+`
`;r.replace(ue,function(Oe,qe,Ke,vs,rs,ys){return Ke||(Ke=vs),q+=r.slice(B,ys).replace(RN,xA),qe&&(x=!0,q+=`' +
__e(`+qe+`) +
'`),rs&&(T=!0,q+=`';
`+rs+`;
__p += '`),Ke&&(q+=`' +
((__t = (`+Ke+`)) == null ? '' : __t) +
'`),B=ys+Oe.length,Oe}),q+=`';
`;var Se=it.call(n,"variable")&&n.variable;if(!Se)q=`with (obj) {
`+q+`
}
`;else if(ON.test(Se))throw new Me(c);q=(T?q.replace(Es,""):q).replace(oa,"$1").replace(hN,"$1;"),q="function("+(Se||"obj")+`) {
`+(Se?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(x?", __e = _.escape":"")+(T?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+q+`return __p
}`;var Be=Rg(function(){return et(y,ge+"return "+q).apply(s,E)});if(Be.source=q,bc(Be))throw Be;return Be}function Zk(r){return rt(r).toLowerCase()}function Jk(r){return rt(r).toUpperCase()}function Xk(r,n,l){if(r=rt(r),r&&(l||n===s))return zp(r);if(!r||!(n=gs(n)))return r;var d=Fs(r),g=Fs(n),y=Wp(d,g),E=Gp(d,g)+1;return Wr(d,y,E).join("")}function eV(r,n,l){if(r=rt(r),r&&(l||n===s))return r.slice(0,Yp(r)+1);if(!r||!(n=gs(n)))return r;var d=Fs(r),g=Gp(d,Fs(n))+1;return Wr(d,0,g).join("")}function tV(r,n,l){if(r=rt(r),r&&(l||n===s))return r.replace(bu,"");if(!r||!(n=gs(n)))return r;var d=Fs(r),g=Wp(d,Fs(n));return Wr(d,g).join("")}function sV(r,n){var l=V,d=Te;if(_t(n)){var g="separator"in n?n.separator:g;l="length"in n?Ue(n.length):l,d="omission"in n?gs(n.omission):d}r=rt(r);var y=r.length;if(Hn(r)){var E=Fs(r);y=E.length}if(l>=y)return r;var x=l-qn(d);if(x<1)return d;var T=E?Wr(E,0,x).join(""):r.slice(0,x);if(g===s)return T+d;if(E&&(x+=T.length-x),wc(g)){if(r.slice(x).search(g)){var B,j=T;for(g.global||(g=Lu(g.source,rt(hp.exec(g))+"g")),g.lastIndex=0;B=g.exec(j);)var q=B.index;T=T.slice(0,q===s?x:q)}}else if(r.indexOf(gs(g),x)!=x){var ue=T.lastIndexOf(g);ue>-1&&(T=T.slice(0,ue))}return T+d}function rV(r){return r=rt(r),r&&pN.test(r)?r.replace(cp,MA):r}var nV=Zn(function(r,n,l){return r+(l?" ":"")+n.toUpperCase()}),Dc=Lm("toUpperCase");function Vg(r,n,l){return r=rt(r),n=l?s:n,n===s?OA(r)?VA(r):vA(r):r.match(n)||[]}var Rg=je(function(r,n){try{return ps(r,s,n)}catch(l){return bc(l)?l:new Me(l)}}),oV=wr(function(r,n){return Ds(n,function(l){l=Xs(l),yr(r,l,vc(r[l],r))}),r});function iV(r){var n=r==null?0:r.length,l=Ce();return r=n?pt(r,function(d){if(typeof d[1]!="function")throw new xs(u);return[l(d[0]),d[1]]}):[],je(function(d){for(var g=-1;++g<n;){var y=r[g];if(ps(y[0],this,d))return ps(y[1],this,d)}})}function aV(r){return MI(Os(r,_))}function xc(r){return function(){return r}}function lV(r,n){return r==null||r!==r?n:r}var uV=Fm(),cV=Fm(!0);function as(r){return r}function Sc(r){return mm(typeof r=="function"?r:Os(r,_))}function dV(r){return _m(Os(r,_))}function fV(r,n){return vm(r,Os(n,_))}var hV=je(function(r,n){return function(l){return Zo(l,r,n)}}),pV=je(function(r,n){return function(l){return Zo(r,l,n)}});function Oc(r,n,l){var d=kt(n),g=Ta(n,d);l==null&&!(_t(n)&&(g.length||!d.length))&&(l=n,n=r,r=this,g=Ta(n,kt(n)));var y=!(_t(l)&&"chain"in l)||!!l.chain,E=Cr(r);return Ds(g,function(x){var T=n[x];r[x]=T,E&&(r.prototype[x]=function(){var B=this.__chain__;if(y||B){var j=r(this.__wrapped__),q=j.__actions__=ns(this.__actions__);return q.push({func:T,args:arguments,thisArg:r}),j.__chain__=B,j}return T.apply(r,Br([this.value()],arguments))})}),r}function mV(){return $t._===this&&($t._=$A),this}function Tc(){}function gV(r){return r=Ue(r),je(function(n){return ym(n,r)})}var _V=oc(pt),vV=oc(Bp),yV=oc(Au);function Lg(r){return fc(r)?Iu(Xs(r)):YI(r)}function bV(r){return function(n){return r==null?s:bn(r,n)}}var wV=$m(),EV=$m(!0);function Nc(){return[]}function Ac(){return!1}function CV(){return{}}function DV(){return""}function xV(){return!0}function SV(r,n){if(r=Ue(r),r<1||r>be)return[];var l=yt,d=Wt(r,yt);n=Ce(n),r-=yt;for(var g=ku(d,n);++l<r;)n(l);return g}function OV(r){return Ve(r)?pt(r,Xs):_s(r)?[r]:ns(rg(rt(r)))}function TV(r){var n=++FA;return rt(r)+n}var NV=ka(function(r,n){return r+n},0),AV=ic("ceil"),IV=ka(function(r,n){return r/n},1),MV=ic("floor");function PV(r){return r&&r.length?Oa(r,as,zu):s}function kV(r,n){return r&&r.length?Oa(r,Ce(n,2),zu):s}function VV(r){return Hp(r,as)}function RV(r,n){return Hp(r,Ce(n,2))}function LV(r){return r&&r.length?Oa(r,as,Yu):s}function UV(r,n){return r&&r.length?Oa(r,Ce(n,2),Yu):s}var FV=ka(function(r,n){return r*n},1),BV=ic("round"),$V=ka(function(r,n){return r-n},0);function jV(r){return r&&r.length?Pu(r,as):0}function HV(r,n){return r&&r.length?Pu(r,Ce(n,2)):0}return v.after=dP,v.ary=pg,v.assign=JP,v.assignIn=Tg,v.assignInWith=Ga,v.assignWith=XP,v.at=ek,v.before=mg,v.bind=vc,v.bindAll=oV,v.bindKey=gg,v.castArray=CP,v.chain=dg,v.chunk=MM,v.compact=PM,v.concat=kM,v.cond=iV,v.conforms=aV,v.constant=xc,v.countBy=j2,v.create=tk,v.curry=_g,v.curryRight=vg,v.debounce=yg,v.defaults=sk,v.defaultsDeep=rk,v.defer=fP,v.delay=hP,v.difference=VM,v.differenceBy=RM,v.differenceWith=LM,v.drop=UM,v.dropRight=FM,v.dropRightWhile=BM,v.dropWhile=$M,v.fill=jM,v.filter=q2,v.flatMap=G2,v.flatMapDeep=K2,v.flatMapDepth=Y2,v.flatten=ag,v.flattenDeep=HM,v.flattenDepth=qM,v.flip=pP,v.flow=uV,v.flowRight=cV,v.fromPairs=zM,v.functions=ck,v.functionsIn=dk,v.groupBy=Q2,v.initial=GM,v.intersection=KM,v.intersectionBy=YM,v.intersectionWith=QM,v.invert=hk,v.invertBy=pk,v.invokeMap=J2,v.iteratee=Sc,v.keyBy=X2,v.keys=kt,v.keysIn=is,v.map=$a,v.mapKeys=gk,v.mapValues=_k,v.matches=dV,v.matchesProperty=fV,v.memoize=Ha,v.merge=vk,v.mergeWith=Ng,v.method=hV,v.methodOf=pV,v.mixin=Oc,v.negate=qa,v.nthArg=gV,v.omit=yk,v.omitBy=bk,v.once=mP,v.orderBy=eP,v.over=_V,v.overArgs=gP,v.overEvery=vV,v.overSome=yV,v.partial=yc,v.partialRight=bg,v.partition=tP,v.pick=wk,v.pickBy=Ag,v.property=Lg,v.propertyOf=bV,v.pull=e2,v.pullAll=ug,v.pullAllBy=t2,v.pullAllWith=s2,v.pullAt=r2,v.range=wV,v.rangeRight=EV,v.rearg=_P,v.reject=nP,v.remove=n2,v.rest=vP,v.reverse=gc,v.sampleSize=iP,v.set=Ck,v.setWith=Dk,v.shuffle=aP,v.slice=o2,v.sortBy=cP,v.sortedUniq=f2,v.sortedUniqBy=h2,v.split=Gk,v.spread=yP,v.tail=p2,v.take=m2,v.takeRight=g2,v.takeRightWhile=_2,v.takeWhile=v2,v.tap=P2,v.throttle=bP,v.thru=Ba,v.toArray=xg,v.toPairs=Ig,v.toPairsIn=Mg,v.toPath=OV,v.toPlainObject=Og,v.transform=xk,v.unary=wP,v.union=y2,v.unionBy=b2,v.unionWith=w2,v.uniq=E2,v.uniqBy=C2,v.uniqWith=D2,v.unset=Sk,v.unzip=_c,v.unzipWith=cg,v.update=Ok,v.updateWith=Tk,v.values=eo,v.valuesIn=Nk,v.without=x2,v.words=Vg,v.wrap=EP,v.xor=S2,v.xorBy=O2,v.xorWith=T2,v.zip=N2,v.zipObject=A2,v.zipObjectDeep=I2,v.zipWith=M2,v.entries=Ig,v.entriesIn=Mg,v.extend=Tg,v.extendWith=Ga,Oc(v,v),v.add=NV,v.attempt=Rg,v.camelCase=Pk,v.capitalize=Pg,v.ceil=AV,v.clamp=Ak,v.clone=DP,v.cloneDeep=SP,v.cloneDeepWith=OP,v.cloneWith=xP,v.conformsTo=TP,v.deburr=kg,v.defaultTo=lV,v.divide=IV,v.endsWith=kk,v.eq=$s,v.escape=Vk,v.escapeRegExp=Rk,v.every=H2,v.find=z2,v.findIndex=og,v.findKey=nk,v.findLast=W2,v.findLastIndex=ig,v.findLastKey=ok,v.floor=MV,v.forEach=fg,v.forEachRight=hg,v.forIn=ik,v.forInRight=ak,v.forOwn=lk,v.forOwnRight=uk,v.get=Ec,v.gt=NP,v.gte=AP,v.has=fk,v.hasIn=Cc,v.head=lg,v.identity=as,v.includes=Z2,v.indexOf=WM,v.inRange=Ik,v.invoke=mk,v.isArguments=Cn,v.isArray=Ve,v.isArrayBuffer=IP,v.isArrayLike=os,v.isArrayLikeObject=Et,v.isBoolean=MP,v.isBuffer=Gr,v.isDate=PP,v.isElement=kP,v.isEmpty=VP,v.isEqual=RP,v.isEqualWith=LP,v.isError=bc,v.isFinite=UP,v.isFunction=Cr,v.isInteger=wg,v.isLength=za,v.isMap=Eg,v.isMatch=FP,v.isMatchWith=BP,v.isNaN=$P,v.isNative=jP,v.isNil=qP,v.isNull=HP,v.isNumber=Cg,v.isObject=_t,v.isObjectLike=bt,v.isPlainObject=ri,v.isRegExp=wc,v.isSafeInteger=zP,v.isSet=Dg,v.isString=Wa,v.isSymbol=_s,v.isTypedArray=Xn,v.isUndefined=WP,v.isWeakMap=GP,v.isWeakSet=KP,v.join=ZM,v.kebabCase=Lk,v.last=Ns,v.lastIndexOf=JM,v.lowerCase=Uk,v.lowerFirst=Fk,v.lt=YP,v.lte=QP,v.max=PV,v.maxBy=kV,v.mean=VV,v.meanBy=RV,v.min=LV,v.minBy=UV,v.stubArray=Nc,v.stubFalse=Ac,v.stubObject=CV,v.stubString=DV,v.stubTrue=xV,v.multiply=FV,v.nth=XM,v.noConflict=mV,v.noop=Tc,v.now=ja,v.pad=Bk,v.padEnd=$k,v.padStart=jk,v.parseInt=Hk,v.random=Mk,v.reduce=sP,v.reduceRight=rP,v.repeat=qk,v.replace=zk,v.result=Ek,v.round=BV,v.runInContext=S,v.sample=oP,v.size=lP,v.snakeCase=Wk,v.some=uP,v.sortedIndex=i2,v.sortedIndexBy=a2,v.sortedIndexOf=l2,v.sortedLastIndex=u2,v.sortedLastIndexBy=c2,v.sortedLastIndexOf=d2,v.startCase=Kk,v.startsWith=Yk,v.subtract=$V,v.sum=jV,v.sumBy=HV,v.template=Qk,v.times=SV,v.toFinite=Dr,v.toInteger=Ue,v.toLength=Sg,v.toLower=Zk,v.toNumber=As,v.toSafeInteger=ZP,v.toString=rt,v.toUpper=Jk,v.trim=Xk,v.trimEnd=eV,v.trimStart=tV,v.truncate=sV,v.unescape=rV,v.uniqueId=TV,v.upperCase=nV,v.upperFirst=Dc,v.each=fg,v.eachRight=hg,v.first=lg,Oc(v,function(){var r={};return Zs(v,function(n,l){it.call(v.prototype,l)||(r[l]=n)}),r}(),{chain:!1}),v.VERSION=i,Ds(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){v[r].placeholder=v}),Ds(["drop","take"],function(r,n){We.prototype[r]=function(l){l=l===s?1:Mt(Ue(l),0);var d=this.__filtered__&&!n?new We(this):this.clone();return d.__filtered__?d.__takeCount__=Wt(l,d.__takeCount__):d.__views__.push({size:Wt(l,yt),type:r+(d.__dir__<0?"Right":"")}),d},We.prototype[r+"Right"]=function(l){return this.reverse()[r](l).reverse()}}),Ds(["filter","map","takeWhile"],function(r,n){var l=n+1,d=l==gt||l==ct;We.prototype[r]=function(g){var y=this.clone();return y.__iteratees__.push({iteratee:Ce(g,3),type:l}),y.__filtered__=y.__filtered__||d,y}}),Ds(["head","last"],function(r,n){var l="take"+(n?"Right":"");We.prototype[r]=function(){return this[l](1).value()[0]}}),Ds(["initial","tail"],function(r,n){var l="drop"+(n?"":"Right");We.prototype[r]=function(){return this.__filtered__?new We(this):this[l](1)}}),We.prototype.compact=function(){return this.filter(as)},We.prototype.find=function(r){return this.filter(r).head()},We.prototype.findLast=function(r){return this.reverse().find(r)},We.prototype.invokeMap=je(function(r,n){return typeof r=="function"?new We(this):this.map(function(l){return Zo(l,r,n)})}),We.prototype.reject=function(r){return this.filter(qa(Ce(r)))},We.prototype.slice=function(r,n){r=Ue(r);var l=this;return l.__filtered__&&(r>0||n<0)?new We(l):(r<0?l=l.takeRight(-r):r&&(l=l.drop(r)),n!==s&&(n=Ue(n),l=n<0?l.dropRight(-n):l.take(n-r)),l)},We.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},We.prototype.toArray=function(){return this.take(yt)},Zs(We.prototype,function(r,n){var l=/^(?:filter|find|map|reject)|While$/.test(n),d=/^(?:head|last)$/.test(n),g=v[d?"take"+(n=="last"?"Right":""):n],y=d||/^find/.test(n);g&&(v.prototype[n]=function(){var E=this.__wrapped__,x=d?[1]:arguments,T=E instanceof We,B=x[0],j=T||Ve(E),q=function(qe){var Ke=g.apply(v,Br([qe],x));return d&&ue?Ke[0]:Ke};j&&l&&typeof B=="function"&&B.length!=1&&(T=j=!1);var ue=this.__chain__,ge=!!this.__actions__.length,Se=y&&!ue,Be=T&&!ge;if(!y&&j){E=Be?E:new We(this);var Oe=r.apply(E,x);return Oe.__actions__.push({func:Ba,args:[q],thisArg:s}),new Ss(Oe,ue)}return Se&&Be?r.apply(this,x):(Oe=this.thru(q),Se?d?Oe.value()[0]:Oe.value():Oe)})}),Ds(["pop","push","shift","sort","splice","unshift"],function(r){var n=fa[r],l=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",d=/^(?:pop|shift)$/.test(r);v.prototype[r]=function(){var g=arguments;if(d&&!this.__chain__){var y=this.value();return n.apply(Ve(y)?y:[],g)}return this[l](function(E){return n.apply(Ve(E)?E:[],g)})}}),Zs(We.prototype,function(r,n){var l=v[n];if(l){var d=l.name+"";it.call(Kn,d)||(Kn[d]=[]),Kn[d].push({name:n,func:l})}}),Kn[Pa(s,A).name]=[{name:"wrapper",func:s}],We.prototype.clone=rI,We.prototype.reverse=nI,We.prototype.value=oI,v.prototype.at=k2,v.prototype.chain=V2,v.prototype.commit=R2,v.prototype.next=L2,v.prototype.plant=F2,v.prototype.reverse=B2,v.prototype.toJSON=v.prototype.valueOf=v.prototype.value=$2,v.prototype.first=v.prototype.head,qo&&(v.prototype[qo]=U2),v},zn=RA();mn?((mn.exports=zn)._=zn,Su._=zn):$t._=zn}).call(Ro)}(Ji,Ji.exports);var Zh=Ji.exports;const Fe=async(e,t)=>{try{const s=window.M.cfg.wwwroot+"/lib/ajax/service.php?sesskey="+window.M.cfg.sesskey+"&info="+e,o=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify([{index:0,methodname:e,args:t}])}),a=o.clone();try{return(await o.json())[0]}catch{return{error:await a.text()}}}catch(s){throw console.error("Erro na chamada AJAX:",s),s}};async function Q1(e={}){try{return await Fe("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw new Error(t.message||"Erro ao buscar ofertas")}}async function Jh(e){try{return await Fe("local_offermanager_get",{id:e})}catch(t){throw new Error(t.message||"Erro ao buscar oferta")}}async function Xh(e){try{return await Fe("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",audienceids:e.audiences||[]})}catch(t){throw new Error(t.message||"Erro ao salvar oferta")}}async function Z1(e){try{return await Fe("local_offermanager_delete",{id:e})}catch(t){throw new Error(t.message||"Erro ao excluir oferta")}}async function fu(){const e=await Fe("local_offermanager_get_type_options",{});if(e.error)throw new Error(error.message||"Erro ao buscar opções de tipos");return e}async function J1(e,t){try{return await Fe("local_offermanager_add_course_to_offer",{offer_id:e,course:t})}catch(s){throw new Error(s.message||"Erro ao adicionar curso à oferta")}}async function X1(e,t){try{return await Fe("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw new Error(s.message||"Erro ao remover curso da oferta")}}async function e0(e,t,s){try{return await Fe("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw new Error(i.message||"Erro ao alterar status do curso")}}async function ep(e){var t;try{const s=await Fe("local_offermanager_get_audiences",{offerid:0});return(t=s==null?void 0:s.data)!=null&&t.all_audiences?{items:s.data.all_audiences.filter(o=>o.name.toLowerCase().includes(e.toLowerCase())).map(o=>({id:o.id,name:o.name}))}:{items:[]}}catch(s){throw new Error(s)}}async function t0(e,t){try{return await Fe("local_offermanager_update_audiences",{offerid:e,audienceids:t})}catch(s){throw new Error(s)}}async function s0(e,t){try{return await Fe("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw new Error(s.message||"Erro ao alterar status da oferta")}}async function Lo(e="",t=0){try{return await Fe("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw new Error(s.message||"Erro ao buscar categorias")}}async function tp(e,t,s="",i=1,o=20){try{console.log(`getCoursesByCategory - Parâmetros: offerId=${e}, categoryId=${t}, search=${s}, page=${i}, perPage=${o}`);const a=parseInt(e,10),u=parseInt(t,10),c=parseInt(i,10),h=parseInt(o,10);(isNaN(a)||isNaN(u)||isNaN(c)||isNaN(h))&&console.error("Parâmetros inválidos para getCoursesByCategory");const m={offerid:a,categoryid:u,search_string:s||"",page:c,per_page:h,exclude_courseids:[]};console.log("Chamando endpoint com parâmetros:",m);const p=await Fe("local_offermanager_fetch_potential_courses",m);return console.log("Resposta bruta do endpoint:",p),p}catch(a){throw console.error("Erro em getCoursesByCategory:",a),new Error(a.message||"Erro ao buscar cursos")}}async function sp(e,t=""){try{return await Fe("local_offermanager_fetch_current_courses",{offerid:e,categoryid:0,search_string:t,exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por nome")}}async function rp(e,t){try{return await Fe("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:"",exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por categoria")}}async function r0(e,t){try{return await Fe("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw new Error(s.message||"Erro ao adicionar cursos à oferta")}}async function Xi(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="turmasCount"&&(t.sortBy="class_counter"),await Fe("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw new Error(s.message||"Erro ao buscar cursos da oferta")}}async function n0(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={optional_fields:{}};e.offercourseid?s.offercourseid=parseInt(e.offercourseid):console.error("offercourseid não está definido nos parâmetros"),e.classname?s.classname=e.classname:console.error("classname não está definido nos parâmetros"),e.startdate?s.startdate=e.startdate:console.error("startdate não está definido nos parâmetros"),e.teachers&&Array.isArray(e.teachers)?s.teachers=[...e.teachers]:(console.warn("teachers não está definido nos parâmetros ou não é um array"),s.teachers=[]),e.enrol?s.enrol=e.enrol:console.error("enrol não está definido nos parâmetros"),e.optional_fields&&t.forEach(u=>{if(u in e.optional_fields){const c=e.optional_fields[u];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(u)?c!==0&&c!==null&&c!==void 0&&c!==""&&(s.optional_fields[u]=c):typeof c=="boolean"?s.optional_fields[u]=c:Array.isArray(c)?c.length>0&&(s.optional_fields[u]=c):c!=null&&c!==""&&(s.optional_fields[u]=c)}});const o=["offercourseid","classname","startdate","enrol"].filter(u=>!s[u]);if(o.length>0)throw console.error("Campos obrigatórios ausentes no serviço:",o),new Error(`Campos obrigatórios ausentes: ${o.join(", ")}`);return await Fe("local_offermanager_add_class",s)}catch(t){throw console.error("Erro ao criar turma:",t),new Error(t.message||"Erro ao criar turma")}}async function hu(e){try{return await Fe("local_offermanager_get_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao buscar turma")}}async function o0(e){try{const t=await Fe("local_offermanager_get_course",{offercourseid:e});return t.error?[]:t}catch(t){throw new Error(t.message||"Erro ao buscar curso da oferta")}}async function i0(e){try{const t=await Fe("local_offermanager_get_classes",{offercourseid:e});return Array.isArray(t)&&t.length===0?(console.log(`Curso ${e} não tem turmas (array vazio)`),[]):Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data)&&t[0].data.length===0?(console.log(`Curso ${e} não tem turmas (data vazio)`),[]):t}catch(t){throw console.error(`Erro ao buscar turmas do curso ${e}:`,t),new Error(t.message||"Erro ao buscar curso da oferta")}}async function a0(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return e.optional_fields&&t.forEach(o=>{if(o in e.optional_fields){const a=e.optional_fields[o];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(o)?a!==0&&a!==null&&a!==void 0&&a!==""&&(s.optional_fields[o]=a):typeof a=="boolean"?s.optional_fields[o]=a:Array.isArray(a)?a.length>0&&(s.optional_fields[o]=a):a!=null&&a!==""&&(s.optional_fields[o]=a)}}),console.log("Campos enviados para a API de atualização:",Object.keys(s.optional_fields)),console.log("Objeto completo enviado para a API de atualização:",s),"enrol"in s&&delete s.enrol,await Fe("local_offermanager_update_class",s)}catch(t){throw new Error(t.message||"Erro ao atualizar turma")}}async function l0(e){try{return await Fe("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao excluir turma")}}async function u0(e,t=0,s="",i=[]){try{return await Fe("local_offermanager_get_potential_teachers",{offercourseid:e,search_string:s,offerclassid:t,excluded_userids:i})}catch(o){throw new Error(o.message||"Erro ao buscar professores")}}async function c0(){try{const e=await Fe("local_offermanager_get_situation_list",{});if(e.error)throw new Error(e.exception.message||"Erro ao buscar situações de matrícula");return e}catch(e){throw new Error(e.message||"Erro ao buscar situações de matrícula")}}async function d0(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await Fe("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw new Error(s.message||"Erro ao duplicar turma")}}async function f0(e){try{const t=parseInt(e,10);if(isNaN(t))throw new Error("ID da turma inválido");const s=await Fe("local_offermanager_get_duplication_courses",{offerclassid:t});let i;return s&&s.data&&Array.isArray(s.data)?i=s.data:i=s,Array.isArray(i)?i.map(a=>({id:a.id,name:a.name||a.fullname,courseid:a.courseid||null,offercourseid:a.offercourseid||a.id,categoryid:a.categoryid||null,category_name:a.category_name||""})):[]}catch(t){try{const s=await Fe("local_offermanager_get_class",{id:parseInt(e,10)});let i,o;if(s&&s.data)i=s.data.offerid,o=s.data.offercourseid;else if(s)i=s.offerid,o=s.offercourseid;else throw new Error("Não foi possível determinar a oferta da turma");const a=await Fe("local_offermanager_get_offer_courses",{offerid:parseInt(i,10)});let u=[];return a&&Array.isArray(a.data)?u=a.data:a&&a.data&&Array.isArray(a.data.courses)?u=a.data.courses:Array.isArray(a)&&(u=a),u.filter(m=>(m.id||m.offercourseid)!=o).map(m=>({id:m.id,name:m.fullname||m.name,courseid:m.courseid||null,offercourseid:m.id,categoryid:m.categoryid||null,category_name:m.category_name||""}))}catch{throw new Error(t.message||"Erro ao buscar cursos para duplicação")}}}async function pu(e){try{const t=await Fe("local_offermanager_get_course_roles",{offercourseid:e});if(t.error)throw new Error(error.message||"Erro ao buscar papéis do curso");return t}catch(t){throw new Error(t.message||"Erro ao buscar papéis do curso")}}async function np(e=!0){try{return await Fe("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw new Error(t.message||"Erro ao buscar métodos de inscrição")}}async function h0(e,t){try{return await Fe("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw new Error(s.message||"Erro ao alterar status da turma")}}const KV="",p0={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},m0={class:"table-responsive"},g0={class:"table"},_0=["data-value"],v0=["onClick"],y0=["data-column"];function b0(e,t,s,i,o,a){return O(),N("div",m0,[f("table",g0,[f("thead",null,[f("tr",null,[(O(!0),N(Re,null,vt(s.headers,u=>(O(),N("th",{key:u.value,class:he({"text-right":u.align==="right"}),style:ls(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Vt(e.$slots,"header-select",{key:0},()=>[nt(Y(u.text),1)],!0):(O(),N(Re,{key:1},[nt(Y(u.text)+" ",1),u.sortable?(O(),N("span",{key:0,onClick:c=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[f("i",{class:he(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,v0)):ae("",!0)],64))],14,_0))),128))])]),f("tbody",null,[(O(!0),N(Re,null,vt(s.items,u=>(O(),N("tr",{key:u.id},[(O(!0),N(Re,null,vt(s.headers,c=>(O(),N("td",{key:c.value,class:he({"text-right":c.align==="right"}),"data-column":c.value},[Vt(e.$slots,"item-"+c.value,{item:u},()=>[nt(Y(u[c.value]),1)],!0)],10,y0))),128))]))),128))])])])}const hn=He(p0,[["render",b0],["__scopeId","data-v-35ce6ca5"]]),YV="",w0={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},E0={class:"select-wrapper"},C0=["value","disabled"],D0=["value"],x0={key:1,class:"error-message"};function S0(e,t,s,i,o,a){return O(),N("div",{ref:"selectContainer",class:"custom-select-container",style:ls(a.customWidth)},[s.label?(O(),N("div",{key:0,class:he(["select-label",{disabled:s.disabled}])},Y(s.label),3)):ae("",!0),f("div",E0,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:he(["custom-select",{error:s.hasError}]),disabled:s.disabled},[(O(!0),N(Re,null,vt(s.options,u=>(O(),N("option",{key:u.value,value:u.value},Y(u.label),9,D0))),128))],42,C0),f("div",{class:he(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(O(),N("div",x0,Y(s.errorMessage),1)):ae("",!0)],4)}const mr=He(w0,[["render",S0],["__scopeId","data-v-c65f2fc1"]]),QV="",O0={name:"CustomInput",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},T0={key:0,class:"input-label"},N0=["type","placeholder","value","disabled","min","max"],A0={key:0,class:"search-icon"},I0={key:2,class:"error-message"};function M0(e,t,s,i,o,a){return O(),N("div",{class:"custom-input-container",style:ls(a.customWidth)},[s.label?(O(),N("div",T0,Y(s.label),1)):ae("",!0),f("div",{class:he(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[f("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...u)=>a.handleInput&&a.handleInput(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),disabled:s.disabled,class:he(["form-control custom-input",{error:s.hasError}]),min:a.isNumberType?0:null,max:s.max},null,42,N0),s.hasSearchIcon?(O(),N("div",A0,t[2]||(t[2]=[f("i",{class:"fas fa-search"},null,-1)]))):ae("",!0),a.isDateType?(O(),N("div",{key:1,class:he(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[f("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):ae("",!0),s.hasError&&s.errorMessage?(O(),N("div",I0,Y(s.errorMessage),1)):ae("",!0)],2)],4)}const Uo=He(O0,[["render",M0],["__scopeId","data-v-ee21b46c"]]),ZV="",P0={name:"CustomCheckbox",props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"]},k0=["id","checked","disabled"],V0=["for"];function R0(e,t,s,i,o,a){return O(),N("div",{class:he(["checkbox-container",{disabled:s.disabled}])},[f("input",{type:"checkbox",id:s.id,checked:s.modelValue,onChange:t[0]||(t[0]=u=>e.$emit("update:modelValue",u.target.checked)),class:"custom-checkbox",disabled:s.disabled},null,40,k0),f("label",{for:s.id,class:he(["checkbox-label",{disabled:s.disabled}])},[Vt(e.$slots,"default",{},()=>[nt(Y(s.label),1)],!0)],10,V0)],2)}const ea=He(P0,[["render",R0],["__scopeId","data-v-bb633156"]]),JV="",L0={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["click"]},U0=["disabled"],F0={key:1};function B0(e,t,s,i,o,a){return O(),N("button",{class:he(["custom-button",[`btn-${s.variant}`]]),disabled:s.disabled,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(O(),N("i",{key:0,class:he(s.icon)},null,2)):ae("",!0),s.label?(O(),N("span",F0,Y(s.label),1)):ae("",!0),Vt(e.$slots,"default",{},void 0,!0)],10,U0)}const Fn=He(L0,[["render",B0],["__scopeId","data-v-9dc7585a"]]),XV="",$0={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},j0={class:"filter-section"},H0={key:0},q0={class:"filter-content"},z0={key:1,class:"filter-tags"};function W0(e,t,s,i,o,a){return O(),N("div",j0,[s.title?(O(),N("h2",H0,Y(s.title),1)):ae("",!0),f("div",q0,[Vt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(O(),N("div",z0,[Vt(e.$slots,"tags",{},void 0,!0)])):ae("",!0)])}const op=He($0,[["render",W0],["__scopeId","data-v-0a9c42cf"]]),eR="",G0={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function K0(e,t,s,i,o,a){return O(),N("div",{class:he(["filter-row",{"filter-row-inline":s.inline}])},[Vt(e.$slots,"default",{},void 0,!0)],2)}const ta=He(G0,[["render",K0],["__scopeId","data-v-6725a4ba"]]),tR="",Y0={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},Q0={key:0,class:"filter-label"},Z0={class:"filter-input"};function J0(e,t,s,i,o,a){return O(),N("div",{class:he(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(O(),N("div",Q0,Y(s.label),1)):ae("",!0),f("div",Z0,[Vt(e.$slots,"default",{},void 0,!0)])],2)}const sa=He(Y0,[["render",J0],["__scopeId","data-v-f69fad7e"]]),sR="",X0={name:"FilterActions"},ew={class:"filter-actions"};function tw(e,t,s,i,o,a){return O(),N("div",ew,[Vt(e.$slots,"default",{},void 0,!0)])}const ip=He(X0,[["render",tw],["__scopeId","data-v-b9facd34"]]),rR="",sw={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},rw={key:0};function nw(e,t,s,i,o,a){return O(),Rt(Vf,null,{default:Ne(()=>[s.isLoading?(O(),N("div",rw,t[0]||(t[0]=[f("div",{class:"modal-overlay"},null,-1),f("div",{class:"loader-wrapper"},[f("span",{class:"loader",role:"status"},[f("span",{class:"sr-only"},"Carregando...")])],-1)]))):ae("",!0)]),_:1})}const mu=He(sw,[["render",nw],["__scopeId","data-v-a4a23ca1"]]),nR="",ow={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},iw={class:"toast-content"};function aw(e,t,s,i,o,a){return O(),Rt(Z_,{to:"body"},[P(Vf,{name:"toast"},{default:Ne(()=>[s.show?(O(),N("div",{key:0,class:he(["toast",s.type])},[f("div",iw,[f("i",{class:he(a.icon)},null,2),f("span",null,Y(s.message),1)]),f("div",{class:"toast-progress",style:ls(a.progressStyle)},null,4)],2)):ae("",!0)]),_:1})])}const Fo=He(ow,[["render",aw],["__scopeId","data-v-4e0ca8ca"]]),oR="",lw={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,20,50]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const o=[];for(let a=s;a<=i;a++)o.push(a);return o},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},uw={class:"pagination-container mt-3"},cw={class:"pagination-info"},dw=["value"],fw={class:"pagination-text"},hw={class:"pagination-controls"},pw=["disabled"],mw=["onClick"],gw=["disabled"];function _w(e,t,s,i,o,a){return O(),N("div",uw,[f("div",cw,[mt(f("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(O(!0),N(Re,null,vt(s.perPageOptions,u=>(O(),N("option",{key:u,value:u},Y(u),9,dw))),128))],544),[[Yl,a.perPageModel]]),f("span",fw," Mostrando de "+Y(a.from)+" até "+Y(a.to)+" de "+Y(s.total)+" resultados ",1)]),f("div",hw,[f("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[f("i",{class:"fas fa-chevron-left"},null,-1)]),8,pw),(O(!0),N(Re,null,vt(a.visiblePages,u=>(O(),N("button",{key:u,class:he(["page-item",{active:u===s.currentPage}]),onClick:c=>a.handlePageChange(u)},Y(u),11,mw))),128)),f("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[f("i",{class:"fas fa-chevron-right"},null,-1)]),8,gw)])])}const pn=He(lw,[["render",_w],["__scopeId","data-v-cd2746ef"]]),iR="",vw={name:"PageHeader",props:{title:{type:String,required:!0}}},yw={class:"page-header"},bw={class:"header-actions"};function ww(e,t,s,i,o,a){return O(),N("div",yw,[f("h2",null,Y(s.title),1),f("div",bw,[Vt(e.$slots,"actions",{},void 0,!0)])])}const ra=He(vw,[["render",ww],["__scopeId","data-v-5266bf48"]]),aR="",Ew={name:"Modal",components:{CustomButton:Fn},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},Cw={class:"modal-body"},Dw={key:0,class:"modal-footer"},xw={key:1,class:"modal-footer"};function Sw(e,t,s,i,o,a){const u=ee("custom-button");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=c=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:he(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=Lt(()=>{},["stop"]))},[f("div",Cw,[Vt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(O(),N("div",Dw,[Vt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(O(),N("div",xw,[P(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=c=>e.$emit("close"))},null,8,["label"]),P(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=c=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):ae("",!0)],2)])):ae("",!0)}const Ow=He(Ew,[["render",Sw],["__scopeId","data-v-87998e77"]]),lR="",Tw={name:"ConfirmationModal",components:{Modal:Ow},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},Nw={key:0,class:"icon-container"},Aw={class:"modal-custom-title"},Iw={key:1,class:"message-list"},Mw={key:0,class:"list-title"},Pw={key:2,class:"message"},kw={class:"modal-custom-footer"},Vw=["disabled"];function Rw(e,t,s,i,o,a){const u=ee("modal");return O(),Rt(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:"sm","show-default-footer":!1,onClose:t[2]||(t[2]=c=>e.$emit("close")),onConfirm:t[3]||(t[3]=c=>e.$emit("confirm"))},{default:Ne(()=>[f("div",{class:he(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(O(),N("div",Nw,[f("i",{class:he(a.iconClass)},null,2)])):ae("",!0),f("h3",Aw,Y(s.title),1),a.hasListContent?(O(),N("div",Iw,[s.listTitle?(O(),N("p",Mw,Y(s.listTitle),1)):ae("",!0),f("ul",null,[(O(!0),N(Re,null,vt(s.listItems,(c,h)=>(O(),N("li",{key:h},Y(c),1))),128))])])):(O(),N("div",Pw,Y(s.message),1)),f("div",kw,[f("button",{class:"btn-cancel",onClick:t[0]||(t[0]=c=>e.$emit("close"))},Y(s.cancelButtonText),1),f("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=c=>e.$emit("confirm"))},Y(s.confirmButtonText),9,Vw)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled"])}const gu=He(Tw,[["render",Rw],["__scopeId","data-v-3be135e0"]]),uR="",cR="",Lw={name:"OfferManagerView",components:{CustomTable:hn,CustomSelect:mr,CustomInput:Uo,CustomCheckbox:ea,CustomButton:Fn,FilterSection:op,FilterRow:ta,FilterGroup:sa,FilterActions:ip,Pagination:pn,PageHeader:ra,ConfirmationModal:gu,LFLoading:mu,Toast:Fo},setup(){return{router:Zi()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{icons:{edit:Y1},inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],typeOptionsEnabled:!1,tableHeaders:[{text:"NOME DA OFERTA",value:"name",sortable:!0},{text:"DESCRIÇÃO",value:"description",sortable:!0},{text:"STATUS DA OFERTA",value:"status",sortable:!0},{text:"TIPO DA OFERTA",value:"type",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=Zh.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){const e=await fu();e.data.types&&(this.typeOptionsEnabled=e.data.enabled,e.data.default&&(this.inputFilters.type=e.data.default),this.typeOptions=e.data.types.map(t=>({value:t,label:t})))},async loadOffers(){this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await Q1(e);if(t.error)throw new Error(t.message||"Erro ao carregar ofertas");this.offers=t.data.offers||[],this.totalOffers=t.data.total_items||0,this.loading=!1},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.router.push({name:"nova-oferta"})},editOffer(e){this.router.push({name:"editar-oferta",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await Z1(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await s0(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},Uw={id:"offer-manager-component",class:"offer-manager"},Fw={class:"new-offer-container"},Bw={key:0,class:"alert alert-danger"},$w={class:"table-container"},jw=["title"],Hw={class:"action-buttons"},qw=["onClick"],zw=["onClick","disabled","title"],Ww={key:0,class:"fas fa-eye"},Gw={key:1,class:"fas fa-eye-slash"},Kw=["onClick","disabled","title"];function Yw(e,t,s,i,o,a){var ye,Z,fe,ve,Ae,ie;const u=ee("CustomButton"),c=ee("PageHeader"),h=ee("CustomInput"),m=ee("FilterGroup"),p=ee("CustomSelect"),_=ee("CustomCheckbox"),w=ee("FilterActions"),D=ee("FilterRow"),k=ee("FilterSection"),F=ee("CustomTable"),te=ee("Pagination"),A=ee("ConfirmationModal"),se=ee("LFLoading"),G=ee("Toast");return O(),N("div",Uw,[P(c,{title:"Gerenciar Ofertas"},{actions:Ne(()=>[f("div",Fw,[P(u,{variant:"primary",label:"Nova Oferta",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),P(k,{title:"FILTRO"},{default:Ne(()=>[P(D,{inline:!0},{default:Ne(()=>[P(m,{label:"Oferta"},{default:Ne(()=>[P(h,{modelValue:o.inputFilters.search,"onUpdate:modelValue":t[0]||(t[0]=V=>o.inputFilters.search=V),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),o.typeOptionsEnabled?(O(),Rt(m,{key:0,label:"Tipo"},{default:Ne(()=>[P(p,{modelValue:o.inputFilters.type,"onUpdate:modelValue":[t[1]||(t[1]=V=>o.inputFilters.type=V),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})):ae("",!0),P(m,{"is-checkbox":!0},{default:Ne(()=>[P(_,{modelValue:o.inputFilters.hideInactive,"onUpdate:modelValue":[t[2]||(t[2]=V=>o.inputFilters.hideInactive=V),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),P(w,null,{default:Ne(()=>[P(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),o.error?(O(),N("div",Bw,[t[7]||(t[7]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),nt(" "+Y(o.error),1)])):ae("",!0),f("div",$w,[P(F,{headers:o.tableHeaders,items:o.offers,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort},{"item-description":Ne(({item:V})=>[f("span",{title:V.description},Y(V.description.length>50?V.description.slice(0,50)+"...":V.description),9,jw)]),"item-type":Ne(({item:V})=>[nt(Y(V.type.charAt(0).toUpperCase()+V.type.slice(1)),1)]),"item-status":Ne(({item:V})=>[nt(Y(V.status===1?"Ativa":"Inativa"),1)]),"item-actions":Ne(({item:V})=>[f("div",Hw,[f("button",{class:"btn-action btn-edit",onClick:Te=>a.editOffer(V),title:"Editar"},t[8]||(t[8]=[f("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("g",{"clip-path":"url(#clip0_9_197955)"},[f("path",{d:"M12.854 0.145905C12.7602 0.0521694 12.6331 -0.000488281 12.5005 -0.000488281C12.3679 -0.000488281 12.2408 0.0521694 12.147 0.145905L10.5 1.7929L14.207 5.49991L15.854 3.8539C15.9006 3.80746 15.9375 3.75228 15.9627 3.69154C15.9879 3.63079 16.0009 3.56567 16.0009 3.4999C16.0009 3.43414 15.9879 3.36902 15.9627 3.30827C15.9375 3.24753 15.9006 3.19235 15.854 3.1459L12.854 0.145905ZM13.5 6.2069L9.793 2.4999L3.293 8.9999H3.5C3.63261 8.9999 3.75978 9.05258 3.85355 9.14635C3.94732 9.24012 4 9.3673 4 9.4999V9.9999H4.5C4.63261 9.9999 4.75978 10.0526 4.85355 10.1464C4.94732 10.2401 5 10.3673 5 10.4999V10.9999H5.5C5.63261 10.9999 5.75978 11.0526 5.85355 11.1464C5.94732 11.2401 6 11.3673 6 11.4999V11.9999H6.5C6.63261 11.9999 6.75978 12.0526 6.85355 12.1464C6.94732 12.2401 7 12.3673 7 12.4999V12.7069L13.5 6.2069ZM6.032 13.6749C6.01095 13.619 6.00012 13.5597 6 13.4999V12.9999H5.5C5.36739 12.9999 5.24021 12.9472 5.14644 12.8535C5.05268 12.7597 5 12.6325 5 12.4999V11.9999H4.5C4.36739 11.9999 4.24021 11.9472 4.14644 11.8535C4.05268 11.7597 4 11.6325 4 11.4999V10.9999H3.5C3.36739 10.9999 3.24021 10.9472 3.14644 10.8535C3.05268 10.7597 3 10.6325 3 10.4999V9.9999H2.5C2.44022 9.99981 2.38094 9.98897 2.325 9.96791L2.146 10.1459C2.09835 10.1939 2.06093 10.251 2.036 10.3139L0.0359968 15.3139C-0.000373859 15.4048 -0.00927736 15.5043 0.0103901 15.6002C0.0300575 15.6961 0.077431 15.7841 0.146638 15.8533C0.215844 15.9225 0.30384 15.9698 0.399716 15.9895C0.495593 16.0092 0.595133 16.0003 0.685997 15.9639L5.686 13.9639C5.74886 13.939 5.80601 13.9016 5.854 13.8539L6.032 13.6759V13.6749Z",fill:"var(--primary)"})]),f("defs",null,[f("clipPath",{id:"clip0_9_197955"},[f("rect",{width:"16",height:"16",fill:"white"})])])],-1)]),8,qw),f("button",{class:he(["btn-action",V.status===1?"btn-deactivate":"btn-activate"]),onClick:Te=>a.toggleOfferStatus(V),disabled:V.status===0&&!V.can_activate,title:a.getStatusButtonTitle(V)},[V.status===1?(O(),N("i",Ww)):(O(),N("i",Gw))],10,zw),f("button",{class:"btn-action btn-delete",onClick:Te=>a.deleteOffer(V),disabled:!V.can_delete,title:V.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[9]||(t[9]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,Kw)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),P(te,{"current-page":o.currentPage,"onUpdate:currentPage":t[3]||(t[3]=V=>o.currentPage=V),"per-page":o.perPage,"onUpdate:perPage":t[4]||(t[4]=V=>o.perPage=V),total:o.totalOffers,loading:o.loading},null,8,["current-page","per-page","total","loading"]),P(A,{show:o.showDeleteModal,title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=V=>o.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),P(A,{show:o.showStatusModal,title:((ye=o.selectedOffer)==null?void 0:ye.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((Z=o.selectedOffer)==null?void 0:Z.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((fe=o.selectedOffer)==null?void 0:fe.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((ve=o.selectedOffer)==null?void 0:ve.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((Ae=o.selectedOffer)==null?void 0:Ae.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((ie=o.selectedOffer)==null?void 0:ie.status)===1?"warning":"question",onClose:t[6]||(t[6]=V=>o.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),P(se,{"is-loading":o.loading},null,8,["is-loading"]),P(G,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])])}const Qw=He(Lw,[["render",Yw],["__scopeId","data-v-78bb8b76"]]);async function Zw(e={}){try{const t=await Fe("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"});if(t.error)throw new Error(error.message||"Erro ao buscar matrículas");return t}catch(t){throw new Error(t.message||"Erro ao buscar matrículas")}}async function Jw(e={}){try{const t=await Fe("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5});return t.error,t}catch(t){return{error:!0,data:[],message:t.message||"Erro ao matricular usuários"}}}async function Xw(e,t="",s){const i=await Fe("local_offermanager_get_potential_users_to_enrol",{offerclassid:e,search_string:t,excluded_userids:s});return i.error?(console.error("Erro na resposta de getPotentialUsersToEnrol:",i.error),[]):i}async function eE(e={}){try{const t=await Fe("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid});return t==null?(console.error("Resposta vazia de editEnrolment"),!1):typeof t=="boolean"?t:t&&typeof t.success=="boolean"?t.success:t&&t.error===!1?t.data===!0:(console.warn("Formato de resposta não reconhecido:",t),!1)}catch(t){return console.error("Erro ao editar matrícula:",t),!1}}async function tE(e={}){try{const t=await Fe("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend});return t&&t.error===!1&&t.data===!0?e.offeruserenrolids.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.error("Formato de resposta não reconhecido:",t),[])}catch(t){throw console.error("Erro ao editar matrículas em lote:",t),t}}async function sE(e){try{const t=await Fe("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e});return t&&t.error===!1&&t.data===!0?e.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.warn("Formato de resposta não reconhecido:",t),[])}catch(t){return console.error("Erro ao excluir matrículas em lote:",t),[]}}async function rE(e){try{const t=await Fe("local_offermanager_get_roles",{offeruserenrolid:e});return t?t.error?(console.error("Erro na resposta de getUserRoles:",t.error),[]):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:[]:(console.error("Resposta vazia de getUserRoles"),[])}catch(t){return console.error("Erro ao buscar papéis do usuário:",t),[]}}async function nE(e,t){try{const s=await Fe("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]});return s==null?(console.error("Resposta vazia de updateUserRoles"),!1):typeof s=="boolean"?s:s&&typeof s.success=="boolean"?s.success:s&&s.error===!1?s.data===!0:(console.warn("Formato de resposta não reconhecido:",s),!1)}catch(s){return console.error("Erro ao atualizar papéis do usuário:",s),!1}}const dR="",oE={name:"HierarchicalSelect",props:{modelValue:{type:String,default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},iE={class:"select-wrapper"},aE=["value","disabled"],lE=["label"],uE=["value"],cE={key:1,class:"error-message"};function dE(e,t,s,i,o,a){return O(),N("div",{ref:"selectContainer",class:"hierarchical-select-container",style:ls(a.customWidth)},[s.label?(O(),N("div",{key:0,class:he(["select-label",{disabled:s.disabled}])},Y(s.label),3)):ae("",!0),f("div",iE,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:he(["hierarchical-select",{error:s.hasError}]),disabled:s.disabled},[(O(!0),N(Re,null,vt(s.options,u=>(O(),N("optgroup",{key:u.value,label:u.label},[(O(!0),N(Re,null,vt(u.children,c=>(O(),N("option",{key:c.value,value:c.value,class:"child-option"},Y(c.label),9,uE))),128))],8,lE))),128))],42,aE),f("div",{class:he(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(O(),N("div",cE,Y(s.errorMessage),1)):ae("",!0)],4)}const fE=He(oE,[["render",dE],["__scopeId","data-v-ca8af705"]]),fR="",hE={name:"FilterTag",emits:["remove"]};function pE(e,t,s,i,o,a){return O(),N("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=u=>e.$emit("remove"))},[t[1]||(t[1]=f("i",{class:"fas fa-times"},null,-1)),Vt(e.$slots,"default",{},void 0,!0)])}const Bo=He(hE,[["render",pE],["__scopeId","data-v-cf6f2168"]]),hR="",mE={name:"FilterTags"},gE={class:"filter-tags"};function _E(e,t,s,i,o,a){return O(),N("div",gE,[Vt(e.$slots,"default",{},void 0,!0)])}const na=He(mE,[["render",_E],["__scopeId","data-v-746bf68d"]]),pR="",vE={name:"Autocomplete",components:{FilterTag:Bo,FilterTags:na},props:{modelValue:{type:[Array,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){if(!this.modelValue)return"";const e=this.internalItems.find(t=>t.value===this.modelValue);return e?e.label:""}},created(){this.debouncedSearch=Zh.debounce(e=>{this.$emit("search",e)},300)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){Array.isArray(this.modelValue)&&this.$emit("select-all"),this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e.value),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},yE={class:"autocomplete-container"},bE=["id"],wE={class:"autocomplete-wrapper"},EE=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls"],CE={key:0,class:"selected-item"},DE=["title"],xE=["id"],SE=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],OE={class:"item-label"},TE={key:0,class:"fas fa-check"},NE={key:0,class:"dropdown-item loading-item"},AE={key:1,class:"dropdown-item no-results"},IE={key:0,class:"tags-container"};function ME(e,t,s,i,o,a){const u=ee("FilterTag"),c=ee("FilterTags");return O(),N("div",yE,[s.label?(O(),N("label",{key:0,class:he(["filter-label",{required:s.required}]),id:`${o.uniqueId}-label`},Y(s.label),11,bE)):ae("",!0),f("div",wE,[f("div",{class:"input-container",style:ls({maxWidth:a.inputMaxWidthStyle})},[f("div",{class:he(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!o.isOpen&&!o.searchQuery}])},[mt(f("input",{type:"text",class:"form-control",placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=h=>o.searchQuery=h),disabled:s.disabled,"aria-expanded":o.isOpen,"aria-owns":`${o.uniqueId}-listbox`,"aria-labelledby":s.label?`${o.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${o.uniqueId}-listbox`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...h)=>a.handleKeydown&&a.handleKeydown(...h)),onFocus:t[2]||(t[2]=h=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...h)=>a.handleInput&&a.handleInput(...h)),onClick:t[4]||(t[4]=h=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...h)=>a.handleBlur&&a.handleBlur(...h)),ref:"inputElement"},null,40,EE),[[ws,o.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!o.isOpen&&!o.searchQuery?(O(),N("div",CE,[f("span",{class:"selected-text",title:a.getSelectedItemLabel},Y(a.truncateLabel(a.getSelectedItemLabel)),9,DE),f("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=Lt((...h)=>a.removeSelectedItem&&a.removeSelectedItem(...h),["stop"]))})])):ae("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!o.isOpen&&!o.searchQuery)?(O(),N("i",{key:1,class:he(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):ae("",!0)],2),o.isOpen?(O(),N("div",{key:0,class:"dropdown-menu show",id:`${o.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...h)=>a.handleScroll&&a.handleScroll(...h))},[a.displayItems.length>0?(O(),N(Re,{key:0},[(O(!0),N(Re,null,vt(a.displayItems,(h,m)=>(O(),N("div",{key:h.value==="__ALL__"?"__ALL__":h.value,class:he(["dropdown-item",{active:o.selectedIndex===m,selected:h.value!=="__ALL__"&&(Array.isArray(s.modelValue)?s.modelValue.some(p=>p.value===h.value):s.modelValue===h.value)}]),id:`${o.uniqueId}-option-${m}`,role:"option","data-index":m,"aria-selected":o.selectedIndex===m,tabindex:o.selectedIndex===m?0:-1,onClick:p=>a.selectItem(h),onKeydown:p=>a.handleOptionKeydown(p,h,m),ref_for:!0,ref:"optionElements",title:h.label},[f("span",OE,Y(a.truncateLabel(h.label)),1),h.value!=="__ALL__"&&Array.isArray(s.modelValue)&&s.modelValue.some(p=>p.value===h.value)?(O(),N("i",TE)):ae("",!0)],42,SE))),128)),s.loading?(O(),N("div",NE,t[8]||(t[8]=[f("span",null,"Carregando mais itens...",-1)]))):ae("",!0)],64)):(O(),N("div",AE,Y(s.noResultsText||"Nenhum item disponível"),1))],40,xE)):ae("",!0)],4),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(O(),N("div",IE,[P(c,null,{default:Ne(()=>[(O(!0),N(Re,null,vt(s.modelValue,h=>(O(),Rt(u,{key:h.value,onRemove:m=>a.removeItem(h)},{default:Ne(()=>[nt(Y(h.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1})])):ae("",!0)])])}const $o=He(vE,[["render",ME],["__scopeId","data-v-e7ee87a5"]]),mR="",PE={name:"EnrolmentModalNew",components:{Toast:Fo,CustomSelect:mr},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Matricular usuários na turma"},size:{type:String,default:"lg",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Salvar"},cancelButtonText:{type:String,default:"Cancelar"},offerclassid:{type:[Number,String],required:!0},roles:{type:Array,required:!0}},emits:["close","success"],data(){return{enrolmentMethod:"manual",enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedRoleId:"",searchQuery:"",isOpen:!1,userOptions:[],selectedUsers:[],debounceTimer:null,selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],loadingUsers:!1,isSubmitting:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null}},computed:{isFormValid(){return this.roles.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1}},watch:{show(e){document.body.style.overflow=e?"hidden":"",e&&this.initializeForm()}},mounted(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("click",this.handleClickOutside),this.show&&(document.body.style.overflow="hidden",this.initializeForm())},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("click",this.handleClickOutside),document.body.style.overflow=""},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")},handleClickOutside(e){if(this.show===!1)return;const t=document.querySelector(".custom-autocomplete-wrapper");t&&!t.contains(e.target)&&(this.isOpen=!1)},async initializeForm(){this.resetForm()},resetForm(){let e=this.roles.find(t=>t.value==5);this.enrolmentMethod="manual",this.selectedRoleId=e.value,this.searchQuery="",this.selectedUsers=[],this.selectedFile=null,this.csvUsers=[],this.csvDelimiter=",",this.csvEncoding="UTF-8"},async fetchPotentialUsersToEnrol(e){this.loadingUsers=!0;let t=this.selectedUsers.map(i=>i.value);const s=await Xw(this.offerclassid,e,t);this.userOptions=s.data.map(i=>({value:i.id,label:i.fullname})),this.loadingUsers=!1},handleInput(){const e=this.searchQuery.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialUsersToEnrol(e),this.userOptions&&(this.isOpen=!0)},500):(this.isOpen=!1,this.userOptions=[])},selectUser(e){const t=this.selectedUsers.findIndex(s=>s.value===e.value);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1),this.searchQuery="",this.isOpen=!1},removeUser(e){this.selectedUsers=this.selectedUsers.filter(t=>t.value!==e.value)},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=o=>{const a=o.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),o=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(m,p)=>{if(p==="\\t")return m.split("	");if(p===" ")return m.split(/\s+/);{const _=p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return m.split(new RegExp(_))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(m=>m.includes("userid"))||!u.some(m=>m.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const c=u.findIndex(m=>m.includes("userid")),h=u.findIndex(m=>m.includes("firstname"));for(let m=1;m<i.length;m++){const p=i[m].trim();if(!p)continue;const _=a(p,t);if(_.length>Math.max(c,h)){const w=_[c].trim(),D=_[h].trim();if(w&&D){if(!/^\d+$/.test(w)){console.warn(`Linha ${m+1}: ID inválido '${w}'. Deve ser um número.`);continue}o.push({id:w,name:D})}}}if(o.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=o}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},async handleSubmit(){if(this.isFormValid)try{this.isSubmitting=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(a=>a.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(a=>parseInt(a.id)));const t=await Jw({offerclassid:parseInt(this.offerclassid),userids:e,roleid:parseInt(this.selectedRoleId)});let s=[],i=!1;if(Array.isArray(t))s=t;else if(t&&Array.isArray(t.data))s=t.data;else if(Array.isArray(t)&&t.length>0&&t[0].data)s=t[0].data;else if(Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data))s=t[0].data;else{this.showErrorMessage("Erro ao processar a resposta do servidor. Verifique o console para mais detalhes.");return}const o=s.filter(a=>a.success).length;if(i=o>0,i)this.showSuccessMessage(`${o} de ${e.length} usuário(s) matriculado(s) com sucesso.`),setTimeout(()=>{this.$emit("success",{count:o,total:e.length}),this.$emit("close")},1500);else{console.error("Nenhum usuário foi matriculado com sucesso");let a="Não foi possível completar a matrícula. Por favor, tente novamente ou entre em contato com o suporte técnico para assistência.";s.length>0&&s[0].message&&(s[0].message.includes("já está matriculado")||s[0].message.includes("already enrolled")?a=s[0].message:s[0].message.includes("Error enrolling user")||s[0].message.includes("[[message:")||s[0].message.includes("enrolment_failed")?a="Não foi possível completar a matrícula. Por favor, tente novamente ou entre em contato com o suporte técnico para assistência.":a=s[0].message),this.showErrorMessage(a)}}catch(e){console.error("Erro ao matricular usuários:",e),this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.isSubmitting=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},kE={class:"modal-header"},VE={class:"modal-title"},RE={class:"modal-body"},LE={class:"enrolment-modal"},UE={class:"form-row"},FE={class:"form-group"},BE={class:"limited-width-input"},$E={class:"form-group"},jE={class:"limited-width-input"},HE={key:0,class:"error-message"},qE={key:0,class:"form-group"},zE={class:"user-select-container"},WE={class:"custom-autocomplete-wrapper"},GE={key:0,class:"dropdown-menu show"},KE=["onClick"],YE={key:0,class:"fas fa-check"},QE={key:0,class:"selected-users-container"},ZE={class:"filter-tags"},JE=["onClick"],XE={key:1,class:"form-group"},eC={class:"file-name"},tC={class:"file-size"},sC={key:0,class:"csv-users-preview"},rC={class:"preview-header"},nC={class:"selected-users-container"},oC={class:"filter-tags"},iC={key:0,class:"more-users"},aC={class:"csv-info"},lC={class:"csv-example"},uC=["href"],cC={class:"csv-options-row"},dC={class:"csv-option"},fC={class:"csv-option"},hC={class:"modal-footer"},pC=["disabled"];function mC(e,t,s,i,o,a){const u=ee("CustomSelect"),c=ee("Toast");return O(),N(Re,null,[s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:he(["modal-container",[`modal-${s.size}`]]),onClick:t[14]||(t[14]=Lt(()=>{},["stop"]))},[f("div",kE,[f("h3",VE,Y(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",RE,[f("div",LE,[t[31]||(t[31]=f("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),f("div",UE,[f("div",FE,[t[17]||(t[17]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Forma de matrícula"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",BE,[P(u,{modelValue:o.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=h=>o.enrolmentMethod=h),options:o.enrolmentMethodOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"])])]),f("div",$E,[t[18]||(t[18]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Papel para atribuir"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",jE,[P(u,{modelValue:o.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=h=>o.selectedRoleId=h),options:s.roles,class:"w-100",required:""},null,8,["modelValue","options"]),s.roles.length===0?(O(),N("div",HE," Não foi possível carregar os papéis disponíveis para esta turma. ")):ae("",!0)])])]),o.enrolmentMethod==="manual"?(O(),N("div",qE,[t[21]||(t[21]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Selecionar usuários"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",zE,[f("div",WE,[mt(f("input",{type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[3]||(t[3]=h=>o.searchQuery=h),onInput:t[4]||(t[4]=(...h)=>a.handleInput&&a.handleInput(...h))},null,544),[[ws,o.searchQuery]]),t[19]||(t[19]=f("div",{class:"select-arrow"},null,-1)),o.isOpen?(O(),N("div",GE,[(O(!0),N(Re,null,vt(o.userOptions,(h,m)=>(O(),N("div",{key:h.value,class:"dropdown-item",onClick:p=>a.selectUser(h)},[nt(Y(h.label)+" ",1),o.selectedUsers.some(p=>p.value===h.value)?(O(),N("i",YE)):ae("",!0)],8,KE))),128))])):ae("",!0)])]),o.selectedUsers.length>0?(O(),N("div",QE,[f("div",ZE,[(O(!0),N(Re,null,vt(o.selectedUsers,h=>(O(),N("div",{key:h.value,class:"tag badge badge-primary",onClick:m=>a.removeUser(h)},[t[20]||(t[20]=f("i",{class:"fas fa-times"},null,-1)),nt(" "+Y(h.label),1)],8,JE))),128))])])):ae("",!0)])):ae("",!0),o.enrolmentMethod==="batch"?(O(),N("div",XE,[t[30]||(t[30]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Matricular usuários a partir de um arquivo CSV"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",{class:he(["csv-upload-area",{"drag-over":o.isDragging}]),onDragover:t[6]||(t[6]=Lt((...h)=>a.onDragOver&&a.onDragOver(...h),["prevent"])),onDragleave:t[7]||(t[7]=Lt((...h)=>a.onDragLeave&&a.onDragLeave(...h),["prevent"])),onDrop:t[8]||(t[8]=Lt((...h)=>a.onDrop&&a.onDrop(...h),["prevent"])),onClick:t[9]||(t[9]=h=>e.$refs.fileInput.click())},[f("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[5]||(t[5]=(...h)=>a.handleFileSelect&&a.handleFileSelect(...h))},null,544),o.selectedFile?(O(),N(Re,{key:1},[t[24]||(t[24]=f("div",{class:"file-icon"},[f("i",{class:"fas fa-file-alt"})],-1)),f("p",eC,Y(o.selectedFile.name),1),f("p",tC," ("+Y(a.formatFileSize(o.selectedFile.size))+") ",1),t[25]||(t[25]=f("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(O(),N(Re,{key:0},[t[22]||(t[22]=f("div",{class:"upload-icon"},[f("i",{class:"fas fa-arrow-down"})],-1)),t[23]||(t[23]=f("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),o.csvUsers.length>0?(O(),N("div",sC,[f("div",rC,[f("span",null,"Usuários encontrados no arquivo ("+Y(o.csvUsers.length)+"):",1)]),f("div",nC,[f("div",oC,[(O(!0),N(Re,null,vt(o.csvUsers.slice(0,5),h=>(O(),N("div",{key:h.id,class:"tag badge badge-primary"},Y(h.name),1))),128)),o.csvUsers.length>5?(O(),N("span",iC,"+"+Y(o.csvUsers.length-5)+" mais",1)):ae("",!0)])])])):ae("",!0),f("div",aC,[t[29]||(t[29]=f("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),f("div",lC,[t[26]||(t[26]=f("span",{class:"example-label"},"Exemplo CSV",-1)),f("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerclassid}`,class:"example-csv"},"example.csv",8,uC)]),f("div",cC,[f("div",dC,[t[27]||(t[27]=f("label",null,"Delimitador do CSV",-1)),P(u,{modelValue:o.csvDelimiter,"onUpdate:modelValue":t[10]||(t[10]=h=>o.csvDelimiter=h),options:o.delimiterOptions,width:160},null,8,["modelValue","options"])]),f("div",fC,[t[28]||(t[28]=f("label",null,"Codificação",-1)),P(u,{modelValue:o.csvEncoding,"onUpdate:modelValue":t[11]||(t[11]=h=>o.csvEncoding=h),options:o.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):ae("",!0),t[32]||(t[32]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1))])]),f("div",hC,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...h)=>a.handleSubmit&&a.handleSubmit(...h)),disabled:o.isSubmitting||!a.isFormValid},Y(s.confirmButtonText),9,pC),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=h=>e.$emit("close"))},Y(s.cancelButtonText),1)])],2)])):ae("",!0),P(c,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])],64)}const gC=He(PE,[["render",mC],["__scopeId","data-v-2c11b891"]]),gR="",_C={name:"EnrollmentDetailsModal",props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{getEnrolmentMethod(e){if(console.log("EnrollmentDetailsModal - Método de inscrição recebido:",e),!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}}}},vC={class:"modal-header"},yC={key:0,class:"modal-body"},bC={class:"details-container"},wC={class:"detail-row"},EC={class:"detail-value"},CC={class:"detail-row"},DC={class:"detail-value"},xC={class:"detail-row"},SC={class:"detail-value"},OC={class:"detail-row"},TC={class:"detail-value"},NC={class:"detail-row"},AC={class:"detail-value"},IC={key:1,class:"modal-body no-data"},MC={class:"modal-footer"};function PC(e,t,s,i,o,a){return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=u=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[2]||(t[2]=Lt(()=>{},["stop"]))},[f("div",vC,[t[5]||(t[5]=f("h3",{class:"modal-title"},"Informações da matrícula",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=u=>e.$emit("close"))},t[4]||(t[4]=[f("i",{class:"fas fa-times"},null,-1)]))]),s.user?(O(),N("div",yC,[f("div",bC,[f("div",wC,[t[6]||(t[6]=f("div",{class:"detail-label"},"Nome completo",-1)),f("div",EC,Y(s.user.fullName),1)]),f("div",CC,[t[7]||(t[7]=f("div",{class:"detail-label"},"Curso",-1)),f("div",DC,Y(s.courseName),1)]),f("div",xC,[t[8]||(t[8]=f("div",{class:"detail-label"},"Método de inscrição",-1)),f("div",SC,Y(a.getEnrolmentMethod(s.user.enrol)),1)]),f("div",OC,[t[9]||(t[9]=f("div",{class:"detail-label"},"Estado",-1)),f("div",TC,[f("span",{class:he(["status-tag",s.user.status===0?"status-ativo":"status-inativo"])},Y(s.user.statusName),3)])]),f("div",NC,[t[10]||(t[10]=f("div",{class:"detail-label"},"Matrícula criada",-1)),f("div",AC,Y(s.user.createdDate),1)])])])):(O(),N("div",IC,"Nenhum dado disponível")),f("div",MC,[f("button",{class:"btn btn-secondary",onClick:t[1]||(t[1]=u=>e.$emit("close"))}," Cancelar ")])])])):ae("",!0)}const kC=He(_C,[["render",PC],["__scopeId","data-v-ffabdbe2"]]),_R="",VC={name:"EditEnrollmentModal",components:{CustomSelect:mr},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},watch:{show(e){e&&this.user&&this.initializeForm()},user(e){e&&this.show&&this.initializeForm()}},methods:{getEnrolmentMethod(e){if(!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}},initializeForm(){if(!this.user)return;this.formData.status=this.user.status;const e=this.user.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.user.timeend){const i=new Date(this.user.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.user.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),c=s.find(h=>parseInt(h.value)===u);this.formData.validityPeriod=c?c.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${s}-${i}`},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},async saveChanges(){var e;if((e=this.user)!=null&&e.offeruserenrolid)try{this.isSubmitting=!0;const t=Number(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.$emit("error","A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await eE({offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i})?(this.$emit("success",{userId:this.user.id,offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i}),this.$emit("close")):this.$emit("error","Não foi possível editar a matrícula. Por favor, tente novamente.")}catch{this.$emit("error","Ocorreu um erro ao editar a matrícula. Por favor, tente novamente.")}finally{this.isSubmitting=!1}},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,o]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,o,a,u,0,0)}}},RC={class:"modal-header"},LC={class:"modal-title"},UC={class:"modal-body"},FC={class:"enrollment-form"},BC={class:"form-row"},$C={class:"form-value"},jC={class:"form-row"},HC={class:"form-field"},qC={class:"select-wrapper"},zC={class:"form-row"},WC={class:"form-field date-time-field"},GC={class:"date-field"},KC={class:"time-field"},YC={class:"enable-checkbox"},QC={class:"form-row"},ZC={class:"form-field"},JC={class:"select-wrapper"},XC={class:"form-row"},eD={class:"date-field"},tD=["disabled"],sD={class:"time-field"},rD=["disabled"],nD={class:"enable-checkbox"},oD={class:"form-row"},iD={class:"form-value"},aD={class:"modal-footer"},lD={class:"footer-buttons"},uD=["disabled"];function cD(e,t,s,i,o,a){const u=ee("CustomSelect");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=c=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[14]||(t[14]=Lt(()=>{},["stop"]))},[f("div",RC,[f("h3",LC," Editar matrícula de "+Y(s.user?s.user.fullName:""),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=c=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",UC,[f("div",FC,[f("div",BC,[t[17]||(t[17]=f("div",{class:"form-label"},"Método de inscrição",-1)),f("div",$C,Y(a.getEnrolmentMethod(s.user&&s.user.enrol?s.user.enrol:"")),1)]),f("div",jC,[t[18]||(t[18]=f("div",{class:"form-label"},"Estado",-1)),f("div",HC,[f("div",qC,[P(u,{modelValue:o.formData.status,"onUpdate:modelValue":t[1]||(t[1]=c=>o.formData.status=c),options:o.statusOptions,width:120,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",zC,[t[20]||(t[20]=f("div",{class:"form-label"},"Matrícula começa",-1)),f("div",WC,[f("div",GC,[mt(f("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=c=>o.formData.startDateStr=c),class:"form-control",onChange:t[3]||(t[3]=(...c)=>e.handleStartDateChange&&e.handleStartDateChange(...c))},null,544),[[ws,o.formData.startDateStr]])]),f("div",KC,[mt(f("input",{type:"time","onUpdate:modelValue":t[4]||(t[4]=c=>o.formData.startTimeStr=c),class:"form-control",onChange:t[5]||(t[5]=(...c)=>e.handleStartTimeChange&&e.handleStartTimeChange(...c))},null,544),[[ws,o.formData.startTimeStr]])]),f("div",YC,[mt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[6]||(t[6]=c=>o.formData.enableStartDate=c),class:"custom-checkbox"},null,512),[[$i,o.formData.enableStartDate]]),t[19]||(t[19]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",QC,[t[21]||(t[21]=f("div",{class:"form-label"},"Período de validade da matrícula",-1)),f("div",ZC,[f("div",JC,[P(u,{modelValue:o.formData.validityPeriod,"onUpdate:modelValue":t[7]||(t[7]=c=>o.formData.validityPeriod=c),options:o.validityPeriodOptions,width:120,class:"smaller-select",onChange:a.handleValidityPeriodChange,disabled:o.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),f("div",XC,[t[23]||(t[23]=f("div",{class:"form-label"},"Matrícula termina",-1)),f("div",{class:he(["form-field date-time-field",{"disabled-inputs-only":!o.formData.enableEndDate}])},[f("div",eD,[mt(f("input",{type:"date","onUpdate:modelValue":t[8]||(t[8]=c=>o.formData.endDateStr=c),class:"form-control",disabled:!o.formData.enableEndDate},null,8,tD),[[ws,o.formData.endDateStr]])]),f("div",sD,[mt(f("input",{type:"time","onUpdate:modelValue":t[9]||(t[9]=c=>o.formData.endTimeStr=c),class:"form-control",disabled:!o.formData.enableEndDate},null,8,rD),[[ws,o.formData.endTimeStr]])]),f("div",nD,[mt(f("input",{type:"checkbox",id:"enable-enddate","onUpdate:modelValue":t[10]||(t[10]=c=>o.formData.enableEndDate=c),class:"custom-checkbox",onChange:t[11]||(t[11]=(...c)=>a.handleEnableEndDateChange&&a.handleEnableEndDateChange(...c))},null,544),[[$i,o.formData.enableEndDate]]),t[22]||(t[22]=f("label",{for:"enable-enddate"},"Habilitar",-1))])],2)]),f("div",oD,[t[24]||(t[24]=f("div",{class:"form-label"},"Matrícula criada",-1)),f("div",iD,Y(s.user&&s.user.createdDate?s.user.createdDate:"Não disponível"),1)])])]),f("div",aD,[t[25]||(t[25]=f("div",{class:"footer-spacer"},null,-1)),f("div",lD,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...c)=>a.saveChanges&&a.saveChanges(...c)),disabled:o.isSubmitting},Y(o.isSubmitting?"Salvando...":"Salvar mudanças"),9,uD),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=c=>e.$emit("close"))}," Cancelar ")])])])])):ae("",!0)}const dD=He(VC,[["render",cD],["__scopeId","data-v-f9509e2b"]]),vR="",yR="",fD={name:"BulkEditEnrollmentModal",components:{Pagination:pn,CustomTable:hn,CustomSelect:mr},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,o)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<o[this.sortBy]?-1*a:i[this.sortBy]>o[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,c]=this.formData.startDateStr.split("-").map(Number),[h,m]=this.formData.startTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);t=Math.floor(p.getTime()/1e3);const _=p.getTimezoneOffset()*60;t+=_}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,c]=this.formData.endDateStr.split("-").map(Number),[h,m]=this.formData.endTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);s=Math.floor(p.getTime()/1e3);const _=p.getTimezoneOffset()*60;s+=_}const i=this.users.filter(a=>a.offeruserenrolid).map(a=>a.offeruserenrolid);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const o=await tE({offeruserenrolids:i,status:e,timestart:t,timeend:s});if(Array.isArray(o)&&o.length>0){const a=o.filter(h=>h.operation_status).length,u=o.length-a;let c="";if(a===o.length)c=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)c=`${a} de ${o.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{c="Nenhuma matrícula pôde ser editada.",this.$emit("error",c);return}this.$emit("success",{message:c,count:a,total:o.length}),this.$emit("close")}else console.error("Resposta inválida da API:",o),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},hD={class:"modal-header"},pD={class:"modal-body"},mD={class:"enrollment-form"},gD={class:"table-container"},_D={class:"form-row"},vD={class:"form-field"},yD={class:"select-wrapper"},bD={class:"form-row"},wD={class:"form-field date-time-field"},ED={class:"date-field"},CD=["disabled"],DD={class:"time-field"},xD=["disabled"],SD={class:"enable-checkbox"},OD={class:"form-row"},TD={class:"form-field date-time-field"},ND={class:"date-field"},AD=["disabled"],ID={class:"time-field"},MD=["disabled"],PD={class:"enable-checkbox"},kD={class:"modal-footer"},VD={class:"footer-buttons"},RD=["disabled"];function LD(e,t,s,i,o,a){const u=ee("CustomTable"),c=ee("Pagination"),h=ee("CustomSelect");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[17]||(t[17]=m=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[16]||(t[16]=Lt(()=>{},["stop"]))},[f("div",hD,[t[19]||(t[19]=f("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=m=>e.$emit("close"))},t[18]||(t[18]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",pD,[f("div",mD,[f("div",null,[f("div",gD,[P(u,{headers:o.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?mt((O(),Rt(c,{key:0,"current-page":o.currentPage,"onUpdate:currentPage":t[1]||(t[1]=m=>o.currentPage=m),"per-page":o.perPage,"onUpdate:perPage":t[2]||(t[2]=m=>o.perPage=m),total:s.users.length},null,8,["current-page","per-page","total"])),[[Wl,s.users.length>o.perPage]]):ae("",!0),t[20]||(t[20]=f("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),f("div",_D,[t[21]||(t[21]=f("div",{class:"form-label"},"Alterar o status",-1)),f("div",vD,[f("div",yD,[P(h,{modelValue:o.formData.status,"onUpdate:modelValue":t[3]||(t[3]=m=>o.formData.status=m),options:o.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",bD,[t[23]||(t[23]=f("div",{class:"form-label"},"Alterar data de início",-1)),f("div",wD,[f("div",ED,[mt(f("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=m=>o.formData.startDateStr=m),class:"form-control",onChange:t[5]||(t[5]=(...m)=>a.handleStartDateChange&&a.handleStartDateChange(...m)),disabled:!o.formData.enableStartDate},null,40,CD),[[ws,o.formData.startDateStr]])]),f("div",DD,[mt(f("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=m=>o.formData.startTimeStr=m),class:"form-control",onChange:t[7]||(t[7]=(...m)=>a.handleStartTimeChange&&a.handleStartTimeChange(...m)),disabled:!o.formData.enableStartDate},null,40,xD),[[ws,o.formData.startTimeStr]])]),f("div",SD,[mt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=m=>o.formData.enableStartDate=m),class:"custom-checkbox"},null,512),[[$i,o.formData.enableStartDate]]),t[22]||(t[22]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",OD,[t[25]||(t[25]=f("div",{class:"form-label"},"Alterar data de fim",-1)),f("div",TD,[f("div",ND,[mt(f("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=m=>o.formData.endDateStr=m),class:"form-control",onChange:t[10]||(t[10]=(...m)=>a.handleEndDateChange&&a.handleEndDateChange(...m)),disabled:!o.formData.enableEndDate},null,40,AD),[[ws,o.formData.endDateStr]])]),f("div",ID,[mt(f("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=m=>o.formData.endTimeStr=m),class:"form-control",onChange:t[12]||(t[12]=(...m)=>a.handleEndTimeChange&&a.handleEndTimeChange(...m)),disabled:!o.formData.enableEndDate},null,40,MD),[[ws,o.formData.endTimeStr]])]),f("div",PD,[mt(f("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=m=>o.formData.enableEndDate=m),class:"custom-checkbox"},null,512),[[$i,o.formData.enableEndDate]]),t[24]||(t[24]=f("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),f("div",kD,[t[26]||(t[26]=f("div",{class:"footer-spacer"},null,-1)),f("div",VD,[f("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(...m)=>a.saveChanges&&a.saveChanges(...m)),disabled:o.isSubmitting},Y(o.isSubmitting?"Salvando...":"Salvar mudanças"),9,RD),f("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=m=>e.$emit("close"))}," Cancelar ")])])])])):ae("",!0)}const UD=He(fD,[["render",LD],["__scopeId","data-v-1ade848f"]]),bR="",FD={name:"BulkDeleteEnrollmentModal",components:{Pagination:pn,CustomSelect:mr,CustomTable:hn},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,o)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<o[this.sortBy]?-1*a:i[this.sortBy]>o[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},BD={class:"modal-header"},$D={class:"modal-body"},jD={class:"enrollment-form"},HD={class:"table-container"},qD={class:"modal-footer"},zD={class:"footer-buttons"},WD=["disabled"];function GD(e,t,s,i,o,a){const u=ee("CustomTable"),c=ee("Pagination");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[6]||(t[6]=h=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[5]||(t[5]=Lt(()=>{},["stop"]))},[f("div",BD,[t[8]||(t[8]=f("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",$D,[f("div",jD,[f("div",HD,[P(u,{headers:o.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?mt((O(),Rt(c,{key:0,"current-page":o.currentPage,"onUpdate:currentPage":t[1]||(t[1]=h=>o.currentPage=h),"per-page":o.perPage,"onUpdate:perPage":t[2]||(t[2]=h=>o.perPage=h),total:s.users.length},null,8,["current-page","per-page","total"])),[[Wl,s.users.length>o.perPage]]):ae("",!0)]),t[9]||(t[9]=f("div",{class:"text-center mt-5"},[f("h5",{class:"mt-1"}," Tem certeza de que deseja excluir essas inscrições de usuário? ")],-1))]),f("div",qD,[f("div",zD,[f("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=h=>e.$emit("confirm")),disabled:o.isSubmitting},Y(o.isSubmitting?"Removendo...":"Remover matrículas"),9,WD),f("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=h=>e.$emit("close"))}," Cancelar ")])])])])):ae("",!0)}const KD=He(FD,[["render",GD],["__scopeId","data-v-cd4191df"]]),wR="",YD={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{goBack(){this.$emit("click")}}};function QD(e,t,s,i,o,a){return O(),N("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.goBack&&a.goBack(...u))},[t[1]||(t[1]=f("i",{class:"fas fa-angle-left"},null,-1)),nt(" "+Y(s.label),1)])}const _u=He(YD,[["render",QD],["__scopeId","data-v-eb293b5c"]]),ER="",ZD={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},JD=["src"];function XD(e,t,s,i,o,a){return O(),N("div",{class:"user-avatar",style:ls(a.avatarStyle)},[a.hasImage?(O(),N("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,JD)):(O(),N("div",{key:1,class:"avatar-initials",style:ls({backgroundColor:a.backgroundColor})},Y(a.initials),5))],4)}const ex=He(ZD,[["render",XD],["__scopeId","data-v-0a49f249"]]),CR="",tx={name:"RoleSelector",props:{userId:{type:[Number,String],required:!0},offeruserenrolid:{type:[Number,String],required:!0},currentRole:{type:[String,Array],required:!0},offerclassid:{type:[Number,String],required:!0}},data(){return{isEditing:!1,selectedRoles:[],roles:[],loading:!1,initialLoading:!0}},computed:{displayRoleNames(){return Array.isArray(this.currentRole)?this.currentRole.join(", "):String(this.currentRole||"")}},mounted(){this.loadRoles()},methods:{async loadRoles(){var e;this.initialLoading||(this.loading=!0);try{const t=await hu(parseInt(this.offerclassid)),s=((e=t==null?void 0:t.data)==null?void 0:e.offercourseid)||t.offercourseid;if(!s)throw new Error("offercourseid não encontrado");const i=await pu(s);this.roles=Array.isArray(i==null?void 0:i.data)?i.data:Array.isArray(i)?i:[];const o=await rE(this.offeruserenrolid);if(Array.isArray(o)&&o.length)this.selectedRoles=o.map(a=>a.id);else if(Array.isArray(this.currentRole))this.selectedRoles=this.roles.filter(a=>this.currentRole.includes(a.name)).map(a=>a.id);else if(this.currentRole){const a=this.roles.find(u=>u.name.toLowerCase()===String(this.currentRole).toLowerCase());a&&(this.selectedRoles=[a.id])}}catch{this.$emit("error","Não foi possível carregar papéis.")}finally{this.loading=!1,this.initialLoading=!1}},startEditing(){this.isEditing=!0,this.$nextTick(()=>{var e;return(e=this.$refs.roleSelect)==null?void 0:e.focus()})},cancelEdit(){this.isEditing=!1},close(){this.isEditing&&(this.isEditing=!1)},async saveRoles(){if(!this.selectedRoles.length){this.$emit("error","Selecione ao menos um papel.");return}this.loading=!0;try{const e=await nE(this.offeruserenrolid,this.selectedRoles.map(t=>parseInt(t)));if(e===!0||e&&e.error===!1||e&&e.success===!0){const t=this.roles.filter(s=>this.selectedRoles.includes(s.id)).map(s=>s.name);this.$emit("success",{userId:this.userId,offeruserenrolid:this.offeruserenrolid,roleids:this.selectedRoles,roleNames:t}),this.isEditing=!1,this.$emit("reload-table")}else throw new Error("Resposta inesperada do servidor: "+JSON.stringify(e))}catch(e){console.error("Erro ao salvar papéis:",e),this.$emit("error","Não foi possível salvar papéis.")}finally{this.loading=!1}}}},sx={class:"role-selector"},rx={key:1,class:"role-edit-wrapper"},nx={class:"role-edit-container"},ox={class:"select-wrapper"},ix=["value"],ax={class:"role-actions"},lx={key:2,class:"loading-overlay"};function ux(e,t,s,i,o,a){return O(),N("div",sx,[o.isEditing?(O(),N("div",rx,[f("div",nx,[f("div",ox,[mt(f("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>o.selectedRoles=u),class:"role-select",ref:"roleSelect",multiple:"",onClick:t[2]||(t[2]=Lt(()=>{},["stop"])),style:ls({height:Math.max(4,o.roles.length)*25+"px"})},[(O(!0),N(Re,null,vt(o.roles,u=>(O(),N("option",{key:u.id,value:u.id},Y(u.name),9,ix))),128))],4),[[Yl,o.selectedRoles]])]),f("div",ax,[f("button",{class:"btn-save",onClick:t[3]||(t[3]=Lt((...u)=>a.saveRoles&&a.saveRoles(...u),["stop"])),title:"Salvar"},t[6]||(t[6]=[f("i",{class:"fas fa-check"},null,-1)])),f("button",{class:"btn-cancel",onClick:t[4]||(t[4]=Lt((...u)=>a.cancelEdit&&a.cancelEdit(...u),["stop"])),title:"Cancelar"},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))])])])):(O(),N("div",{key:0,class:"role-display",onClick:t[0]||(t[0]=Lt((...u)=>a.startEditing&&a.startEditing(...u),["stop"]))},[f("span",null,Y(a.displayRoleNames),1),t[5]||(t[5]=f("i",{class:"fas fa-pencil-alt edit-icon","aria-hidden":"true"},null,-1))])),o.loading&&o.isEditing?(O(),N("div",lx,t[8]||(t[8]=[f("div",{class:"spinner"},null,-1)]))):ae("",!0)])}const cx=He(tx,[["render",ux],["__scopeId","data-v-21b55063"]]),DR="",dx={name:"RegisteredUsers",components:{CustomTable:hn,CustomSelect:mr,HierarchicalSelect:fE,CustomInput:Uo,CustomCheckbox:ea,CustomButton:Fn,FilterSection:op,FilterRow:ta,FilterGroup:sa,FilterActions:ip,FilterTag:Bo,FilterTags:na,Pagination:pn,PageHeader:ra,ConfirmationModal:gu,Autocomplete:$o,EnrolmentModalNew:gC,EnrollmentDetailsModal:kC,Toast:Fo,EditEnrollmentModal:dD,BulkEditEnrollmentModal:UD,BulkDeleteEnrollmentModal:KD,BackButton:_u,UserAvatar:ex,RoleSelector:cx,LFLoading:mu},data(){return{offerid:null,offerclassid:null,offercourseid:null,courseid:null,courseContextId:null,inputFilters:{name:[],cpf:[],email:[]},appliedFilters:{name:[],cpf:[],email:[]},filteredUsers:[],fetchedUser:{},nameOptions:[],cpfOptions:[],emailOptions:[],tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO DA MATRÍCULA",value:"startDate",sortable:!0},{text:"DATA FIM DA MATRÍCULA",value:"endDate",sortable:!0},{text:"PRAZO DE CONCLUSÃO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progress",sortable:!1},{text:"SITUAÇÃO DE MATRÍCULA",value:"situation",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"status",sortable:!0}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,showBulkDeleteEnrollmentModal:!1,showEnrollmentModal:!1,selectedUser:null,showEnrolmentModal:!1,roleOptions:[],showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,classDetails:{},selectedUsers:[],selectedBulkAction:"",selectedPageView:"usuarios_matriculados",pageViewOptions:[{value:"matriculas",label:"Matrículas",children:[{value:"usuarios_matriculados",label:"Usuários matriculados"}]},{value:"grupos",label:"Grupos",children:[{value:"grupos",label:"Grupos"},{value:"agrupamentos",label:"Agrupamentos"},{value:"visao_geral",label:"Visão geral"}]},{value:"permissoes",label:"Permissões",children:[{value:"permissoes",label:"Permissões"},{value:"outros_usuarios",label:"Outros usuários"},{value:"verificar_permissoes",label:"Verificar permissões"}]}]}},setup(){return{router:Zi()}},async created(){var t,s,i,o;if(this.offerclassid=this.offerclassid??this.$route.params.offerclassid,!this.offerclassid)throw new Error("ID da turma não foi definido.");this.offerclassid=parseInt(this.offerclassid);const e=await hu(this.offerclassid);if(e.error)throw new Error("Erro ao requisitar informações da turma");this.classDetails=e.data,this.offerid=parseInt((t=this.classDetails)==null?void 0:t.offerid),this.offercourseid=parseInt((s=this.classDetails)==null?void 0:s.offercourseid),this.corseid=(i=this.classDetails)==null?void 0:i.courseid,this.courseContextId=(o=this.classDetails)==null?void 0:o.course_context_id,await this.loadRoles(),await this.loadRegisteredUsers()},computed:{allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.selectedUsers=[],this.loadRegisteredUsers())},currentPage(e,t){e!==t&&this.loadRegisteredUsers()},filteredUsers(e,t){e!==t&&this.loadRegisteredUsers()}},methods:{async loadRegisteredUsers(){this.loading=!0,this.error=null;let e=[];this.filteredUsers.length>0&&(e=[...new Set(this.filteredUsers.map(i=>i.id||i.value))]);const t={offerclassid:this.offerclassid,userids:e,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},s=await Zw(t);if(s.data){const i=s.data;if(Array.isArray(i.enrolments)){const o=await this.calculateDeadline();this.enrolments=i.enrolments.map(a=>({id:a.userid,offeruserenrolid:a.offeruserenrolid,fullName:a.fullname,email:a.email,cpf:a.cpf,enrol:a.enrol,roles:this.formatRoles(a.roles),groups:a.groups,timecreated:a.timecreated,createdDate:this.formatDateTime(a.timecreated),timestart:a.timestart,timeend:a.timeend,startDate:this.formatDate(a.timestart),endDate:this.formatDate(a.timeend),deadline:o==null?"Imilitado":o===1?"1 dia":`${o} dias`,progress:this.formatProgress(a.progress),situation:a.situation,situationName:a.situation_name,grade:a.grade||"-",status:a.status,statusName:a.status!==void 0?a.status===0?"Ativo":"Suspenso":"-"})),this.totalEnrolments=i.total||this.enrolments.length}}else this.enrolments=[],this.totalEnrolments=0;this.loading=!1},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},async calculateDeadline(){const e=this.classDetails.optional_fields,t=e.enrolperiod,s=e.enableenrolperiod,i=e.enableenddate,o=this.classDetails.startdate,a=e.enddate;let u;if(!i&&!s)return null;if(t===0&&s===!1)if(o&&a){const c=new Date(o),m=new Date(a)-c;u=Math.ceil(m/(1e3*60*60*24))}else u=null;else u=t;return u},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){return!e||e==="-"?"-":typeof e=="string"?e.split(",").join(", "):Array.isArray(e)&&e.length>0&&typeof e[0]=="object"&&e[0].name?e.map(t=>t.name).join(", "):Array.isArray(e)?e.join(", "):"-"},async loadNameOptions(e){const t=await searchEnrolledUsers({offerclassid:this.offerclassid,fieldstring:"name",searchstring:e||"",excludeduserids:params.excludeduserids});!t.error&&t.data?this.nameOptions=t.data.enrolments.map(s=>({value:s.id,label:s.fullname})):this.nameOptions=[]},async loadCpfOptions(e){const t=await searchEnrolledUsers({offerclassid:this.offerclassid,fieldstring:"username",searchstring:e||"",excludeduserids:params.excludeduserids});t.data?this.cpfOptions=t.data.enrolments.map(s=>({value:s.id,label:s.cpf})):this.cpfOptions=[]},async loadEmailOptions(e){const t=await searchEnrolledUsers({offerclassid:this.offerclassid,fieldstring:"email",searchstring:e||"",excludeduserids:params.excludeduserids});if(t.error){this.emailOptions=[];return}this.emailOptions=t.data.enrolments.map(s=>({value:s.id,label:s.email}))},removeFilter(e){const t=this.filteredUsers.findIndex(s=>s.id===e||s.value===e);t!==-1&&this.filteredUsers.splice(t,1)},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,await this.loadRegisteredUsers()},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",startDate:"startdate",endDate:"enddate",deadline:"enrolperiod",situation:"situation",status:"status"}[e]||"fullname"},addNewUser(){var e;if(this.classDetails&&((e=this.classDetails)==null?void 0:e.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showEnrolmentModal=!0},closeEnrolmentModal(){this.showEnrolmentModal=!1},async goBack(){this.router.push({name:"editar-oferta",params:{id:this.offerid}})},viewUserProfile(e){if(!e)return;const t=`/user/view.php?id=${e}&course=${this.courseid}`;window.location.href=t},async handlePageViewChange(e){let t=this.offerclassid,s=this.courseid,i=this.courseContextId;const o={usuarios_matriculados:`/local/offermanager/new-subscribed-users/${t}`,grupos:`/group/index.php?id=${s}`,agrupamentos:`/group/groupings.php?id=${s}`,visao_geral:`/user/index.php?id=${s}`,permissoes:`/admin/roles/permissions.php?contextid=${i}`,outros_usuarios:`/enrol/otherusers.php?id=${s}`,verificar_permissoes:`/admin/roles/check.php?contextid=${i}`};o[e]&&(window.location.href=o[e])},async handleEnrolmentSuccess(){await this.loadRegisteredUsers()},async loadRoles(){const e=await pu(this.offercourseid);if(e.error)throw new Error("Erro ao requisitar papéis do curso");this.roleOptions=e.data.map(t=>({value:t.id,label:t.name}))},showEnrollmentDetails(e){this.selectedUser={fullName:e.fullName,enrol:e.enrol||"Inscrições manuais",status:e.status||0,statusName:e.statusName||"Ativo",startDate:e.startDate||"Não disponível",createdDate:e.createdDate||e.startDate||"Não disponível"},this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedUser=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedUser=null},async handleEditEnrollmentSuccess(e){if(this.showSuccessMessage("Matrícula editada com sucesso."),e.roleid){let t=null;if(this.roleOptions&&this.roleOptions.length>0){const i=this.roleOptions.find(o=>o.value===String(e.roleid));i&&(t=i.name)}if(!t){await this.loadRegisteredUsers(),this.showEditEnrollmentModal=!1,this.selectedUser=null;return}const s=this.enrolments.findIndex(i=>i.id===e.userId);if(s!==-1){if(t&&(this.enrolments[s].roles=t),e.status!==void 0&&(this.enrolments[s].status=e.status,e.status===1?this.enrolments[s].statusName="Ativo":e.status===0&&(this.enrolments[s].statusName="Suspenso")),e.timestart){const i=new Date(e.timestart*1e3);this.enrolments[s].startDate=i.toLocaleDateString("pt-BR")}if(e.timeend){const i=new Date(e.timeend*1e3);this.enrolments[s].endDate=i.toLocaleDateString("pt-BR")}}else await this.loadRegisteredUsers()}else await this.loadRegisteredUsers();this.showEditEnrollmentModal=!1,this.selectedUser=null},handleEditEnrollmentError(e){this.showErrorMessage(e||"Não foi possível editar a matrícula. Por favor, tente novamente.")},handleRoleUpdateSuccess(e){this.showSuccessMessage("Papel atualizado com sucesso.");const t=this.enrolments.findIndex(s=>s.id===e.userId);t!==-1?this.enrolments[t].roles=e.roleName:this.reloadTable()},handleRoleUpdateError(e){this.showErrorMessage(e||"Ocorreu um erro ao atualizar o papel do usuário.")},reloadTable(){this.loadRegisteredUsers()},editUser(e){let t=null;e.roles&&e.roleid&&(t=e.roleid),this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,enrol:e.enrol,status:e.status,statusName:e.statusName,roles:e.roles,roleid:t,startDate:e.startDate,timestart:e.timestart,timeend:e.timeend,createdDate:e.createdDate||"-"},this.showEditEnrollmentModal=!0},async confirmeBulkDeleteEnrollment(){this.loading=!0;const e=[];for(const i of this.selectedUsers){const o=this.enrolments.find(a=>a.id===i);o&&o.offeruserenrolid&&e.push(o.offeruserenrolid)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await sE(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,o=s.length-i;i>0?(this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${o>0?` ${o} matrícula(s) não puderam ser canceladas.`:""}`),await this.loadRegisteredUsers(),this.selectedUsers=[]):this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`),await this.loadRegisteredUsers(),this.selectedUsers=[];this.showBulkDeleteEnrollmentModal=!1,this.loading=!1},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}this.showSendMessageModal(this.selectedUsers)},showSendMessageModal(e){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}this.showAddNoteModal(this.courseid,this.selectedUsers)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(o=>(o.getRoot().on("hidden.bs.modal",()=>{this.selectedBulkAction=""}),o)).catch(o=>{this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))for(const s of this.selectedUsers){const i=this.enrolments.find(o=>o.id===s);if(i){const o={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(o)}}else for(const s of this.selectedUsers)if(typeof s=="number"){const i=this.enrolments.find(o=>o.id===s);if(i){const o={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(o)}}else if(typeof s=="object"&&s!==null){const i={ID:s.id||"",Nome:s.fullName||s.name||"",Email:s.email||"",CPF:s.cpf||"",Papéis:s.roles||"",Grupos:s.groups||"","Data de Início":s.startDate||"","Data de Término":s.endDate||"",Prazo:s.deadline||"",Progresso:s.progress||"",Situação:s.situationName||s.situation||"",Nota:s.grade||"",Estado:s.statusName||""};t.push(i)}if(t.length===0){this.showErrorMessage("Nenhum dado disponível para download.");return}switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(h=>h.replace(/([A-Z])/g," $1").replace(/^./,m=>m.toUpperCase()).trim()),o=t+[i.join(","),...e.map(h=>s.map(m=>{const p=h[m]||"";return`"${String(p).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([o],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(c=>s.map(h=>{const m=c[h]||"";return`"${String(m).replace(/"/g,'""')}"`}).join(","))].join(`
`),o=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(o),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length===0)return;const t=Object.keys(e[0]),s=[];for(let A=0;A<t.length;A++){const se=t[A].replace(/([A-Z])/g," $1").replace(/^./,G=>G.toUpperCase()).trim();s.push(se)}let i="";for(let A=0;A<s.length;A++)i+="<th>"+s[A]+"</th>";let o="";for(let A=0;A<e.length;A++){let se="<tr>";for(let G=0;G<t.length;G++)se+="<td>"+(e[A][t[G]]||"")+"</td>";se+="</tr>",o+=se}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",c="</head><body><h1>Usuários Matriculados</h1>",h="<table><thead><tr>",m="</tr></thead><tbody>",p="</tbody></table>",_='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",w="</body></html>",D=a+u+c+h+i+m+o+p+_+w,k=new Blob([D],{type:"text/html;charset=utf-8;"}),F=URL.createObjectURL(k),te=document.createElement("a");te.setAttribute("href",F),te.setAttribute("download","usuarios_matriculados.html"),te.style.visibility="hidden",document.body.appendChild(te),te.click(),document.body.removeChild(te),URL.revokeObjectURL(F),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),o=document.createElement("a");o.setAttribute("href",i),o.setAttribute("download","usuarios_matriculados.json"),o.style.visibility="hidden",document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(i)},downloadODS(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(h=>{const m=s.map(p=>{const _=h[p]||"";return'"'+String(_).replace(/"/g,'""')+'"'});i.push(m.join(","))});const o=t+i.join(`
`),a=new Blob([o],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")},downloadPDF(e){e.length!==0&&(this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."))},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){this.showSuccessMessage(e.message||"Matrículas editadas com sucesso."),await this.loadRegisteredUsers(),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},fx={id:"offer-manager-component",class:"offer-manager"},hx={style:{display:"flex","align-items":"center","margin-bottom":"20px",gap:"10px"}},px={style:{width:"240px"}},mx={key:0,class:"alert alert-danger"},gx={class:"table-container"},_x={class:"checkbox-container"},vx=["checked","indeterminate"],yx={class:"checkbox-container"},bx=["checked","onChange"],wx=["href","title"],Ex={class:"user-name-link"},Cx={class:"progress-container"},Dx={class:"progress-text"},xx={class:"status-container"},Sx={class:"status-actions"},Ox=["onClick"],Tx=["onClick"],Nx={class:"selected-users-actions"},Ax={class:"bulk-actions-container"},Ix={key:1,class:"bottom-enroll-button"};function Mx(e,t,s,i,o,a){var ve,Ae,ie;const u=ee("BackButton"),c=ee("PageHeader"),h=ee("HierarchicalSelect"),m=ee("CustomButton"),p=ee("FilterTag"),_=ee("FilterTags"),w=ee("UserAvatar"),D=ee("RoleSelector"),k=ee("CustomTable"),F=ee("Pagination"),te=ee("EnrollmentDetailsModal"),A=ee("EnrolmentModalNew"),se=ee("EditEnrollmentModal"),G=ee("BulkEditEnrollmentModal"),ye=ee("BulkDeleteEnrollmentModal"),Z=ee("LFLoading"),fe=ee("Toast");return O(),N("div",fx,[P(c,{title:"Usuários matriculados"},{actions:Ne(()=>[P(u,{onClick:a.goBack},null,8,["onClick"])]),_:1}),f("div",hx,[f("div",px,[P(h,{modelValue:o.selectedPageView,"onUpdate:modelValue":t[0]||(t[0]=V=>o.selectedPageView=V),options:o.pageViewOptions,onNavigate:a.handlePageViewChange},null,8,["modelValue","options","onNavigate"])]),!o.classDetails||((ve=o.classDetails)==null?void 0:ve.operational_cycle)!==2?(O(),Rt(m,{key:0,variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])):ae("",!0)]),P(_,null,{default:Ne(()=>[(O(!0),N(Re,null,vt(o.filteredUsers,(V,Te)=>(O(),Rt(p,{key:V.id,onRemove:le=>a.removeFilter(Te)},{default:Ne(()=>[nt(Y(V.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1}),o.error?(O(),N("div",mx,[t[8]||(t[8]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),nt(" "+Y(o.error),1)])):ae("",!0),f("div",gx,[P(k,{headers:o.tableHeaders,items:o.enrolments,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort},{"header-select":Ne(()=>[f("div",_x,[f("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[1]||(t[1]=(...V)=>a.toggleSelectAll&&a.toggleSelectAll(...V)),class:"custom-checkbox"},null,40,vx)])]),"item-select":Ne(({item:V})=>[f("div",yx,[f("input",{type:"checkbox",checked:a.isSelected(V.id),onChange:Te=>a.toggleSelectUser(V.id),class:"custom-checkbox"},null,40,bx)])]),"item-fullName":Ne(({item:V})=>[f("a",{class:"user-name-container",href:`/user/view.php?id=${V.id}`,title:"Ver perfil de "+V.fullName},[P(w,{"full-name":V.fullName,size:36},null,8,["full-name"]),f("span",Ex,Y(V.fullName),1)],8,wx)]),"item-email":Ne(({item:V})=>[nt(Y(V.email),1)]),"item-cpf":Ne(({item:V})=>[nt(Y(V.cpf),1)]),"item-roles":Ne(({item:V})=>[P(D,{userId:V.id,offeruserenrolid:V.offeruserenrolid,currentRole:V.roles,offerclassid:o.offerclassid,onSuccess:a.handleRoleUpdateSuccess,onError:a.handleRoleUpdateError,onReloadTable:a.reloadTable},null,8,["userId","offeruserenrolid","currentRole","offerclassid","onSuccess","onError","onReloadTable"])]),"item-groups":Ne(({item:V})=>[nt(Y(V.groups),1)]),"item-startDate":Ne(({item:V})=>[nt(Y(V.startDate),1)]),"item-endDate":Ne(({item:V})=>[nt(Y(V.endDate),1)]),"item-deadline":Ne(({item:V})=>[nt(Y(V.deadline),1)]),"item-progress":Ne(({item:V})=>[f("div",Cx,[f("div",{class:"progress-bar",style:ls({width:V.progress})},null,4),f("span",Dx,Y(V.progress),1)])]),"item-situation":Ne(({item:V})=>[nt(Y(V.situationName),1)]),"item-grade":Ne(({item:V})=>[nt(Y(V.grade),1)]),"item-status":Ne(({item:V})=>[f("div",xx,[f("span",{class:he(["status-tag badge",V.status===0?"badge-success":"badge-danger"])},Y(V.statusName),3),f("div",Sx,[f("button",{class:"btn-information",onClick:Te=>a.showEnrollmentDetails(V),title:"Informações da matrícula"},t[9]||(t[9]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),f("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})],-1)]),8,Ox),f("button",{class:"btn-settings",onClick:Te=>a.editUser(V),title:"Editar matrícula"},t[10]||(t[10]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"3"}),f("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})],-1)]),8,Tx)])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),P(F,{"current-page":o.currentPage,"onUpdate:currentPage":t[2]||(t[2]=V=>o.currentPage=V),"per-page":o.perPage,"onUpdate:perPage":t[3]||(t[3]=V=>o.perPage=V),total:o.totalEnrolments,loading:o.loading},null,8,["current-page","per-page","total","loading"]),f("div",Nx,[f("div",Ax,[t[12]||(t[12]=f("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),mt(f("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[4]||(t[4]=V=>o.selectedBulkAction=V),onChange:t[5]||(t[5]=(...V)=>a.handleBulkAction&&a.handleBulkAction(...V))},t[11]||(t[11]=[ny('<option value="" data-v-95dd149d>Escolher...</option><optgroup label="Comunicação" data-v-95dd149d><option value="message" data-v-95dd149d>Enviar uma mensagem</option><option value="note" data-v-95dd149d>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-95dd149d><option value="download_csv" data-v-95dd149d> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-95dd149d>Microsoft excel (.xlsx)</option><option value="download_html" data-v-95dd149d>Tabela HTML</option><option value="download_json" data-v-95dd149d> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-95dd149d>OpenDocument (.ods)</option><option value="download_pdf" data-v-95dd149d> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições" data-v-95dd149d><option value="edit_enrolment" data-v-95dd149d> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-95dd149d> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Yl,o.selectedBulkAction]])])]),!o.classDetails||((Ae=o.classDetails)==null?void 0:Ae.operational_cycle)!==2?(O(),N("div",Ix,[P(m,{variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])])):ae("",!0),P(te,{show:o.showEnrollmentModal,user:o.selectedUser,"course-name":((ie=o.classDetails)==null?void 0:ie.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),P(A,{show:o.showEnrolmentModal,offerclassid:o.offerclassid,roles:o.roleOptions,onClose:a.closeEnrolmentModal,onSuccess:a.handleEnrolmentSuccess},null,8,["show","offerclassid","roles","onClose","onSuccess"]),P(se,{show:o.showEditEnrollmentModal,user:o.selectedUser,offerclassid:o.offerclassid,onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess,onError:a.handleEditEnrollmentError},null,8,["show","user","offerclassid","onClose","onSuccess","onError"]),P(G,{show:o.showBulkEditEnrollmentModal,users:o.selectedUsers.map(V=>o.enrolments.find(Te=>Te.id===V)).filter(Boolean),offerclassid:o.offerclassid,onClose:t[6]||(t[6]=V=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","offerclassid","onSuccess","onError"]),P(ye,{show:o.showBulkDeleteEnrollmentModal,users:o.selectedUsers.map(V=>o.enrolments.find(Te=>Te.id===V)).filter(Boolean),offerclassid:o.offerclassid,onClose:t[7]||(t[7]=V=>o.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","offerclassid","onConfirm","onError"]),P(Z,{"is-loading":o.loading},null,8,["is-loading"]),P(fe,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])])}const Px=He(dx,[["render",Mx],["__scopeId","data-v-95dd149d"]]),xR="",kx={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},Vx={class:"table-responsive"},Rx={class:"table"},Lx={key:0,class:"expand-column"},Ux=["onClick","data-value"],Fx={key:0,class:"sort-icon"},Bx={key:0},$x={key:0,class:"expand-column"},jx=["onClick","title"],Hx=["colspan"],qx={class:"expanded-content"},zx={key:1},Wx=["colspan"];function Gx(e,t,s,i,o,a){return O(),N("div",Vx,[f("table",Rx,[f("thead",null,[f("tr",null,[s.expandable?(O(),N("th",Lx)):ae("",!0),(O(!0),N(Re,null,vt(s.headers,u=>(O(),N("th",{key:u.value,onClick:c=>u.sortable?a.handleSort(u.value):null,class:he({sortable:u.sortable}),"data-value":u.value},[nt(Y(u.text)+" ",1),u.sortable?(O(),N("span",Fx,[f("i",{class:he(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):ae("",!0)],10,Ux))),128))])]),s.items.length>0?(O(),N("tbody",Bx,[(O(!0),N(Re,null,vt(s.items,(u,c)=>(O(),N(Re,{key:u.id},[f("tr",{class:he({expanded:o.expandedRows.includes(u.id)})},[s.expandable?(O(),N("td",$x,[f("button",{class:"btn-expand",onClick:h=>a.toggleExpand(u.id),title:o.expandedRows.includes(u.id)?"Recolher":"Expandir"},[f("div",{class:he(["icon-container",{"is-expanded":o.expandedRows.includes(u.id)}])},t[0]||(t[0]=[f("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[f("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),f("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,jx)])):ae("",!0),(O(!0),N(Re,null,vt(s.headers,h=>(O(),N("td",{key:`${u.id}-${h.value}`},[Vt(e.$slots,"item-"+h.value,{item:u},()=>[nt(Y(u[h.value]),1)],!0)]))),128))],2),s.expandable?(O(),N("tr",{key:0,class:he(["expanded-row",{"is-visible":o.expandedRows.includes(u.id)}])},[f("td",{colspan:s.headers.length+1},[f("div",qx,[Vt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,Hx)],2)):ae("",!0)],64))),128))])):(O(),N("tbody",zx,[f("tr",null,[f("td",{colspan:s.headers.length+(s.expandable?1:0)},[Vt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=f("div",{class:"empty-state"},[f("span",null,"Não existem registros")],-1))],!0)],8,Wx)])]))])])}const Kx=He(kx,[["render",Gx],["__scopeId","data-v-049f598f"]]),SR="",Yx={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},Qx={class:"text-editor-container"},Zx={class:"editor-toolbar"},Jx={class:"toolbar-group"},Xx=["disabled"],eS=["disabled"],tS=["disabled"],sS=["disabled"],rS={class:"toolbar-group"},nS=["disabled"],oS=["disabled"],iS=["contenteditable"],aS=["rows","placeholder","disabled"];function lS(e,t,s,i,o,a){return O(),N("div",Qx,[s.label?(O(),N("label",{key:0,class:he(["filter-label",{disabled:s.disabled}])},Y(s.label),3)):ae("",!0),f("div",{class:he(["editor-container",{disabled:s.disabled}])},[f("div",Zx,[f("div",Jx,[f("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[f("i",{class:"fas fa-bold"},null,-1)]),8,Xx),f("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[f("i",{class:"fas fa-italic"},null,-1)]),8,eS),f("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[f("i",{class:"fas fa-underline"},null,-1)]),8,tS),f("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[f("i",{class:"fas fa-strikethrough"},null,-1)]),8,sS)]),t[16]||(t[16]=f("div",{class:"toolbar-divider"},null,-1)),f("div",rS,[f("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[f("i",{class:"fas fa-list-ul"},null,-1)]),8,nS),f("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[f("i",{class:"fas fa-list-ol"},null,-1)]),8,oS)])]),o.showHtmlSource?mt((O(),N("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>o.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,aS)),[[ws,o.htmlContent]]):(O(),N("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent"},null,40,iS))],2)])}const ap=He(Yx,[["render",lS],["__scopeId","data-v-672cb06c"]]),OR="",TR="",uS={name:"AddCourseModal",components:{CustomInput:Uo,CustomButton:Fn,CustomTable:hn,Pagination:pn,Autocomplete:$o,FilterTag:Bo},props:{modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCategoryObject:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],existingCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,o)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<o[this.sortBy]?-1*a:i[this.sortBy]>o[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e){e?(this.loadCurrentCourses(),this.loadAllCategories()):(this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){e||(this.courseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null,this.loadCurrentCourses())},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.selectedCoursesPreview=[]},async confirm(){try{if(this.selectedCoursesPreview.length===0){console.warn("Nenhum curso selecionado para adicionar"),this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await r0(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch(e){console.error("Erro ao salvar cursos:",e)}},async loadCurrentCourses(){console.log("loadCurrentCourses this.offerId:",this.offerId);try{this.loadingCurrentCourses=!0;const e=await Xi(this.offerId);console.log("loadCurrentCourses response:",e),e&&e.data&&(Array.isArray(e.data)?this.existingCourses=e.data.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id})):this.existingCourses=[])}catch(e){console.error("Erro ao carregar cursos da oferta:",e),this.existingCourses=[]}finally{this.loadingCurrentCourses=!1}},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Lo("");e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar todas as categorias:",e),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCoursesForCategory(e.value)},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e)return;const o=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const a=await tp(this.offerId,o,i,t,this.coursesPerPage);let u=null,c=[];try{if(Array.isArray(a)&&a.length>0?a[0].error===!1&&a[0].data?a[0].data.courses?(u=a[0].data,c=u.courses||[]):Array.isArray(a[0].data)?(c=a[0].data,u={page:1,total_pages:1}):a[0].data.data&&Array.isArray(a[0].data.data)&&(c=a[0].data.data,u={page:a[0].data.page||1,total_pages:a[0].data.total_pages||1}):(c=a,u={page:1,total_pages:1}):a&&typeof a=="object"&&(a.data&&a.data.courses?(c=a.data.courses,u={page:a.data.page||1,total_pages:a.data.total_pages||1}):a.courses?(c=a.courses,u={page:a.page||1,total_pages:a.total_pages||1}):a.data&&Array.isArray(a.data)&&(c=a.data,u={page:1,total_pages:1})),c.length===0&&a&&typeof a=="object"&&!Array.isArray(a)){for(const h in a)if(Array.isArray(a[h])){c=a[h],u={page:1,total_pages:1};break}}}catch(h){console.error("Erro ao processar resposta:",h)}if(u){if(this.coursesPage=u.page||1,this.coursesTotalPages=u.total_pages||1,this.hasMoreCourses=(u.page||1)<(u.total_pages||1),c&&c.length>0){const m=c.filter(p=>!this.existingCourses.some(_=>_.id===p.id)&&!this.selectedCoursesPreview.some(_=>_.id===p.id)).map(p=>({value:p.id,label:p.fullname}));s?this.courseOptions=[...this.courseOptions,...m]:this.courseOptions=m}}else console.warn("Formato de resposta inesperado")}catch(a){console.error("Erro ao carregar cursos da categoria:",a),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(console.log("Buscando cursos com termo:",e),this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?tp(this.offerId,this.selectedCategory,"",1,this.coursesPerPage).then(i=>{let o=[];try{if(Array.isArray(i)&&i.length>0?i[0].error===!1&&i[0].data?i[0].data.courses?o=i[0].data.courses||[]:Array.isArray(i[0].data)?o=i[0].data:i[0].data.data&&Array.isArray(i[0].data.data)&&(o=i[0].data.data):o=i:i&&typeof i=="object"&&(i.data&&i.data.courses?o=i.data.courses:i.courses?o=i.courses:i.data&&Array.isArray(i.data)&&(o=i.data)),o.length===0&&i&&typeof i=="object"&&!Array.isArray(i)){for(const a in i)if(Array.isArray(i[a])){o=i[a];break}}}catch(a){console.error("Erro ao processar resposta em removeCourse:",a)}o&&o.length>0&&o.find(u=>u.id===s.id)&&this.courseOptions.push({value:s.id,label:s.name})}).catch(i=>{console.error("Erro ao verificar categoria do curso:",i)}):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.courseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCurrentCourses()}}},cS={class:"modal-header"},dS={class:"modal-body"},fS={class:"search-section"},hS={class:"search-group"},pS={class:"search-group"},mS={class:"table-container"},gS={key:0,class:"empty-preview-message"},_S={class:"action-buttons"},vS=["onClick"],yS={class:"modal-footer"};function bS(e,t,s,i,o,a){const u=ee("Autocomplete"),c=ee("CustomTable"),h=ee("Pagination"),m=ee("CustomButton");return s.modelValue?(O(),N("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...p)=>a.closeModal&&a.closeModal(...p))},[f("div",{class:"modal-content",onClick:t[5]||(t[5]=Lt(()=>{},["stop"]))},[f("div",cS,[t[8]||(t[8]=f("h2",null,"Adicionar curso",-1)),f("button",{class:"close-button",onClick:t[0]||(t[0]=(...p)=>a.closeModal&&a.closeModal(...p))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",dS,[t[11]||(t[11]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",fS,[f("div",hS,[P(u,{modelValue:o.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>o.selectedCategory=p),items:o.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:o.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":o.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),f("div",pS,[P(u,{modelValue:o.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>o.selectedCourse=p),items:o.courseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!o.selectedCategory,loading:o.loadingCourses||o.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",mS,[o.selectedCoursesPreview.length===0?(O(),N("div",gS,t[9]||(t[9]=[f("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(O(),Rt(c,{key:1,headers:o.tableHeaders,items:a.filteredCourses,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort},{"item-actions":Ne(({item:p})=>[f("div",_S,[f("button",{class:"btn-action btn-delete",onClick:_=>a.removeCourse(p),title:"Remover da lista"},t[10]||(t[10]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,vS)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),o.selectedCoursesPreview.length>0?(O(),Rt(h,{key:0,"current-page":o.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=p=>o.currentPage=p),a.handlePageChange],"per-page":o.perPage,"onUpdate:perPage":[t[4]||(t[4]=p=>o.perPage=p),a.handlePerPageChange],total:o.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):ae("",!0)]),f("div",yS,[P(m,{variant:"primary",label:"Confirmar",disabled:o.selectedCoursesPreview.length===0,onClick:a.confirm},null,8,["disabled","onClick"]),P(m,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])])):ae("",!0)}const wS=He(uS,[["render",bS],["__scopeId","data-v-0a88ee2a"]]),NR="",AR="",ES={name:"DuplicateClassModal",components:{Autocomplete:$o,CustomTable:hn,Pagination:pn},props:{show:{type:Boolean,default:!1},turma:{type:Object,default:null},parentCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success","loading","error"],data(){return{selectedCategory:null,selectedCategoryObject:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return[...this.selectedCoursesPreview].sort((i,o)=>{const a=i[this.sortBy],u=o[this.sortBy];return a<u?this.sortDesc?1:-1:a>u?this.sortDesc?-1:1:0}).slice(e,t)}},watch:{show(e){e&&this.turma&&this.parentCourse?(this.resetForm(),this.$nextTick(()=>{this.loadAllCategories()})):this.resetForm()},turma(){this.show&&(this.resetForm(),this.loadAllCategories())},parentCourse(){this.show&&(this.resetForm(),this.loadAllCategories())},selectedCategory(e){e?this.loadCoursesForCategory(e):(this.targetCourseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null)}},methods:{resetForm(){this.selectedCategory=null,this.selectedCategoryObject=null,this.categoryOptions=[],this.loadingCategories=!1,this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.coursesPage=1,this.coursesPerPage=20,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.currentPage=1,this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0,this.existingCourses=[]},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Lo("",this.offerId);e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar categorias:",e),this.$emit("error","Erro ao carregar categorias. Por favor, tente novamente."),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.targetCourseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.targetCourseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e||!this.turma)return;const o=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const a=await f0(this.turma.id);let u=[];a&&Array.isArray(a)&&(u=a);const c=this.parentCourse.offerCourseId||this.parentCourse.id;u=u.filter(m=>{const p=m.categoryid,_=String(p)===String(o),w=m.offercourseid||m.id,D=String(w)!==String(c),k=!i||m.name&&m.name.toLowerCase().includes(i.toLowerCase())||m.fullname&&m.fullname.toLowerCase().includes(i.toLowerCase());return _&&D&&k}),u=u.filter(m=>{const p=m.offercourseid||m.id;return!this.selectedCoursesPreview.some(_=>String(_.value)===String(p))});const h=u.map(m=>{let p=m.offercourseid||m.id;return p==null?null:{value:p,label:m.name||m.fullname||m.coursename||`Curso ${p}`,categoryid:m.categoryid,category_name:m.category_name}}).filter(m=>m!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...h]:this.targetCourseOptions=h,this.hasMoreCourses=h.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(a){console.error("Erro ao carregar cursos da categoria:",a),this.$emit("error","Erro ao carregar cursos. Por favor, tente novamente."),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value)),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},async handleConfirm(){if(!(!this.turma||this.selectedCoursesPreview.length===0))try{this.$emit("loading",!0);const e=this.turma.nome,t=parseInt(this.turma.id,10);if(isNaN(t))throw new Error("ID da turma inválido");this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0;const s=[];for(const i of this.selectedCoursesPreview){const o=parseInt(i.value,10);if(isNaN(o)){console.error(`ID inválido para o curso ${i.label}`);continue}try{this.$emit("loading",!0,`Duplicando para ${i.label} (${this.duplicatedCount+1}/${this.totalToDuplicate})`);const a=await d0(t,o);s.push({turmaNome:e,targetCourseName:i.label,turmaId:t,targetCourseId:o,result:a}),this.duplicatedCount++}catch(a){console.error(`Erro ao duplicar para o curso ${i.label}:`,a),this.$emit("error",`Erro ao duplicar para o curso ${i.label}: ${a.message}`)}}if(this.duplicatingCourses=!1,s.length>0)this.$emit("success",{turmaNome:e,duplicatedCount:s.length,totalSelected:this.totalToDuplicate,results:s}),this.resetForm(),this.$emit("close");else throw new Error("Nenhuma turma foi duplicada com sucesso.")}catch(e){this.$emit("error",e.message||"Erro ao duplicar turmas.")}finally{this.duplicatingCourses=!1,this.$emit("loading",!1)}}}},CS={class:"modal-header"},DS={class:"modal-title"},xS={class:"modal-body"},SS={class:"search-section"},OS={class:"search-group"},TS={class:"search-group"},NS={class:"table-container"},AS={key:0,class:"empty-preview-message"},IS={class:"action-buttons"},MS=["onClick"],PS={class:"modal-footer"},kS=["disabled"];function VS(e,t,s,i,o,a){var m;const u=ee("Autocomplete"),c=ee("CustomTable"),h=ee("Pagination");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[8]||(t[8]=p=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[7]||(t[7]=Lt(()=>{},["stop"]))},[f("div",CS,[f("h3",DS,'Duplicar Turma "'+Y((m=s.turma)==null?void 0:m.nome)+'"',1),f("button",{class:"close-button",onClick:t[0]||(t[0]=p=>e.$emit("close"))},t[9]||(t[9]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",xS,[t[12]||(t[12]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",SS,[f("div",OS,[P(u,{modelValue:o.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>o.selectedCategory=p),items:o.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:o.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":o.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),f("div",TS,[P(u,{modelValue:o.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>o.selectedCourse=p),items:o.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!o.selectedCategory,loading:o.loadingCourses||o.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",NS,[o.selectedCoursesPreview.length===0?(O(),N("div",AS,t[10]||(t[10]=[f("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(O(),Rt(c,{key:1,headers:o.tableHeaders,items:a.filteredCourses,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort},{"item-actions":Ne(({item:p})=>[f("div",IS,[f("button",{class:"btn-action btn-delete",onClick:_=>a.removeCourse(p),title:"Remover da lista"},t[11]||(t[11]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,MS)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),o.selectedCoursesPreview.length>0?(O(),Rt(h,{key:0,"current-page":o.currentPage,"onUpdate:currentPage":t[3]||(t[3]=p=>o.currentPage=p),"per-page":o.perPage,"onUpdate:perPage":t[4]||(t[4]=p=>o.perPage=p),total:o.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):ae("",!0)]),f("div",PS,[f("button",{class:"btn-primary",onClick:t[5]||(t[5]=(...p)=>a.handleConfirm&&a.handleConfirm(...p)),disabled:o.selectedCoursesPreview.length===0}," Duplicar ",8,kS),f("button",{class:"btn-secondary",onClick:t[6]||(t[6]=p=>e.$emit("close"))},"Cancelar")])])])):ae("",!0)}const RS=He(ES,[["render",VS],["__scopeId","data-v-7ebf3397"]]),IR="",LS={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},US=["data-content","aria-label"],FS=["title","aria-label"];function BS(e,t,s,i,o,a){return O(),N("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[f("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,FS)],8,US)}const vu=He(LS,[["render",BS]]),MR="",$S={name:"EnrolTypeModal",components:{CustomSelect:mr,HelpIcon:vu},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Tipo de Inscrição"},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Continuar"},cancelButtonText:{type:String,default:"Cancelar"},offercourseid:{type:[Number,String],required:!0},offerid:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},mounted(){this.$nextTick(()=>{this.initializePopovers()})},watch:{show(e){e&&(this.loadEnrolmentMethods(),this.$nextTick(()=>{this.initializePopovers()}))}},methods:{async loadEnrolmentMethods(){this.loading=!0;const e=await np(!0);e&&Array.isArray(e.data)&&(this.enrolmentMethods=e.data.map(t=>({value:t.enrol,label:t.name}))),this.loading=!1},handleConfirm(){this.selectedEnrolType&&this.$emit("confirm",{enrolType:this.selectedEnrolType,offercourseid:this.offercourseid,offerid:this.offerid})},initializePopovers(){typeof $<"u"&&typeof $.fn.popover<"u"?$('[data-toggle="popover"]').popover({container:"body",trigger:"focus",html:!0}):console.warn("jQuery ou Bootstrap não estão disponíveis para inicializar popovers")}}},jS={class:"modal-header"},HS={class:"modal-title"},qS={class:"modal-body"},zS={class:"enrol-type-modal"},WS={class:"form-group mb-3"},GS={class:"label-with-help"},KS={class:"limited-width-input",style:{"max-width":"280px"}},YS={class:"modal-footer"},QS={class:"footer-buttons"},ZS=["disabled"];function JS(e,t,s,i,o,a){const u=ee("HelpIcon"),c=ee("CustomSelect");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[5]||(t[5]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:he(["modal-container",[`modal-${s.size}`]]),onClick:t[4]||(t[4]=Lt(()=>{},["stop"]))},[f("div",jS,[f("h3",HS,Y(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[6]||(t[6]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",qS,[f("div",zS,[t[9]||(t[9]=f("p",{class:"modal-description"}," Selecione o tipo de inscrição para esta turma. Esta configuração não poderá ser alterada posteriormente. ",-1)),f("div",WS,[f("div",GS,[t[7]||(t[7]=f("label",{class:"form-label"},"Método de inscrição",-1)),t[8]||(t[8]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw m-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),P(u,{title:"Ajuda com método de inscrição",text:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.
Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.
Autoisncrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no curso para fazer sua matrícula em uma turma. Ou seja, ele não será inscrito automaticamente como no Tipo de inscrição 'Inscrição por público-alvo em ofertas'.`})]),f("div",KS,[P(c,{modelValue:o.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=h=>o.selectedEnrolType=h),options:[{value:"",label:"Selecione um método..."},...o.enrolmentMethods],width:280,required:""},null,8,["modelValue","options"])])])])]),f("div",YS,[t[10]||(t[10]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1)),f("div",QS,[f("button",{class:"btn btn-primary",onClick:t[2]||(t[2]=(...h)=>a.handleConfirm&&a.handleConfirm(...h)),disabled:!o.selectedEnrolType},Y(s.confirmButtonText),9,ZS),f("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=h=>e.$emit("close"))},Y(s.cancelButtonText),1)])])],2)])):ae("",!0)}const XS=He($S,[["render",JS],["__scopeId","data-v-4b89966a"]]),eO="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik03IDE0cy0xIDAtMS0xIDEtNCA1LTQgNSAzIDUgNC0xIDEtMSAxSDd6bTQtNmEzIDMgMCAxIDAgMC02IDMgMyAwIDAgMCAwIDZ6IiBmaWxsPSIjZmZmIi8+CiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTUuMjE2IDE0QTIuMjM4IDIuMjM4IDAgMCAxIDUgMTNjMC0xLjM1NS42OC0yLjc1IDEuOTM2LTMuNzJBNi4zMjUgNi4zMjUgMCAwIDAgNSA5Yy00IDAtNSAzLTUgNHMxIDEgMSAxaDQuMjE2eiIgZmlsbD0iI2ZmZiIvPgogICAgPHBhdGggZD0iTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1eiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",tO="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAyYS41LjUgMCAwIDEgLjUuNXY1aDVhLjUuNSAwIDAgMSAwIDFoLTV2NWEuNS41IDAgMCAxLTEgMHYtNWgtNWEuNS41IDAgMSAxIDAtMWg1di01QS41LjUgMCAwIDEgOCAyeiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",PR="",sO={name:"NewOfferView",components:{CustomTable:hn,CustomSelect:mr,CustomInput:Uo,CustomButton:Fn,Pagination:pn,CollapsibleTable:Kx,PageHeader:ra,BackButton:_u,Autocomplete:$o,TextEditor:ap,CustomCheckbox:ea,FilterRow:ta,FilterGroup:sa,FilterTag:Bo,FilterTags:na,AddCourseModal:wS,ConfirmationModal:gu,Toast:Fo,HelpIcon:vu,DuplicateClassModal:RS,EnrolTypeModal:XS,LFLoading:mu},setup(){const e=Zi(),t=Qh();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},data(){return{icons:{users:eO,plus:tO},showAddCourseModalVisible:!1,showCourseStatusModal:!1,selectedCourse:null,showWarning:!0,isEditing:!1,offerId:null,showDeleteCourseModal:!1,courseToDelete:null,showDeleteClassModal:!1,classToDelete:null,classParentCourse:null,showClassStatusModal:!1,showDuplicateClassModal:!1,showEnrolTypeModal:!1,selectedClass:null,classToDuplicate:null,classToDuplicateParentCourse:null,selectedCourseForClass:null,loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,offer:{id:null,name:"",offerType:"",description:"",status:0},formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}},offerTypeOptions:[],typeOptionsEnabled:!1,audienceTypeOptions:[],allAudiences:[],selectedAudiences:[],categoryOptions:[],courseOptions:[],selectedCategory:null,selectedCourse:null,inputFilters:{course:"",category:"",onlyActive:!1},appliedFilters:{course:"",category:"",onlyActive:!1},courseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"turmasCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],selectedCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,courseNoResultsText:"Nenhum curso encontrado"}},async mounted(){await this.loadInitialData(),this.offerId?(this.isEditing=!0,await this.loadOfferData(),await this.loadCategoryOptions(),await this.loadCourseOptions()):this.isEditing=!1,await this.loadTypeOptions(),await this.loadAllAudiences(),this.showWarning=!0},computed:{canManageCourses(){return this.isEditing&&this.offerId!==null},hasActiveFilters(){return this.appliedFilters.course}},methods:{getEnrolTypeLabel(e){if(!e)return"-";if(typeof e!="string")return String(e);const t=e.toLowerCase(),s={offer_manual:"Manual",offer_self:"Autoinscrição",manual:"Manual",self:"Autoinscrição",guest:"Visitante",cohort:"Coorte",database:"Base de dados",flatfile:"Arquivo plano",ldap:"LDAP",lti:"LTI",meta:"Meta-curso",mnet:"MNet",paypal:"PayPal",shibboleth:"Shibboleth"};if(s[e])return s[e];for(const[i,o]of Object.entries(s))if(i.toLowerCase()===t)return o;if(t==="offer_manual")return"Manual";if(t==="offer_self")return"Autoinscrição";for(const[i,o]of Object.entries(s))if(t.includes(i.toLowerCase()))return o;return e},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},async loadInitialData(){},async loadOfferData(){if(this.offerId){this.loading=!0;try{const e=await Jh(this.offerId);e&&e.data&&(this.offer={id:e.data.id,name:e.data.name,offerType:e.data.typeid,description:e.data.description||"",status:e.data.status||0},this.selectedAudiences=e.data.audiences?e.data.audiences.map(t=>({value:t.id,label:t.name})):[],await this.loadCourses())}catch{this.showErrorMessage("Erro ao carregar dados da oferta.")}finally{this.loading=!1}}},async loadTypeOptions(){try{const e=await fu();if(e&&e.data){const{enabled:t,types:s}=e.data;this.typeOptionsEnabled=!!t,t&&Array.isArray(s)?this.offerTypeOptions=s.map(i=>({value:i,label:i.charAt(0).toUpperCase()+i.slice(1)})):(this.offerTypeOptions=[],this.typeOptionsEnabled=!1)}else this.offerTypeOptions=[],this.typeOptionsEnabled=!1}catch{this.typeOptionsEnabled=!1}},async loadAllAudiences(){if(this.allAudiences.length>0){this.audienceTypeOptions=[...this.allAudiences];return}this.loading=!0;try{const e=await ep("");e&&e.items&&(this.allAudiences=e.items.map(t=>({value:t.id,label:t.name})),this.audienceTypeOptions=[...this.allAudiences])}catch{this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},async applyFilters(){this.appliedFilters=JSON.parse(JSON.stringify(this.inputFilters));try{this.loading=!0,await this.loadCourses()}catch{this.showErrorMessage("Erro ao aplicar filtros. Por favor, tente novamente.")}finally{this.loading=!1}this.currentPage=1},clearFilters(){this.inputFilters={course:"",category:"",onlyActive:!1},this.appliedFilters={course:"",category:"",onlyActive:!1},this.selectedCategory=null,this.selectedCourse=null,this.loadCourses(),this.loadCourseOptions()},async removeFilter(e){try{this.loading=!0,this.inputFilters[e]="",this.appliedFilters[e]="",e==="category"?(this.selectedCategory=null,await this.loadCourseOptions()):e==="course"&&(this.selectedCourse=null),this.hasActiveFilters?await this.applyFilters():await this.loadCourses()}catch{this.showErrorMessage("Erro ao remover filtro. Por favor, tente novamente.")}finally{this.loading=!1}},async loadCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await Lo("",this.offerId);e&&e.data&&Array.isArray(e.data)?this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})):this.categoryOptions=[]}catch{this.showErrorMessage("Erro ao carregar categorias.")}finally{this.loading=!1}},async loadCourseOptions(e=null,t=!0){if(this.offerId)try{if(this.loading=!0,e){await this.updateCourseOptionsByCategory(e);return}t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const s=await Xi(this.offerId,{onlyActive:!1,page:this.coursesPage,perPage:this.coursesPerPage,sortBy:this.sortBy,sortDesc:this.sortDesc});if(s&&s.data&&s.data.courses){const{page:i,total_pages:o,courses:a}=s.data;this.coursesPage=i||this.coursesPage,this.coursesTotalPages=o||1,this.hasMoreCourses=this.coursesPage<this.coursesTotalPages;const u=a.map(c=>({value:c.id||c.courseid,label:c.fullname}));t?this.courseOptions=u:this.courseOptions=[...this.courseOptions,...u],this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else if(s&&s.data&&Array.isArray(s.data)){const i=s.data.map(o=>({value:o.id||o.courseid,label:o.fullname}));t?this.courseOptions=i:this.courseOptions=[...this.courseOptions,...i],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else t&&(this.courseOptions=[]),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível"}catch{this.showErrorMessage("Erro ao carregar cursos."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}},async loadMoreCourses(){this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.coursesPage+=1,this.selectedCategory?await this.updateCourseOptionsByCategory({value:this.selectedCategory,label:this.inputFilters.category},!1):await this.loadCourseOptions(null,!1))},async handleCourseSearch(e){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[];try{this.selectedCategory&&(this.selectedCategory=null);const t=await sp(this.offerId,e);t&&t.data&&Array.isArray(t.data)&&(this.courseOptions=t.data.map(s=>({value:s.id||s.courseid,label:s.fullname}))),this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso encontrado para a busca":"Nenhum curso encontrado"}catch{this.showErrorMessage("Erro ao buscar cursos."),this.courseOptions=[]}finally{this.loadingCourses=!1}},async updateCourseOptionsByCategory(e,t=!0){if(!(!this.offerId||!e)){this.loading=!0;try{t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;let s=e.value;if(isNaN(parseInt(s))){const o=await Lo(e.label,this.offerId);if(o&&o.data&&o.data.length>0){const a=o.data.find(u=>u.name.toLowerCase()===e.label.toLowerCase());if(a)s=a.id;else{this.showErrorMessage("Erro ao identificar a categoria selecionada."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}else{this.showErrorMessage("Erro ao buscar categorias."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}const i=await rp(this.offerId,s);if(i&&i.data&&Array.isArray(i.data)){const o=i.data.map(a=>({value:a.id||a.courseid,label:a.fullname}));t?(this.courseOptions=o,this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""):this.courseOptions=[...this.courseOptions,...o],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível nesta categoria":"Nenhum curso encontrado"}else t&&(this.courseOptions=[],this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível nesta categoria"}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},async handleCategorySelect(e){e&&(this.inputFilters.category=e.label,this.currentPage=1,await this.applyFilters(),await this.updateCourseOptionsByCategory(e,!0))},async handleCourseSelect(e){e&&(this.inputFilters.course=e.label,this.currentPage=1,this.selectedCategory=null,await this.applyFilters())},handleOnlyActiveChange(){this.appliedFilters.onlyActive=this.inputFilters.onlyActive,this.currentPage=1,this.loadCourses()},goBack(){this.router.push({name:"listar-ofertas"})},async searchCourses(){if(this.offerId)try{this.loading=!0;const e=await sp(this.offerId,this.appliedFilters.course);if(e&&e.data&&e.data.length>0){const t=e.data.map(i=>i.id),s=await Xi(this.offerId,{courseIds:t});s&&s.data.courses?this.selectedCourses=s.data.courses.map(i=>({id:i.id||i.courseid,offerCourseId:i.id,name:i.fullname,category:i.category_name||"-",turmasCount:Array.isArray(i.turmas)?i.turmas.length:0,status:i.status===1||i.status==="1"?"Ativo":"Inativo",can_delete:i.can_delete!==void 0?i.can_delete:!0,can_activate:i.can_activate!==void 0?i.can_activate:!0,turmas:Array.isArray(i.turmas)?i.turmas.map(o=>({id:o.id,nome:o.name,enrol_type:o.enrol_type||"-",vagas:o.max_users?o.max_users:"Ilimitado",inscritos:o.enrolled_users||0,dataInicio:o.start_date||"-",dataFim:o.end_date||"-"})):[]})):this.selectedCourses=[]}else this.selectedCourses=[]}catch(e){console.log(e),this.showErrorMessage("Erro ao buscar cursos. Por favor, tente novamente.")}finally{this.loading=!1}},async searchCategories(){if(this.offerId)try{this.loading=!0;const e=await Lo(this.appliedFilters.category,this.offerId);if(e&&e.data&&e.data.length>0){let t=[];for(const s of e.data){const i=s.id;if(!i)continue;const o=await rp(this.offerId,i);if(o&&o.data){const a=o.data.map(u=>({id:u.id||u.courseid,offerCourseId:u.id,name:u.fullname,category:s.name||"-",turmasCount:Array.isArray(u.turmas)?u.turmas.length:0,status:u.status===1||u.status==="1"?"Ativo":"Inativo",can_delete:u.can_delete!==void 0?u.can_delete:!0,can_activate:u.can_activate!==void 0?u.can_activate:!0,turmas:Array.isArray(u.turmas)?u.turmas.map(c=>({id:c.id,nome:c.name,enrol_type:c.enrol_type||"-",vagas:c.max_users?c.max_users:"Ilimitado",inscritos:c.enrolled_users||0,dataInicio:c.start_date||"-",dataFim:c.end_date||"-"})):[]}));t=[...t,...a]}}this.selectedCourses=t}else this.selectedCourses=[]}catch{this.showErrorMessage("Erro ao buscar categorias. Por favor, tente novamente.")}finally{this.loading=!1}},showAddCourseModal(){this.showAddCourseModalVisible=!0},async handleAddCourseConfirm(e){try{this.loading=!0;for(const t of e)await J1(this.offerId,t);await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadCourses()},handlePageChange(e){this.currentPage=e,this.loadCourses()},addTurma(e){this.selectedCourseForClass=e,this.showEnrolTypeModal=!0},handleEnrolTypeConfirm(e){this.showEnrolTypeModal=!1,this.router.push({name:"NewClass",params:{offercourseid:e.offercourseid,offerid:e.offerid||this.offerId||"0"},query:{enrol_type:e.enrolType}})},editTurma(e){const t=this.selectedCourses.find(s=>s.turmas&&s.turmas.some(i=>i.id===e.id));t?this.router.push({name:"EditClass",params:{offercourseid:t.offerCourseId,classid:e.id}}):this.showErrorMessage("Não foi possível editar a turma. Curso pai não encontrado.")},toggleClassStatus(e){this.selectedClass={...e,status:e.status||"Ativo"},this.showClassStatusModal=!0},async confirmToggleClassStatus(){if(this.selectedClass)try{this.loading=!0;const e=this.selectedClass.nome,t=this.selectedClass.status!=="Ativo";await h0(this.selectedClass.id,t);const s=this.selectedCourses.findIndex(i=>i.turmas.some(o=>o.id===this.selectedClass.id));if(s!==-1){const i=this.selectedCourses[s],o=i.turmas.findIndex(a=>a.id===this.selectedClass.id);o!==-1&&(i.turmas[o].status=t?"Ativo":"Inativo")}await this.loadCourses(),this.showSuccessMessage(t?`Turma "${e}" ativada com sucesso.`:`Turma "${e}" inativada com sucesso.`),this.selectedClass=null,this.showClassStatusModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status da turma.")}finally{this.loading=!1}},removeTurma(e,t){const s=e.turmas[t];s.can_delete&&(this.classToDelete=s,this.classParentCourse=e,this.showDeleteClassModal=!0)},viewRegisteredUsers(e){this.router.push({name:"usuarios-matriculados",params:{offerclassid:parseInt(e.id)}})},duplicateTurma(e,t){this.classToDuplicate=e,this.classToDuplicateParentCourse=t,this.showDuplicateClassModal=!0},async handleDuplicateSuccess(e){await this.loadCourses(),e.duplicatedCount?this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para ${e.duplicatedCount} curso(s).`):this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para o curso "${e.targetCourseName}".`)},async confirmDeleteClass(){if(!(!this.classToDelete||!this.classParentCourse))try{this.loading=!0;const e=this.classToDelete.nome;await l0(this.classToDelete.id);const t=this.classParentCourse.turmas.findIndex(s=>s.id===this.classToDelete.id);t!==-1&&(this.classParentCourse.turmas.splice(t,1),this.classParentCourse.turmasCount=this.classParentCourse.turmas.length),this.showSuccessMessage(`Turma ${e} excluída com sucesso.`),this.classToDelete=null,this.classParentCourse=null,this.showDeleteClassModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}},updateTurmasCount(e){const t=this.selectedCourses.findIndex(s=>s.id===e);if(t!==-1){const s=this.selectedCourses[t];s.turmasCount=Array.isArray(s.turmas)?s.turmas.length:0,this.selectedCourses[t]={...s}}},async loadCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.appliedFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.appliedFilters.course&&(e.courseSearch=this.appliedFilters.course),this.appliedFilters.category&&(e.categorySearch=this.appliedFilters.category);const t=await Xi(this.offerId,e);if(t&&t.data){let s=[],i=this.currentPage,o=1,a=0;t.data.courses&&({page:i,total_pages:o,total_items:a,courses:s}=t.data),this.currentPage=i||this.currentPage;const u=[];for(const c of s)try{const h=await i0(c.id);let m=[];h&&typeof h=="object"&&h.error===!1&&Array.isArray(h.data)&&h.data.length>0&&(m=h.data.map(p=>{let _=p.enrol_type||p.enrol||"-";return{id:p.id,nome:p.name,enrol_type:_,vagas:p.max_users?p.max_users:"Ilimitado",inscritos:p.enrolled_users||0,dataInicio:this.formatDate(p.startdate),dataFim:this.formatDate(p.enddate),status:p.status===0||p.status==="0"?"Inativo":"Ativo",can_activate:p.can_activate!==void 0?p.can_activate:!0,can_delete:p.can_delete!==void 0?p.can_delete:!0}})),u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:m.length,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:m})}catch{u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:0,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:[]})}this.selectedCourses=u,a!=null?this.totalItems=a:o>0?this.totalItems=o*this.perPage:this.totalItems=u.length,await this.loadCategoryOptions(),await this.loadCourseOptions()}else this.selectedCourses=[],this.totalItems=0,this.categoryOptions=[],this.courseOptions=[]}catch{this.showErrorMessage("Erro ao carregar cursos da oferta. Por favor, tente novamente."),this.selectedCourses=[],this.totalItems=0}finally{this.loading=!1}},async loadOffer(e){try{this.loading=!0,this.inputFilters={course:"",category:""},this.appliedFilters={course:"",category:""},this.selectedCategory=null,this.selectedCourse=null;const[t,s]=await Promise.all([Jh(e),ep("")]),i=Array.isArray(t)?t[0]:t;if(!i.error&&i.data){const o=i.data;if(this.offer={name:o.name,offerType:o.type,description:o.description,id:o.id,status:o.status},s&&Array.isArray(s.items)){const a=o.audiences||[];this.selectedAudiences=s.items.filter(u=>a.includes(u.id)).map(u=>({value:u.id,label:u.name.toUpperCase()}))}this.isEditing=!0,await this.loadCourses()}else throw new Error(i.message||"Erro ao carregar dados da oferta")}catch(t){this.showErrorMessage(t.message||"Erro ao carregar oferta.")}finally{this.loading=!1}},handleSelectAllAudiences(){const e=new Set(this.allAudiences.map(i=>i.value)),t=new Set(this.selectedAudiences.map(i=>i.value));let s=!0;for(const i of e)if(!t.has(i)){s=!1;break}s&&this.selectedAudiences.length===this.allAudiences.length?this.selectedAudiences=[]:this.selectedAudiences=[...this.allAudiences]},validate(){Object.keys(this.formErrors).forEach(t=>{this.formErrors[t].hasError=!1});let e=!1;return this.offer.name||(this.formErrors.name.hasError=!0,e=!0),this.selectedAudiences.length===0&&(this.formErrors.audiences.hasError=!0,e=!0),e&&this.showErrorMessage("Há campos obrigatórios a serem preenchidos."),!e},validateField(e){switch(e){case"name":this.formErrors.name.hasError=!this.offer.name;break;case"audiences":this.formErrors.audiences.hasError=this.selectedAudiences.length===0;break}return!this.formErrors[e].hasError},async saveOffer(){var e;if(this.loading=!0,!this.validate()){this.loading=!1;return}try{const t={name:this.offer.name,description:this.offer.description,type:this.offer.offerType,status:this.offer.status,audiences:this.selectedAudiences.map(i=>i.value)};let s;if(this.isEditing&&this.offerId)if(t.id=this.offerId,s=await Xh(t),s&&!s.error)if(await this.updateOfferAudiences())this.showSuccessMessage("Oferta atualizada com sucesso!");else throw new Error("Oferta atualizada, mas houve falha ao atualizar públicos-alvo.");else{const i=(s==null?void 0:s.message)||(s==null?void 0:s.error)||"Falha ao atualizar oferta.";throw new Error(i)}else if(s=await Xh(t),s&&s.data&&s.data.id){const i=s.data.id;this.offerId=i,this.offer.id=i,this.isEditing=!0,this.showSuccessMessage("Oferta salva com sucesso!");const o=`/edit-offer/${this.offerId}`;this.router.replace(o)}else{const i=(s==null?void 0:s.message)||((e=s==null?void 0:s[0])==null?void 0:e.message)||"Falha ao salvar a oferta.";throw new Error(i)}}catch(t){this.showErrorMessage(t.message||"Erro ao salvar oferta. Verifique os dados e tente novamente.")}finally{this.loading=!1}},async updateOfferAudiences(){if(!this.offerId)return!1;try{const e=this.selectedAudiences.map(s=>s.value),t=await t0(this.offerId,e);return!!(t&&!t.error)}catch{return this.showErrorMessage("Erro ao atualizar públicos-alvo."),!1}finally{}},toggleCourseStatus(e){e.can_activate&&(this.selectedCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status==="Ativo"?e.can_activate?"Inativar":"Não é possível inativar este curso":e.can_activate?"Ativar":"Não é possível ativar este curso"},async confirmToggleCourseStatus(){if(this.selectedCourse)try{this.loading=!0;const e=this.selectedCourse.status!=="Ativo",t=this.selectedCourse.name,s=this.selectedCourse.offerCourseId||this.selectedCourse.id;await e0(this.offerId,s,e);const i=this.selectedCourses.findIndex(o=>o.id===this.selectedCourse.id);i!==-1&&(this.selectedCourses[i].status=e?"Ativo":"Inativo"),this.showCourseStatusModal=!1,this.selectedCourse=null,await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status do curso.")}finally{this.loading=!1}},deleteCourse(e){e.can_delete&&(this.courseToDelete=e,this.showDeleteCourseModal=!0)},async confirmDeleteCourse(){if(this.courseToDelete)try{this.loading=!0;const e=this.courseToDelete.name,t=this.courseToDelete.offerCourseId||this.courseToDelete.id;await X1(this.offerId,t),this.selectedCourses=this.selectedCourses.filter(s=>s.id!==this.courseToDelete.id),await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(`Curso "${e}" excluído com sucesso.`),this.courseToDelete=null,this.showDeleteCourseModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}},async created(){try{const t=await fu();if(t&&t.data){const{enabled:s,types:i,default:o}=t.data;this.typeOptionsEnabled=s,s&&Array.isArray(i)&&(this.offerTypeOptions=i.map(a=>({value:a,label:a})),o&&!this.isEditing&&(this.offer.offerType=o))}}catch(t){this.showErrorMessage(t.message||"Erro ao carregar opções de tipos.")}const e=this.route.params.id;e&&(this.offerId=parseInt(e),this.loadOffer(this.offerId))},watch:{"inputFilters.course"(e,t){e.length===0&&t.length>0&&this.loadCourses()},"inputFilters.category"(e,t){e.length===0&&t.length>0&&this.loadCourses()},selectedCategory(e){e||(this.inputFilters.category="",this.appliedFilters.category&&(this.appliedFilters.category="",this.loadCourses()),this.loadCourseOptions())},selectedCourse(e){e||(this.inputFilters.course="",this.appliedFilters.course&&(this.appliedFilters.course="",this.loadCourses()))},currentPage(){this.loadCourses()},perPage(){this.currentPage=1,this.loadCourses()}}},rO={id:"new-offer-component",class:"new-offer"},nO={key:0,class:"alert alert-warning"},oO={class:"section-container"},iO={class:"form-row mb-3"},aO={class:"form-group"},lO={class:"input-container"},uO={key:0,class:"form-group"},cO={class:"input-container"},dO={class:"form-row mb-3",style:{"margin-bottom":"1.5rem"}},fO={class:"form-group"},hO={class:"label-container"},pO={class:"label-with-help"},mO={class:"input-container"},gO={class:"form-group text-editor-container"},_O={class:"limited-width-editor"},vO={key:0,class:"section-title"},yO={key:1,class:"message-container"},bO={key:2},wO={class:"filters-left-group"},EO={class:"filters-right-group"},CO={class:"empty-state"},DO={class:"no-results"},xO=["title"],SO={class:"action-buttons"},OO=["onClick"],TO=["src"],NO=["onClick","disabled","title"],AO=["onClick","disabled","title"],IO={class:"turmas-container"},MO={class:"turmas-content"},PO={key:0},kO={class:"turma-col"},VO=["title"],RO={class:"turma-col"},LO={class:"turma-col"},UO={class:"turma-col"},FO={class:"turma-col"},BO={class:"turma-col"},$O={class:"turma-col"},jO={class:"action-buttons"},HO=["onClick"],qO=["src"],zO=["onClick"],WO=["onClick"],GO=["title","onClick"],KO=["onClick","disabled","title"],YO={key:1,class:"empty-turmas"},QO={class:"d-flex justify-content-between align-items-center"},ZO={class:"actions-container offer-actions"};function JO(e,t,s,i,o,a){var Te,le,Ge,gt,ht,ct,xe,be,Ut,Xt,yt;const u=ee("BackButton"),c=ee("PageHeader"),h=ee("CustomInput"),m=ee("CustomSelect"),p=ee("HelpIcon"),_=ee("Autocomplete"),w=ee("TextEditor"),D=ee("FilterGroup"),k=ee("CustomCheckbox"),F=ee("FilterTag"),te=ee("FilterTags"),A=ee("FilterRow"),se=ee("CollapsibleTable"),G=ee("Pagination"),ye=ee("CustomButton"),Z=ee("AddCourseModal"),fe=ee("ConfirmationModal"),ve=ee("DuplicateClassModal"),Ae=ee("EnrolTypeModal"),ie=ee("LFLoading"),V=ee("Toast");return O(),N("div",rO,[P(c,{title:o.isEditing?`Editar oferta: ${o.offer.name}`:"Nova oferta"},{actions:Ne(()=>[P(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"]),o.showWarning?(O(),N("div",nO,t[21]||(t[21]=[f("i",{class:"fas fa-exclamation-triangle"},null,-1),nt(" Para que uma instância de oferta seja ativada e disponibilize os cursos para os públicos-alvo configurados, é necessário garantir que pelo menos um curso, um grupo de público-alvo, e uma turma estejam configurados à instância da oferta. ")]))):ae("",!0),f("div",oO,[t[27]||(t[27]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),f("div",iO,[f("div",aO,[t[22]||(t[22]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Nome da Oferta"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),f("div",lO,[P(h,{modelValue:o.offer.name,"onUpdate:modelValue":t[0]||(t[0]=ce=>o.offer.name=ce),placeholder:"Oferta 0001",width:280,required:"","has-error":o.formErrors.name.hasError,"error-message":o.formErrors.name.message,onValidate:t[1]||(t[1]=ce=>a.validateField("name"))},null,8,["modelValue","has-error","error-message"])])]),o.typeOptionsEnabled?(O(),N("div",uO,[t[23]||(t[23]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Tipo da oferta")])],-1)),f("div",cO,[P(m,{modelValue:o.offer.offerType,"onUpdate:modelValue":t[2]||(t[2]=ce=>o.offer.offerType=ce),options:o.offerTypeOptions,width:280},null,8,["modelValue","options"])])])):ae("",!0)]),f("div",dO,[f("div",fO,[f("div",hO,[f("div",pO,[t[24]||(t[24]=f("label",{class:"form-label"},"Público-alvo",-1)),t[25]||(t[25]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),P(p,{title:"Ajuda com público-alvo",text:"Selecione pelo menos um público-alvo para a oferta."})])]),f("div",mO,[P(_,{class:"autocomplete-audiences",modelValue:o.selectedAudiences,"onUpdate:modelValue":[t[3]||(t[3]=ce=>o.selectedAudiences=ce),t[4]||(t[4]=ce=>a.validateField("audiences"))],items:o.audienceTypeOptions,placeholder:"Pesquisar público-alvo...","input-max-width":218,required:!0,"show-all-option":!0,"has-error":o.formErrors.audiences.hasError,"error-message":o.formErrors.audiences.message,onSelectAll:a.handleSelectAllAudiences},null,8,["modelValue","items","has-error","error-message","onSelectAll"])])])]),f("div",gO,[t[26]||(t[26]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Descrição da oferta")])],-1)),f("div",_O,[P(w,{modelValue:o.offer.description,"onUpdate:modelValue":t[5]||(t[5]=ce=>o.offer.description=ce),placeholder:"Digite a descrição da oferta aqui...",rows:5},null,8,["modelValue"])])])]),f("div",{class:he(["section-container",{"no-title-section":!o.isEditing}])},[o.isEditing?(O(),N("h2",vO,"CURSOS")):ae("",!0),!a.canManageCourses||!o.isEditing?(O(),N("div",yO,t[28]||(t[28]=[f("div",{class:"lock-message"},[f("i",{class:"fas fa-lock lock-icon"}),f("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))):ae("",!0),o.isEditing&&a.canManageCourses?(O(),N("div",bO,[P(A,{inline:"",class:"courses-filter-row"},{default:Ne(()=>[f("div",wO,[P(D,null,{default:Ne(()=>[P(_,{modelValue:o.selectedCategory,"onUpdate:modelValue":t[6]||(t[6]=ce=>o.selectedCategory=ce),items:o.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","input-max-width":218,"has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":o.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","no-results-text","onSelect"])]),_:1}),P(D,null,{default:Ne(()=>[P(_,{modelValue:o.selectedCourse,"onUpdate:modelValue":t[7]||(t[7]=ce=>o.selectedCourse=ce),items:o.courseOptions,placeholder:"Pesquisar...",label:"Curso","input-max-width":218,"has-search-icon":!0,"auto-open":!0,loading:o.loadingCourses||o.loadingMoreCourses,"no-results-text":o.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","loading","no-results-text","onSelect","onLoadMore","onSearch"])]),_:1}),P(D,{isCheckbox:!0,class:"checkbox-filter-group"},{default:Ne(()=>[P(k,{modelValue:o.inputFilters.onlyActive,"onUpdate:modelValue":t[8]||(t[8]=ce=>o.inputFilters.onlyActive=ce),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),_:1}),o.appliedFilters.course?(O(),Rt(te,{key:0,class:"mt-3"},{default:Ne(()=>[P(F,{onRemove:t[9]||(t[9]=ce=>a.removeFilter("course"))},{default:Ne(()=>[nt(" Curso: "+Y(o.appliedFilters.course),1)]),_:1})]),_:1})):ae("",!0)]),f("div",EO,[f("button",{class:"btn btn-primary",onClick:t[10]||(t[10]=(...ce)=>a.showAddCourseModal&&a.showAddCourseModal(...ce))}," Adicionar curso ")])]),_:1}),P(se,{headers:o.courseTableHeaders,items:o.selectedCourses,"sort-by":o.sortBy,"sort-desc":o.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":Ne(()=>[f("div",CO,[f("span",DO,Y(o.loading?"Carregando registros...":"Não existem registros"),1)])]),"item-name":Ne(({item:ce})=>[f("span",{title:ce.name},Y(ce.name.length>50?ce.name.slice(0,50)+"...":ce.name),9,xO)]),"item-actions":Ne(({item:ce})=>[f("div",SO,[f("button",{class:"btn-action btn-add",onClick:ze=>a.addTurma(ce),title:"Adicionar turma"},[f("img",{src:o.icons.plus,alt:"Adicionar turma"},null,8,TO)],8,OO),f("button",{class:he(["btn-action",ce.status==="Ativo"?"btn-deactivate":"btn-activate"]),onClick:ze=>a.toggleCourseStatus(ce),disabled:ce.status==="Inativo"&&!ce.can_activate||!ce.can_activate,title:a.getStatusButtonTitle(ce)},[f("i",{class:he(ce.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,NO),f("button",{class:"btn-action btn-delete",onClick:ze=>a.deleteCourse(ce),disabled:!ce.can_delete,title:ce.can_delete?"Excluir":"Não é possível excluir este curso"},t[29]||(t[29]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,AO)])]),"expanded-content":Ne(({item:ce})=>[f("div",IO,[t[34]||(t[34]=f("div",{class:"turmas-header"},[f("div",{class:"turma-col"},"NOME DA TURMA"),f("div",{class:"turma-col"},"TIPO DE INSCRIÇÃO"),f("div",{class:"turma-col"},"QTD. DE VAGAS"),f("div",{class:"turma-col"},"QTD. DE ALUNOS INSCRITOS"),f("div",{class:"turma-col"},"DATA INÍCIO DA TURMA"),f("div",{class:"turma-col"},"DATA FIM DA TURMA"),f("div",{class:"turma-col"},"AÇÕES")],-1)),f("div",MO,[ce.turmas&&ce.turmas.length>0?(O(),N("div",PO,[(O(!0),N(Re,null,vt(ce.turmas,(ze,fs)=>(O(),N("div",{class:"turmas-row",key:fs},[f("div",kO,[f("span",{title:ze.nome},Y(ze.nome.length>20?ze.nome.slice(0,20)+"...":ze.nome),9,VO)]),f("div",RO,Y(a.getEnrolTypeLabel(ze.enrol_type)),1),f("div",LO,Y(ze.vagas),1),f("div",UO,Y(ze.inscritos),1),f("div",FO,Y(ze.dataInicio),1),f("div",BO,Y(ze.dataFim),1),f("div",$O,[f("div",jO,[f("button",{class:"btn-action btn-users",onClick:zt=>a.viewRegisteredUsers(ze),title:"Usuários Matriculados"},[f("img",{src:o.icons.users,alt:"Usuários Matriculados"},null,8,qO)],8,HO),f("button",{class:"btn-action btn-edit",onClick:zt=>a.editTurma(ze),title:"Editar"},t[30]||(t[30]=[f("i",{class:"fas fa-pencil-alt"},null,-1)]),8,zO),f("button",{class:"btn-action btn-duplicate",onClick:zt=>a.duplicateTurma(ze,ce),title:"Duplicar Turma"},t[31]||(t[31]=[f("i",{class:"fas fa-copy"},null,-1)]),8,WO),f("button",{class:he(["btn-action",ze.status==="Ativo"?"btn-deactivate":"btn-activate"]),title:ze.status==="Ativo"?"Inativar":"Ativar",onClick:zt=>a.toggleClassStatus(ze)},[f("i",{class:he(ze.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,GO),f("button",{class:"btn-action btn-delete",onClick:zt=>a.removeTurma(ce,fs),disabled:!ze.can_delete,title:ze.can_delete?"Excluir":"Não é possível excluir esta turma"},t[32]||(t[32]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,KO)])])]))),128))])):(O(),N("div",YO,t[33]||(t[33]=[f("span",null,"Nenhuma turma encontrada para este curso",-1)])))])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),P(G,{ref:"pagination","current-page":o.currentPage,"onUpdate:currentPage":[t[11]||(t[11]=ce=>o.currentPage=ce),a.handlePageChange],"per-page":o.perPage,"onUpdate:perPage":t[12]||(t[12]=ce=>o.perPage=ce),total:o.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"])])):ae("",!0)],2),t[36]||(t[36]=f("hr",null,null,-1)),f("div",QO,[t[35]||(t[35]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[nt(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",ZO,[P(ye,{variant:"primary",label:"Salvar",onClick:a.saveOffer},null,8,["onClick"]),P(ye,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])])]),P(Z,{modelValue:o.showAddCourseModalVisible,"onUpdate:modelValue":t[13]||(t[13]=ce=>o.showAddCourseModalVisible=ce),"offer-id":o.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offer-id","onConfirm"]),P(fe,{show:o.showCourseStatusModal,title:((Te=o.selectedCourse)==null?void 0:Te.status)==="Ativo"?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((le=o.selectedCourse)==null?void 0:le.status)==="Ativo"?"":"Tem certeza que deseja ativar este curso?","list-items":((Ge=o.selectedCourse)==null?void 0:Ge.status)==="Ativo"?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((gt=o.selectedCourse)==null?void 0:gt.status)==="Ativo"?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:((ht=o.selectedCourse)==null?void 0:ht.status)==="Ativo"?"warning":"question",onClose:t[14]||(t[14]=ce=>o.showCourseStatusModal=!1),onConfirm:a.confirmToggleCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),P(fe,{show:o.showDeleteCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[15]||(t[15]=ce=>o.showDeleteCourseModal=!1),onConfirm:a.confirmDeleteCourse},null,8,["show","onConfirm"]),P(fe,{show:o.showDeleteClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[16]||(t[16]=ce=>o.showDeleteClassModal=!1),onConfirm:a.confirmDeleteClass},null,8,["show","onConfirm"]),P(fe,{show:o.showClassStatusModal,title:((ct=o.selectedClass)==null?void 0:ct.status)==="Ativo"?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:((xe=o.selectedClass)==null?void 0:xe.status)==="Ativo"?"":"Tem certeza que deseja ativar esta turma?","list-items":((be=o.selectedClass)==null?void 0:be.status)==="Ativo"?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":((Ut=o.selectedClass)==null?void 0:Ut.status)==="Ativo"?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:((Xt=o.selectedClass)==null?void 0:Xt.status)==="Ativo"?"warning":"question",onClose:t[17]||(t[17]=ce=>o.showClassStatusModal=!1),onConfirm:a.confirmToggleClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),P(ve,{show:o.showDuplicateClassModal,turma:o.classToDuplicate,parentCourse:o.classToDuplicateParentCourse,offerId:o.offerId,onClose:t[18]||(t[18]=ce=>o.showDuplicateClassModal=!1),onSuccess:a.handleDuplicateSuccess,onLoading:t[19]||(t[19]=ce=>o.loading=ce),onError:a.showErrorMessage},null,8,["show","turma","parentCourse","offerId","onSuccess","onError"]),P(Ae,{show:o.showEnrolTypeModal,offercourseid:(yt=o.selectedCourseForClass)==null?void 0:yt.offerCourseId,offerid:o.offerId||"0",onClose:t[20]||(t[20]=ce=>o.showEnrolTypeModal=!1),onConfirm:a.handleEnrolTypeConfirm},null,8,["show","offercourseid","offerid","onConfirm"]),P(ie,{"is-loading":o.loading},null,8,["is-loading"]),P(V,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])])}const lp=He(sO,[["render",JO],["__scopeId","data-v-540f42da"]]),kR="",XO={name:"NewClassView",components:{CustomInput:Uo,CustomSelect:mr,CustomButton:Fn,PageHeader:ra,BackButton:_u,Autocomplete:$o,TextEditor:ap,CustomCheckbox:ea,FilterRow:ta,FilterGroup:sa,Toast:Fo,HelpIcon:vu,FilterTag:Bo,FilterTags:na},setup(){const e=Zi(),t=Qh();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},props:{offercourseid:{type:Number,required:!0},classid:{type:Number,required:!1,default:null}},data(){return{loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,isEditing:!1,enrolmentMethods:[],classData:{enrol:"",offercourseid:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,extensionallowedsituations:[]}},selectedTeachers:[],teacherSearchTerm:"",debounceTimer:null,teacherList:[],showTeacherDropdown:!1,highlightedIndex:-1,extensionSituations:[],reenrolSituations:[],roleOptions:[],situationList:[],offerCourse:null,formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Papel padrão é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"}},validationAlert:{show:!1,message:"Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados."}}},async created(){if(!this.offercourseid)throw new Error("offercourseid não foi definido.");this.classid?this.isEditing=!0:this.route.query.classid&&this.route.query.edit==="true"&&(this.isEditing=!0,this.$nextTick(()=>{this.router.replace({name:"EditClass",params:{offercourseid:this.offercourseid,classid:this.classid}})})),await this.loadInitialData(),this.isEditing&&this.classid&&(await this.loadClassData(),this.$nextTick(()=>{this.restartComponent()}))},mounted(){window.scrollTo(0,0),document.addEventListener("click",this.handleClickOutside)},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside),this.debounceTimer&&clearTimeout(this.debounceTimer)},computed:{extensionSituationList(){let e=[0,1];return this.situationList.filter(t=>e.includes(t.value))},reenrolSituationList(){let e=[6,7,8,4,5];return this.situationList.filter(t=>e.includes(t.value))},maxEnrolPeriod(){if(this.classData.startdate&&this.classData.optional_fields.enddate&&this.classData.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.classData.startdate,this.classData.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.classData.startdate&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.classData.startdate===this.classData.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},watch:{extensionSituations:{handler(e){this.classData.optional_fields.extensionallowedsituations=e.map(t=>t.value)},deep:!0},reenrolSituations:{handler(e){this.classData.optional_fields.reenrolmentsituations=e.map(t=>t.value)},deep:!0},"classData.optional_fields.enableenrolperiod":function(e){!e&&this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado."))},"classData.startdate":function(){this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enddate":function(){this.classData.optional_fields.enableenddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.classData.optional_fields.enableenddate&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enableenddate":function(e){e&&this.classData.optional_fields.enddate&&this.validateField("enddate"),!e&&this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enrolperiod":function(e){this.classData.optional_fields.enableenrolperiod&&e&&this.validateField("enrolperiod")},"classData.optional_fields.preenrolmentstartdate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.preenrolmentenddate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()}},methods:{calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const o=Math.abs(i-s);return Math.ceil(o/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enableenrolperiod=!1,this.classData.optional_fields.enrolperiod=null,this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[]),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},async loadInitialData(){try{this.loading=!0,this.isEditing||(this.classData.offercourseid=this.offercourseid),await this.loadOfferCourse(),await this.loadRoles(),await this.loadSituations(),await this.loadEnrolmentMethods()}catch{this.showErrorMessage("Alguns dados não puderam ser carregados.")}finally{this.loading=!1}},async loadOfferCourse(){try{const e=await o0(this.offercourseid);this.offerCourse=e==null?void 0:e.data}catch{this.showErrorMessage("Erro ao carregar informações do curso da oferta.")}},async loadRoles(){const e=await pu(this.offercourseid);if(e.data&&(this.roleOptions=e.data.map(t=>({value:t.id,label:t.name})),!this.classData.optional_fields.roleid)){const t=this.roleOptions.find(s=>s.value===5);this.classData.optional_fields.roleid=(t==null?void 0:t.value)??this.roleOptions[0].value}},async loadSituations(){const e=await c0();e.data&&(this.situationList=e.data.map(t=>({value:t.id,label:t.name})))},async loadEnrolmentMethods(){const e=this.route.query.enrol_type;!this.isEditing&&e&&(this.classData.enrol=e);const t=await np(!0);t&&Array.isArray(t)&&(this.enrolmentMethods=t.map(s=>({value:s.enrol,label:s.name})))},validate(){Object.keys(this.formErrors).forEach(s=>{this.formErrors[s].hasError=!1}),this.validationAlert.show=!1;let e=!1;return this.classData.classname||(this.formErrors.classname.hasError=!0,e=!0),this.classData.startdate?this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate)?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0,e=!0):this.formErrors.startdate.hasError=!1:(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0,e=!0),this.classData.optional_fields.roleid||(this.formErrors.roleid.hasError=!0,e=!0),this.classData.optional_fields.enableenddate&&(this.classData.optional_fields.enddate?this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate)?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0,e=!0):this.formErrors.enddate.hasError=!1:(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0,e=!0)),this.classData.optional_fields.enablepreenrolment&&(this.classData.optional_fields.preenrolmentstartdate||(this.formErrors.preenrolmentstartdate.hasError=!0,e=!0),this.classData.optional_fields.preenrolmentenddate||(this.formErrors.preenrolmentenddate.hasError=!0,e=!0)),this.validatePreenrolmentDates()||(e=!0),this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enrolperiod?this.maxEnrolPeriod!==null&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod&&(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0,e=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0,e=!0)),this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.extensionperiod||(this.formErrors.extensionperiod.hasError=!0,e=!0),this.classData.optional_fields.extensiondaysavailable||(this.formErrors.extensiondaysavailable.hasError=!0,e=!0),this.classData.optional_fields.extensionmaxrequests||(this.formErrors.extensionmaxrequests.hasError=!0,e=!0)),e&&(this.validationAlert.show=!0,this.showErrorMessage(this.validationAlert.message),window.scrollTo({top:0,behavior:"smooth"})),!e},validateField(e){switch(e){case"enrol":this.formErrors.enrol.hasError=!1;break;case"classname":this.formErrors.classname.hasError=!this.classData.classname;break;case"startdate":const s=this.classData.startdate,i=s&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate);s?i?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!1):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0);break;case"roleid":this.formErrors.roleid.hasError=!this.classData.optional_fields.roleid;break;case"enddate":const o=this.classData.optional_fields.enableenddate,a=this.classData.optional_fields.enddate,u=o&&a&&this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate);o&&!a?(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0):u?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0):(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!1);break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const c=this.classData.optional_fields.enableenrolperiod,h=this.classData.optional_fields.enrolperiod!==null&&this.classData.optional_fields.enrolperiod!==void 0&&this.classData.optional_fields.enrolperiod!=="",m=this.maxEnrolPeriod!==null&&h&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod;c&&!h?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):c&&m?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"extensionperiod":this.formErrors.extensionperiod.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionperiod;break;case"extensiondaysavailable":this.formErrors.extensiondaysavailable.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensiondaysavailable;break;case"extensionmaxrequests":this.formErrors.extensionmaxrequests.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionmaxrequests;break}const t=Object.values(this.formErrors).some(s=>s.hasError);return this.validationAlert.show=t,!this.formErrors[e].hasError},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.classData.optional_fields.enablepreenrolment){const t=this.classData.startdate,s=this.classData.optional_fields.enableenddate?this.classData.optional_fields.enddate:null,i=this.classData.optional_fields.preenrolmentstartdate,o=this.classData.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),o||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(o)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(o)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(o)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},async loadClassData(){this.loading=!0;const e=await hu(this.classid);if(e.error==!0)throw new Error(e.exception);this.classData=e.data,e.data.optional_fields&&this.processOptionalFields(e.data.optional_fields),e.data.teachers&&Array.isArray(e.data.teachers)&&(this.selectedTeachers=e.data.teachers),this.updateUIAfterLoading(),document.title=`Editar Turma: ${this.classData.classname}`,this.loading=!1},processOptionalFields(e){this.processDateFields(e),this.processEnrolmentFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processDateFields(e){e.enableenddate&&(this.classData.optional_fields.enableenddate=!0,this.classData.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.classData.optional_fields.enablepreenrolment=!0,this.classData.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.classData.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.classData.optional_fields.enableenrolperiod=!0,this.classData.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.classData.optional_fields.enrolperiod=null},processUserLimits(e){this.classData.optional_fields.minusers=e.minusers>0?e.minusers:null,this.classData.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.classData.optional_fields.roleid=e.roleid||null,this.classData.optional_fields.description=e.description||""},processReenrolment(e){e.enablereenrol?(this.classData.optional_fields.enablereenrol=!0,this.classData.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.reenrolSituations=e.reenrolmentsituations.map(t=>this.situationList.find(s=>s.value===parseInt(t))))):this.classData.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.classData.optional_fields.enableextension=!0,this.processExtensionPeriods(e),this.processExtensionSituations(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.classData.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.classData.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.classData.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},processExtensionSituations(e){Array.isArray(e.extensionallowedsituations)&&(this.extensionSituations=e.extensionallowedsituations.map(t=>this.situationList.find(s=>s.value===t)))},resetExtensionFields(){this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[]},handleTeacherInput(){const e=this.teacherSearchTerm.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?(this.showTeacherDropdown=!0,this.highlightedIndex=-1,this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialTeachers(e)},500)):(this.showTeacherDropdown=!1,this.teacherList=[])},async fetchPotentialTeachers(e){let t=this.selectedTeachers.map(i=>i.id||i.value)??[];const s=await u0(this.offercourseid,this.classid,e,t);this.teacherList=!s.error&&s.data?s.data:[]},removeTeacher(e){this.selectedTeachers=this.selectedTeachers.filter(t=>t.id!==e)},handleTeacherInputFocus(){this.teacherSearchTerm.length>=3&&this.teacherList.length>0&&(this.showTeacherDropdown=!0)},selectTeacher(e){this.selectedTeachers.push({id:e.id,value:e.id,fullname:e.fullname,email:e.email}),this.teacherSearchTerm="",this.showTeacherDropdown=!1,this.highlightedIndex=-1,this.teacherList=[],this.$nextTick(()=>{this.$refs.teacherSearchInput&&this.$refs.teacherSearchInput.focus()})},handleKeydown(e){if(!(!this.showTeacherDropdown||this.teacherList.length===0))switch(e.key){case"ArrowDown":e.preventDefault(),this.highlightedIndex=Math.min(this.highlightedIndex+1,this.teacherList.length-1);break;case"ArrowUp":e.preventDefault(),this.highlightedIndex=Math.max(this.highlightedIndex-1,0);break;case"Enter":e.preventDefault(),this.highlightedIndex>=0&&this.highlightedIndex<this.teacherList.length&&this.selectTeacher(this.teacherList[this.highlightedIndex]);break;case"Escape":e.preventDefault(),this.showTeacherDropdown=!1,this.highlightedIndex=-1;break}},handleClickOutside(e){this.$refs.teacherSearchContainer&&!this.$refs.teacherSearchContainer.contains(e.target)&&(this.showTeacherDropdown=!1,this.highlightedIndex=-1)},updateUIAfterLoading(){this.$nextTick(()=>{this.updateFormFields(),this.$forceUpdate(),(!this.classData.classname||!this.classData.enrol)&&this.showErrorMessage("Dados incompletos após carregamento.")})},updateFormFields(){this.updateSelectField("enrolSelect",this.classData.enrol),this.updateInputField("classnameInput",this.classData.classname),this.updateInputField("startdateInput",this.classData.startdate)},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},async saveClass(){if(!this.validate())return;this.loading=!0;const e=JSON.parse(JSON.stringify(this.classData));e.teachers=this.selectedTeachers.map(o=>o.id),!e.optional_fields.enableextension||!e.optional_fields.enableenrolperiod?(e.optional_fields.extensionperiod=void 0,e.optional_fields.extensiondaysavailable=void 0,e.optional_fields.extensionmaxrequests=void 0,e.optional_fields.extensionallowedsituations=[],e.optional_fields.enableenrolperiod||(e.optional_fields.enableextension=!1)):e.optional_fields.extensionallowedsituations=this.extensionSituations.map(o=>o.value),e.optional_fields.enableenrolperiod||(e.optional_fields.enrolperiod=void 0),e.optional_fields.enablereenrol?e.optional_fields.reenrolmentsituations=this.reenrolSituations.map(o=>o.value):e.optional_fields.reenrolmentsituations=[],["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].forEach(o=>{const a=e.optional_fields[o];(a===0||a===null||a===""||a===void 0)&&(e.optional_fields[o]=void 0)}),this.isEditing&&"enrol"in e&&delete e.enrol;const i=(this.isEditing?["offercourseid","classname","startdate"]:["offercourseid","classname","startdate","enrol"]).filter(o=>!e[o]);if(i.length>0){this.showErrorMessage(`Campos obrigatórios ausentes: ${i.join(", ")}`),this.loading=!1;return}if(e.offercourseid=parseInt(e.offercourseid),this.isEditing&&this.classid){e.offerclassid=this.classid;let o=await a0(e);!o.error&&o.data?(this.showSuccessMessage(o.data.message),this.loadClassData()):this.showErrorMessage(o.exception.message)}else{let o=await n0(e);!o.error&&o.data?(this.showSuccessMessage(o.data.message),this.isEditing=!0,this.router.push({name:"EditClass",params:{offercourseid:this.offercourseid,classid:o.data.offerclassid}})):this.showErrorMessage(o.exception.message)}this.loading=!1},goBack(){this.offerCourse.offerid?this.router.push({name:"editar-oferta",params:{id:this.offerCourse.offerid}}):this.router.push({name:"listar-ofertas"})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},updateFormFields(){if(this.$refs.enrolSelect&&this.classData.enrol)try{this.$refs.enrolSelect.value=this.classData.enrol;const e=new Event("change");this.$refs.enrolSelect.$el.dispatchEvent(e),this.$refs.enrolSelect.$emit("input",this.classData.enrol),this.$refs.enrolSelect.$forceUpdate()}catch{}if(this.$refs.classnameInput&&this.classData.classname)try{this.$refs.classnameInput.value=this.classData.classname;const e=new Event("input");this.$refs.classnameInput.$el.dispatchEvent(e),this.$refs.classnameInput.$emit("input",this.classData.classname),this.$refs.classnameInput.$forceUpdate()}catch{}if(this.$refs.startdateInput&&this.classData.startdate)try{this.$refs.startdateInput.value=this.classData.startdate;const e=new Event("input");this.$refs.startdateInput.$el.dispatchEvent(e),this.$refs.startdateInput.$emit("input",this.classData.startdate),this.$refs.startdateInput.$forceUpdate()}catch{}this.$forceUpdate()},handleSelectAllExtensionSituations(){const e=this.extensionSituationList.every(t=>this.extensionSituations.some(s=>s.value===t.value));this.extensionSituations=e?[]:[...this.extensionSituationList],this.classData.optional_fields.extensionallowedsituations=this.extensionSituations.map(t=>t.value)},handleSelectAllReenrolSituations(){const e=this.reenrolSituationList.every(t=>this.reenrolSituations.some(s=>s.value===t.value));this.reenrolSituations=e?[]:[...this.reenrolSituationList],this.classData.optional_fields.reenrolmentsituations=this.reenrolSituations.map(t=>t.value)},restartComponent(){window.scrollTo(0,0),this.updateFormFields(),this.$forceUpdate(),setTimeout(()=>{this.updateFormFields(),this.$forceUpdate(),window.scrollTo(0,0)},500)}}},eT={class:"new-class",ref:"classView"},tT={class:"page-header-container"},sT={key:0,class:"validation-alert"},rT={class:"section-container"},nT={class:"form-group mb-3"},oT={class:"label-with-help"},iT={class:"limited-width-input",style:{"max-width":"280px"}},aT={class:"form-row mb-3"},lT={class:"form-group"},uT={class:"label-with-help"},cT={class:"limited-width-input"},dT={class:"label-with-help"},fT={class:"input-with-checkbox"},hT={class:"limited-width-input"},pT={class:"form-row mb-3"},mT={class:"label-with-help"},gT={class:"label-with-help"},_T={key:2,class:"form-group"},vT={class:"form-group mb-3"},yT={class:"label-with-help"},bT={class:"limited-width-editor"},wT={class:"form-row mb-3"},ET={key:0,class:"form-group"},CT={class:"label-with-help"},DT={class:"limited-width-input"},xT={key:1,class:"form-group"},ST={class:"label-with-help"},OT={class:"limited-width-input"},TT={class:"form-group"},NT={class:"label-with-help"},AT={class:"limited-width-input"},IT={class:"form-row mb-3"},MT={class:"label-with-help"},PT={class:"input-with-checkbox"},kT={class:"limited-width-input"},VT={class:"section-container"},RT={class:"form-row mb-3"},LT={class:"label-with-help"},UT={class:"form-row mb-3"},FT={class:"limited-width-input"},BT={class:"form-row mb-3"},$T={class:"limited-width-input"},jT={class:"form-row mb-3"},HT={class:"limited-width-input"},qT={class:"limited-width-select"},zT={key:1,class:"section-container"},WT={class:"form-row mb-3"},GT={class:"form-group"},KT={class:"label-with-help"},YT={class:"limited-width-select"},QT={class:"section-container"},ZT={class:"form-group mb-3"},JT={class:"label-with-help"},XT={class:"limited-width-select"},eN={class:"position-relative",ref:"teacherSearchContainer"},tN={class:"input-wrapper with-icon"},sN={key:0,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","max-height":"200px","overflow-y":"auto","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"},ref:"teacherDropdown"},rN=["onClick","onMouseenter"],nN={key:0,class:"text-muted small"},oN={key:1,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"}},iN={class:"my-4"},aN=["onClick"],lN={class:"actions-container"},uN={key:2,class:"loading"};function cN(e,t,s,i,o,a){const u=ee("BackButton"),c=ee("PageHeader"),h=ee("HelpIcon"),m=ee("CustomInput"),p=ee("CustomCheckbox"),_=ee("TextEditor"),w=ee("CustomSelect"),D=ee("Autocomplete"),k=ee("CustomButton"),F=ee("Toast"),te=pv("tooltip");return O(),N("div",eT,[f("div",tT,[P(c,{title:o.isEditing?"Editar turma":"Nova turma"},{actions:Ne(()=>[P(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"])]),o.validationAlert.show?(O(),N("div",sT,[t[34]||(t[34]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),f("span",null,Y(o.validationAlert.message),1)])):ae("",!0),f("div",rT,[t[51]||(t[51]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),f("div",nT,[f("div",oT,[t[35]||(t[35]=f("label",{class:"form-label"},"Nome da turma",-1)),t[36]||(t[36]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),P(h,{title:"Ajuda com nome da turma",text:"Insira um nome para a turma. Exemplo: Turma ADM 2025."})]),f("div",iT,[P(m,{modelValue:o.classData.classname,"onUpdate:modelValue":t[0]||(t[0]=A=>o.classData.classname=A),placeholder:"Digite o nome da turma",width:280,required:"",ref:"classnameInput","has-error":o.formErrors.classname.hasError,"error-message":o.formErrors.classname.message,onValidate:t[1]||(t[1]=A=>a.validateField("classname"))},null,8,["modelValue","has-error","error-message"])])]),f("div",aT,[f("div",lT,[f("div",uT,[t[37]||(t[37]=f("label",{class:"form-label"},"Data início da turma",-1)),t[38]||(t[38]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),P(h,{title:"Ajuda com data início da turma",text:"Insira uma data de início para a turma. Exemplo: 16/03/2025."})]),f("div",cT,[P(m,{modelValue:o.classData.startdate,"onUpdate:modelValue":t[2]||(t[2]=A=>o.classData.startdate=A),type:"date",width:180,required:"",class:"date-input",ref:"startdateInput","has-error":o.formErrors.startdate.hasError,"error-message":o.formErrors.startdate.message,onValidate:t[3]||(t[3]=A=>a.validateField("startdate"))},null,8,["modelValue","has-error","error-message"])])]),f("div",{class:he(["form-group",{disabled:!o.classData.optional_fields.enableenddate}])},[f("div",dT,[t[39]||(t[39]=f("label",{class:"form-label"},"Data fim da turma",-1)),t[40]||(t[40]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),P(h,{title:"Ajuda com data fim da turma",text:"Insira uma data fim para a turma. Exemplo: 16/12/2025."})]),f("div",fT,[f("div",hT,[P(m,{modelValue:o.classData.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=A=>o.classData.optional_fields.enddate=A),type:"date",width:180,disabled:!o.classData.optional_fields.enableenddate,required:"",class:"date-input","has-error":o.formErrors.enddate.hasError,"error-message":o.formErrors.enddate.message,onValidate:t[5]||(t[5]=A=>a.validateField("enddate"))},null,8,["modelValue","disabled","has-error","error-message"])]),P(p,{modelValue:o.classData.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=A=>o.classData.optional_fields.enableenddate=A),id:"enableEndDate",label:"Habilitar data fim da turma",class:"inline-checkbox",disabled:!1},null,8,["modelValue"])])],2)]),f("div",pT,[o.classData.enrol=="offer_self"?(O(),N("div",{key:0,class:he(["form-group",{disabled:!o.classData.optional_fields.enablepreenrolment}])},[f("div",mT,[t[41]||(t[41]=f("label",{class:"form-label"},"Data início pré-inscrição",-1)),P(h,{title:"Ajuda com data início pré-inscrição",text:"Data de início do período de pré-inscrição."})]),P(m,{modelValue:o.classData.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[7]||(t[7]=A=>o.classData.optional_fields.preenrolmentstartdate=A),type:"date",width:180,disabled:!o.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":o.formErrors.preenrolmentstartdate.hasError,"error-message":o.formErrors.preenrolmentstartdate.message,onValidate:t[8]||(t[8]=A=>a.validateField("preenrolmentstartdate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ae("",!0),o.classData.enrol=="offer_self"?(O(),N("div",{key:1,class:he(["form-group",{disabled:!o.classData.optional_fields.enablepreenrolment}])},[f("div",gT,[t[42]||(t[42]=f("label",{class:"form-label"},"Data fim pré-inscrição",-1)),P(h,{title:"Ajuda com data fim pré-inscrição",text:"Data de término do período de pré-inscrição."})]),P(m,{modelValue:o.classData.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[9]||(t[9]=A=>o.classData.optional_fields.preenrolmentenddate=A),type:"date",width:180,disabled:!o.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":o.formErrors.preenrolmentenddate.hasError,"error-message":o.formErrors.preenrolmentenddate.message,onValidate:t[10]||(t[10]=A=>a.validateField("preenrolmentenddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ae("",!0),o.classData.enrol=="offer_self"?(O(),N("div",_T,[t[43]||(t[43]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"}," ")],-1)),P(p,{modelValue:o.classData.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[11]||(t[11]=A=>o.classData.optional_fields.enablepreenrolment=A),id:"enablePreEnrolment",label:"Habilitar pré-inscrição",disabled:!1},null,8,["modelValue"])])):ae("",!0)]),f("div",vT,[f("div",yT,[t[44]||(t[44]=f("label",{class:"form-label"},"Descrição da turma",-1)),P(h,{title:"Ajuda com descrição da turma",text:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025."})]),f("div",bT,[P(_,{modelValue:o.classData.optional_fields.description,"onUpdate:modelValue":t[12]||(t[12]=A=>o.classData.optional_fields.description=A),placeholder:"Digite a descrição da turma aqui...",rows:5,disabled:!1},null,8,["modelValue"])])]),f("div",wT,[o.classData.enrol=="offer_self"?(O(),N("div",ET,[f("div",CT,[t[45]||(t[45]=f("label",{class:"form-label"},"Mínimo de usuários inscritos",-1)),P(h,{title:"Ajuda com mínimo de usuários",text:"Número mínimo de usuários para a turma."})]),f("div",DT,[P(m,{modelValue:o.classData.optional_fields.minusers,"onUpdate:modelValue":t[13]||(t[13]=A=>o.classData.optional_fields.minusers=A),type:"number",width:180},null,8,["modelValue"])])])):ae("",!0),o.classData.enrol=="offer_self"?(O(),N("div",xT,[f("div",ST,[t[46]||(t[46]=f("label",{class:"form-label"},"Máximo de usuários inscritos",-1)),P(h,{title:"Ajuda com máximo de usuários",text:"Número máximo de usuários para a turma."})]),f("div",OT,[P(m,{modelValue:o.classData.optional_fields.maxusers,"onUpdate:modelValue":t[14]||(t[14]=A=>o.classData.optional_fields.maxusers=A),type:"number",width:180},null,8,["modelValue"])])])):ae("",!0),f("div",TT,[f("div",NT,[t[47]||(t[47]=f("label",{class:"form-label"},"Papel atribuído por padrão",-1)),t[48]||(t[48]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),P(h,{title:"Ajuda com papel atribuído por padrão",text:"O papel atribuído será o papel que o usuário receberá dentro do curso após sua matrícula na turma. Exemplo: Estudante, Professor, Coordenador, etc…"})]),f("div",AT,[P(w,{modelValue:o.classData.optional_fields.roleid,"onUpdate:modelValue":t[15]||(t[15]=A=>o.classData.optional_fields.roleid=A),options:o.roleOptions,width:180,required:"","has-error":o.formErrors.roleid.hasError,"error-message":o.formErrors.roleid.message,onValidate:t[16]||(t[16]=A=>a.validateField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])])]),f("div",IT,[f("div",{class:he(["form-group",{disabled:!o.classData.optional_fields.enableenrolperiod}])},[f("div",MT,[t[49]||(t[49]=f("label",{class:"form-label"},"Prazo de conclusão da turma",-1)),t[50]||(t[50]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),P(h,{title:"Ajuda com prazo de conclusão da turma",text:"O prazo de conclusão refere-se a quantidade de dias que um usuário terá para realizar o curso. Após esse período, se o usuário não obtiver uma situação de êxito na matrícula (Aprovado ou Concluído) durante seu progresso no curso, o sistema encerrará sua matrícula."+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["text"])]),f("div",PT,[f("div",kT,[P(m,{modelValue:o.classData.optional_fields.enrolperiod,"onUpdate:modelValue":t[17]||(t[17]=A=>o.classData.optional_fields.enrolperiod=A),type:"number",width:180,disabled:!o.classData.optional_fields.enableenrolperiod,required:"","has-error":o.formErrors.enrolperiod.hasError,"error-message":o.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[18]||(t[18]=A=>a.validateField("enrolperiod"))},null,8,["modelValue","disabled","has-error","error-message","max"])]),mt(P(p,{modelValue:o.classData.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[19]||(t[19]=A=>o.classData.optional_fields.enableenrolperiod=A),id:"enableEnrolPeriod",label:"Habilitar prazo de conclusão",class:"inline-checkbox",disabled:a.shouldDisableEnrolPeriod},null,8,["modelValue","disabled"]),[[te,a.shouldDisableEnrolPeriod?"Não é possível habilitar prazo de conclusão para turmas de um dia (data início = data fim)":""]])])],2)])]),f("div",VT,[f("div",RT,[f("div",{class:he(["form-group",{disabled:!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[f("div",LT,[t[52]||(t[52]=f("label",{class:"form-label"},"Prorrogação de matrícula",-1)),P(h,{title:"Ajuda com prorrogação de matrícula",text:"A prorrogação estende o Prazo de conclusão do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou. Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."})]),mt(P(p,{modelValue:o.classData.optional_fields.enableextension,"onUpdate:modelValue":t[20]||(t[20]=A=>o.classData.optional_fields.enableextension=A),id:"enableExtension",label:"Habilitar Prorrogação de matrícula",disabled:!o.classData.optional_fields.enableenrolperiod},null,8,["modelValue","disabled"]),[[te,o.classData.optional_fields.enableenrolperiod?"":"É necessário habilitar o Prazo de conclusão da turma primeiro"]])],2)]),f("div",UT,[f("div",{class:he(["form-group",{disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[53]||(t[53]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantos dias serão acrescentados para prorrogação?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",FT,[P(m,{modelValue:o.classData.optional_fields.extensionperiod,"onUpdate:modelValue":t[21]||(t[21]=A=>o.classData.optional_fields.extensionperiod=A),type:"number",width:180,disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,required:"","has-error":o.formErrors.extensionperiod.hasError,"error-message":o.formErrors.extensionperiod.message,onValidate:t[22]||(t[22]=A=>a.validateField("extensionperiod"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",BT,[f("div",{class:he(["form-group",{disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[54]||(t[54]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantos dias antes do término do prazo de matrícula o botão de prorrogação deve ser exibido?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",$T,[P(m,{modelValue:o.classData.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[23]||(t[23]=A=>o.classData.optional_fields.extensiondaysavailable=A),type:"number",width:180,disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,required:"","has-error":o.formErrors.extensiondaysavailable.hasError,"error-message":o.formErrors.extensiondaysavailable.message,onValidate:t[24]||(t[24]=A=>a.validateField("extensiondaysavailable"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",jT,[f("div",{class:he(["form-group",{disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[55]||(t[55]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantas vezes o usuário pode pedir prorrogação?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",HT,[P(m,{modelValue:o.classData.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[25]||(t[25]=A=>o.classData.optional_fields.extensionmaxrequests=A),type:"number",width:180,disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,required:"","has-error":o.formErrors.extensionmaxrequests.hasError,"error-message":o.formErrors.extensionmaxrequests.message,onValidate:t[26]||(t[26]=A=>a.validateField("extensionmaxrequests"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",{class:he(["form-group mb-3",{disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[56]||(t[56]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Para quais situações de matrícula é permitida a prorrogação?")],-1)),f("div",qT,[P(D,{modelValue:o.extensionSituations,"onUpdate:modelValue":t[27]||(t[27]=A=>o.extensionSituations=A),items:a.extensionSituationList,placeholder:"Selecione as situações...",disabled:!o.classData.optional_fields.enableextension||!o.classData.optional_fields.enableenrolperiod,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllExtensionSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)]),o.classData.enrol=="offer_self"?(O(),N("div",zT,[f("div",WT,[f("div",GT,[f("div",KT,[t[57]||(t[57]=f("label",{class:"form-label"},"Habilitar rematrícula",-1)),P(h,{title:"Ajuda com habilitar rematrícula",text:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."})]),P(p,{modelValue:o.classData.optional_fields.enablereenrol,"onUpdate:modelValue":t[28]||(t[28]=A=>o.classData.optional_fields.enablereenrol=A),id:"enableReenrol",label:"Habilitar rematrícula",disabled:!1},null,8,["modelValue"])])]),f("div",{class:he(["form-group mb-3",{disabled:!o.classData.optional_fields.enablereenrol}])},[t[58]||(t[58]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quais situações de matrícula permitem rematrícula?")],-1)),f("div",YT,[P(D,{modelValue:o.reenrolSituations,"onUpdate:modelValue":t[29]||(t[29]=A=>o.reenrolSituations=A),items:a.reenrolSituationList,placeholder:"Selecione as situações...",disabled:!o.classData.optional_fields.enablereenrol,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllReenrolSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)])):ae("",!0),f("div",QT,[f("div",ZT,[f("div",JT,[t[59]||(t[59]=f("label",{class:"form-label"},"Atribuir corpo docente",-1)),P(h,{title:"Ajuda com atribuir corpo docente",text:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."})]),f("div",XT,[f("div",eN,[f("div",tN,[mt(f("input",{type:"text","onUpdate:modelValue":t[30]||(t[30]=A=>o.teacherSearchTerm=A),placeholder:"Pesquisar ...",class:"form-control custom-input",onInput:t[31]||(t[31]=(...A)=>a.handleTeacherInput&&a.handleTeacherInput(...A)),onFocus:t[32]||(t[32]=(...A)=>a.handleTeacherInputFocus&&a.handleTeacherInputFocus(...A)),onKeydown:t[33]||(t[33]=(...A)=>a.handleKeydown&&a.handleKeydown(...A)),ref:"teacherSearchInput"},null,544),[[ws,o.teacherSearchTerm]])]),o.showTeacherDropdown&&o.teacherList.length>0?(O(),N("div",sN,[(O(!0),N(Re,null,vt(o.teacherList,(A,se)=>(O(),N("div",{key:A.id,class:he(["dropdown-item",{active:o.highlightedIndex===se}]),onClick:G=>a.selectTeacher(A),onMouseenter:G=>o.highlightedIndex=se},[f("div",null,[f("div",null,Y(A.fullname),1),A.email?(O(),N("div",nN,Y(A.email),1)):ae("",!0)])],42,rN))),128))],512)):ae("",!0),o.showTeacherDropdown&&o.teacherSearchTerm.length>=3&&o.teacherList.length===0?(O(),N("div",oN,t[60]||(t[60]=[f("div",{class:"dropdown-item-text text-center fst-italic"},"Nenhum professor encontrado",-1)]))):ae("",!0)],512),f("div",iN,[(O(!0),N(Re,null,vt(o.selectedTeachers,(A,se)=>(O(),N("a",{key:A.id,class:"tag badge bg-primary text-white p-2 cursor-pointer mr-2",onClick:G=>a.removeTeacher(A.id)},[t[61]||(t[61]=f("i",{class:"fas fa-times mr-1"},null,-1)),nt(" "+Y(A.fullname),1)],8,aN))),128))])])])]),t[63]||(t[63]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[nt(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",lN,[P(k,{variant:"primary",label:"Salvar",loading:o.loading,onClick:a.saveClass},null,8,["loading","onClick"]),P(k,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])]),o.loading?(O(),N("div",uN,t[62]||(t[62]=[f("div",{class:"spinner-border",role:"status"},[f("span",{class:"sr-only"},"Carregando...")],-1)]))):ae("",!0),P(F,{show:o.showToast,message:o.toastMessage,type:o.toastType,duration:3e3},null,8,["show","message","type"])],512)}const up=He(XO,[["render",cN],["__scopeId","data-v-64469837"]]),dN=[{path:"/",name:"listar-ofertas",component:Qw,meta:{title:"Gerenciar Ofertas"}},{path:"/new-offer",name:"nova-oferta",component:lp,meta:{title:"Nova Oferta"}},{path:"/edit-offer/:id",name:"editar-oferta",component:lp,props:!0,meta:{title:"Editar Oferta"}},{path:"/new-class/:offercourseid",name:"NewClass",component:up,props:!0,meta:{title:"Nova Turma"}},{path:"/edit-class/:offercourseid/:classid",name:"EditClass",component:up,props:!0,meta:{title:"Editar Turma"}},{path:"/new-subscribed-users/:offerclassid",name:"usuarios-matriculados",component:Px,props:!0,meta:{title:"Usuários matriculados"}},{path:"/:pathMatch(.*)*",redirect:"/"}],jo=G1({history:n1("/local/offermanager/"),routes:dN,scrollBehavior(){return{top:0}}});jo.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),jo.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&jo.push("/")});const VR="",fN=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{fN();const s=Wy(Tb);if(s.use(Db()),s.use(jo),t&&t.route){let o={"new-offer":"/new-offer","edit-offer":"/edit-offer","new-subscribed-users":"/new-subscribed-users","new-class":"/new-class","edit-class":"/edit-class"}[t.route]||"/";t.route==="edit-offer"&&t.offerId&&(o=`/edit-offer/${t.offerId}`),t.route==="new-subscribed-users"&&t.subscribeId&&(o=`/new-subscribed-users/${t.subscribeId}`),t.route==="new-class"&&t.offercourseid&&(o=`/new-class/${t.offercourseid}`),t.route==="edit-class"&&t.offercourseid&&t.classid&&(o=`/edit-class/${t.offercourseid}/${t.classid}`),jo.replace(o)}return s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
